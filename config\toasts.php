<?php
// config/toasts.php

return [
    /*
    |--------------------------------------------------------------------------
    | Toast Notification System Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration controls all aspects of the toast notification system
    | including animations, behavior, display settings, and notification types.
    | All durations are in milliseconds unless otherwise specified.
    |
    | Version: 2.0.0
    */

    /* Animation Settings */
    'animations' => [
        /*
        |--------------------------------------------------------------------------
        | Default Animation Preset
        |--------------------------------------------------------------------------
        |
        | The default animation preset to use when none is specified for a toast type.
        | Must match one of the keys in the 'presets' array below.
        */
        'preset' => 'slide_from_bottom',

        /*
        |--------------------------------------------------------------------------
        | Animation Presets
        |--------------------------------------------------------------------------
        |
        | Predefined animation configurations that can be referenced by name.
        | Each preset defines enter/leave transitions with Tailwind CSS classes.
        */
        'presets' => [
            'slide_from_bottom' => [
                'enter' => [
                    'transition_classes' => 'transition',
                    'easing' => 'ease-out',
                    'duration' => 300,
                    'from' => 'opacity-0 translate-y-2 sm:translate-y-0 sm:translate-x-2',
                    'to' => 'opacity-100 translate-y-0 sm:translate-x-0',
                ],
                'leave' => [
                    'transition_classes' => 'transition',
                    'easing' => 'ease-in',
                    'duration' => 200,
                    'from' => 'opacity-100',
                    'to' => 'opacity-0',
                ],
                'delay' => 0,
                'hooks' => [
                    'onEnterStart' => null,
                    'onEnterEnd' => null,
                    'onLeaveStart' => null,
                    'onLeaveEnd' => null,
                ],
            ],
            'slide_from_right' => [
                'enter' => [
                    'transition_classes' => 'transition',
                    'easing' => 'ease-out',
                    'duration' => 300,
                    'from' => 'opacity-0 translate-x-full',
                    'to' => 'opacity-100 translate-x-0',
                ],
                'leave' => [
                    'transition_classes' => 'transition',
                    'easing' => 'ease-in',
                    'duration' => 200,
                    'from' => 'opacity-100 translate-x-0',
                    'to' => 'opacity-0 translate-x-full',
                ],
                'delay' => 0,
                'hooks' => [],
            ],
            'fade' => [
                'enter' => [
                    'transition_classes' => 'transition',
                    'easing' => 'ease-out',
                    'duration' => 300,
                    'from' => 'opacity-0',
                    'to' => 'opacity-100',
                ],
                'leave' => [
                    'transition_classes' => 'transition',
                    'easing' => 'ease-in',
                    'duration' => 200,
                    'from' => 'opacity-100',
                    'to' => 'opacity-0',
                ],
                'delay' => 0,
                'hooks' => [],
            ],
            'scale_fade' => [
                'enter' => [
                    'transition_classes' => 'transition transform',
                    'easing' => 'ease-out',
                    'duration' => 300,
                    'from' => 'opacity-0 scale-95',
                    'to' => 'opacity-100 scale-100',
                ],
                'leave' => [
                    'transition_classes' => 'transition transform',
                    'easing' => 'ease-in',
                    'duration' => 200,
                    'from' => 'opacity-100 scale-100',
                    'to' => 'opacity-0 scale-95',
                ],
                'delay' => 0,
                'hooks' => [],
                'transform_origin' => 'bottom right',
            ],
            'none' => [
                'enter' => [
                    'transition_classes' => '',
                    'easing' => '',
                    'duration' => 0,
                    'from' => '',
                    'to' => '',
                ],
                'leave' => [
                    'transition_classes' => '',
                    'easing' => '',
                    'duration' => 0,
                    'from' => '',
                    'to' => '',
                ],
                'delay' => 0,
                'hooks' => [],
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | Global Animation Settings
        |--------------------------------------------------------------------------
        |
        | Default values applied to all animations unless overridden by presets.
        */
        'global' => [
            'default_enter_duration' => 300,
            'default_leave_duration' => 200,
            'default_easing' => 'ease-out',
            'default_transition_classes' => 'transition',
            'enable_js_hooks' => false,
        ],
    ],

    /* Behavior Settings */
    'behavior' => [
        /*
        |--------------------------------------------------------------------------
        | Auto-Dismiss Behavior
        |--------------------------------------------------------------------------
        */
        'auto_dismiss' => [
            'enabled' => true,
            'default_duration' => 5000,
            'pause_on_hover' => true,
            'pause_on_window_blur' => true,
        ],

        /*
        |--------------------------------------------------------------------------
        | Toast Stacking
        |--------------------------------------------------------------------------
        */
        'stacking' => [
            'reverse_order' => false,
            'max_visible' => 5,
            'z_index' => 1050,
        ],

        /*
        |--------------------------------------------------------------------------
        | Accessibility
        |--------------------------------------------------------------------------
        */
        'accessibility' => [
            'aria_live' => 'polite',
            'focus_management' => true,
            'close_button_label' => 'Close notification',
        ],

        /*
        |--------------------------------------------------------------------------
        | Interaction
        |--------------------------------------------------------------------------
        */
        'interaction' => [
            'swipe_to_dismiss' => true,
            'clear_on_navigate' => true,
            'queue_mode' => 'fifo', // 'fifo' or 'lifo'
        ],

        /*
        |--------------------------------------------------------------------------
        | Duplicate Prevention
        |--------------------------------------------------------------------------
        */
        'duplicates' => [
            'prevent' => false,
            'time_threshold' => 1000,
            'content_check' => true,
        ],
    ],

    /* Close Button Settings */
    'close_button' => [
        'enabled' => true,
        'element' => 'button',
        'aria_label' => 'Close notification',
        'classes' => [
            'base' => 'flex items-center justify-center p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-current focus:ring-opacity-50',
            'size' => 'h-5 w-5',
            'color' => 'text-white',
            'hover' => 'hover:bg-black/10 hover:text-white/80',
            'position' => 'absolute top-2 right-2',
            'transition' => 'transition duration-150 ease-in-out',
        ],
        'icon' => '<svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" /></svg>',
    ],

    /* Display Settings */
    'display' => [
        'position' => [
            'desktop' => 'top-right',
            'mobile' => 'bottom-right',
        ],
        'sizing' => [
            'max_width' => 'max-w-md',
            'mobile_max_width' => 'max-w-xs',
        ],
    ],

    /* Progress Bar Settings */
    'progress_bar' => [
        'enabled' => true,
        'classes' => [
            'base' => 'overflow-hidden',
            'height' => 'h-1',
            'width' => [
                'default' => 'w-full',
                'mobile' => 'w-[95%]',
            ],
            'background' => [
                'light' => 'bg-black/10',
                'dark' => 'bg-white/10',
            ],
            'transition' => [
                'property' => 'width',
                'timing' => 'ease-linear',
                'duration' => 'duration-[--toast-duration]',
            ],
        ],
        'type_overrides' => [
            'success' => ['foreground' => 'bg-green-400/90 dark:bg-green-500/80'],
            'error' => ['foreground' => 'bg-red-400/90 dark:bg-red-500/80', 'height' => 'h-[2px]'],
            'warning' => ['foreground' => 'bg-yellow-400 dark:bg-yellow-500'],
            'info' => ['foreground' => 'bg-blue-500 dark:bg-blue-400'],
            'custom_notification' => ['foreground' => 'bg-purple-400 dark:bg-purple-500'],
            'default' => ['foreground' => 'bg-gray-500 dark:bg-gray-400'],
        ],
    ],

    /* Toast Types Configuration */
    'types' => [
        'defaults' => [
            'duration' => null,
            'text_color' => 'text-white',
            'show_progress' => true,
            'dismissible' => true,
            'position' => null,
            'layout_preset' => 'default',
            'aria_role' => 'status',
            'close_button' => true,
            'sound' => [
                'src' => null,
                'volume' => null,
                'loop' => null,
            ],
            'priority' => 'low',
            'animation_preset' => null,
        ],

        'layouts' => [
            'default' => [
                'wrapper_classes' => 'p-4 rounded-lg shadow-lg flex items-center space-x-4',
                'icon_wrapper_classes' => 'flex-shrink-0',
                'content_wrapper_classes' => 'flex-grow',
                'close_button_classes' => 'ml-auto text-current hover:opacity-75',
                'progress_bar_classes' => 'h-1 rounded-b-lg absolute bottom-0 left-0 right-0',
            ],
            'with_actions' => [
                'wrapper_classes' => 'p-4 rounded-lg shadow-lg flex flex-col space-y-2',
                'icon_wrapper_classes' => 'flex-shrink-0',
                'content_wrapper_classes' => 'flex-grow',
                'close_button_classes' => 'ml-auto text-current hover:opacity-75',
                'progress_bar_classes' => 'h-1 rounded-b-lg absolute bottom-0 left-0 right-0',
                'action_container_classes' => 'flex justify-end space-x-2 mt-2',
            ],
        ],

        'definitions' => [
            'success' => [
                'bg' => 'bg-green-600 dark:bg-green-700',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
                'duration' => 3000,
                'text_color' => 'text-white',
                'aria_role' => 'status',
                'progress_bar' => [
                    'height' => 'h-1',
                ],
                'sound' => ['src' => 'success'],
                'priority' => 'low',
            ],

            'error' => [
                'bg' => 'bg-red-600 dark:bg-red-700',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
                'duration' => 6000,
                'text_color' => 'text-white',
                'show_progress' => true,
                'aria_role' => 'alert',
                'progress_bar' => [],
                'sound' => ['src' => 'error', 'volume' => 0.8],
                'actions' => [
                    [
                        'label' => 'Retry',
                        'handler' => 'retryErrorHandler',
                        'classes' => 'text-white underline hover:opacity-75',
                    ],
                ],
                'priority' => 'high',
            ],

            'warning' => [
                'bg' => 'bg-yellow-500 dark:bg-yellow-600',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.174 3.374 1.945 3.374h14.71c1.771 0 2.812-1.874 1.945-3.374L13.94 2.332c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>',
                'duration' => 4000,
                'text_color' => 'text-gray-800 dark:text-white',
                'progress_bar' => [],
                'dismissible' => true,
                'sound' => ['src' => 'warning'],
                'priority' => 'normal',
            ],

            'info' => [
                'bg' => 'bg-blue-600 dark:bg-blue-700',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" /></svg>',
                'duration' => 4000,
                'text_color' => 'text-white',
                'dismissible' => true,
                'progress_bar' => [],
                'sound' => ['src' => 'info'],
                'priority' => 'normal',
            ],

            'default' => [
                'bg' => 'bg-gray-800 dark:bg-gray-900',
                'icon' => '',
                'duration' => 3000,
                'text_color' => 'text-white',
                'show_progress' => false,
                'dismissible' => true,
                'progress_bar' => ['bg' => 'bg-gray-700 dark:bg-gray-600'],
                'priority' => 'low',
            ],

            'custom_notification' => [
                'bg' => 'bg-purple-600 dark:bg-purple-800',
                'icon' => '<svg class="h-6 w-6 text-purple-100" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.04 5.455 1.31m5.714 0a24.248 24.248 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" /></svg>',
                'text_color' => 'text-purple-100',
                'duration' => 7000,
                'show_progress' => true,
                'dismissible' => false,
                'layout_preset' => 'with_actions',
                'progress_bar' => [],
                'actions' => [
                    [
                        'label' => 'View Details',
                        'handler' => 'viewCustomDetails',
                        'classes' => 'text-purple-100 underline hover:opacity-75',
                    ],
                    [
                        'label' => 'Dismiss',
                        'handler' => 'dismissToast',
                        'classes' => 'text-purple-100 font-bold hover:text-purple-50',
                    ],
                ],
                'priority' => 'normal',
            ],

            'critical' => [
                'bg' => 'bg-red-800 dark:bg-red-900',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.174 3.374 1.945 3.374h14.71c1.771 0 2.812-1.874 1.945-3.374L13.94 2.332c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>',
                'duration' => 0,
                'text_color' => 'text-white',
                'show_progress' => false,
                'dismissible' => false,
                'priority' => 'high',
                'sound' => ['src' => 'error', 'volume' => 1.0, 'loop' => true],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Advanced Configuration
    |--------------------------------------------------------------------------
    */
    'advanced' => [
        'dom_pooling' => [
            'enabled' => true,
            'pool_size' => 5,
        ],
        'persistence' => [
            'enabled' => false,
            'driver' => 'localStorage',
            'key' => 'toast_queue',
        ],
    ],
];
