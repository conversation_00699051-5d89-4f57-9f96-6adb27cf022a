@echo off
echo Creating backup of registration module...
echo -----------------------------------
echo.

rem Create timestamp for the backup folder
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c-%%a-%%b)
for /f "tokens=1-2 delims=: " %%a in ('time /t') do (set mytime=%%a%%b)
set timestamp=%mydate%_%mytime%

rem Create main backup directory with timestamp
echo Creating main backup directory with timestamp: %timestamp%
mkdir "backup\registration_%timestamp%" 2>nul
set BACKUP_DIR=backup\registration_%timestamp%
echo.

rem Create directory structure
echo Creating directory structure...
mkdir "%BACKUP_DIR%\app\Livewire\Registration" 2>nul
mkdir "%BACKUP_DIR%\app\Livewire\Registration\Steps" 2>nul
mkdir "%BACKUP_DIR%\app\Models\Auth" 2>nul
mkdir "%BACKUP_DIR%\app\Services" 2>nul
mkdir "%BACKUP_DIR%\resources\views\livewire\registration" 2>nul
mkdir "%BACKUP_DIR%\resources\views\livewire\registration\steps" 2>nul
mkdir "%BACKUP_DIR%\routes" 2>nul
mkdir "%BACKUP_DIR%\database\migrations" 2>nul
echo Directory structure created successfully.
echo.

rem Backup Livewire Registration components
echo Backing up Livewire Registration components...
if exist app\Livewire\Registration\RegistrationComplete.php copy app\Livewire\Registration\RegistrationComplete.php "%BACKUP_DIR%\app\Livewire\Registration\" /Y
if exist app\Livewire\Registration\RegistrationWizard.php copy app\Livewire\Registration\RegistrationWizard.php "%BACKUP_DIR%\app\Livewire\Registration\" /Y
echo.

rem Backup Registration Steps components
echo Backing up Registration Steps components...
if exist app\Livewire\Registration\Steps\AdditionalDocuments.php copy app\Livewire\Registration\Steps\AdditionalDocuments.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\BusinessInformation.php copy app\Livewire\Registration\Steps\BusinessInformation.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\BusinessKycDocuments.php copy app\Livewire\Registration\Steps\BusinessKycDocuments.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\EmailVerification.php copy app\Livewire\Registration\Steps\EmailVerification.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\EntityTypeSelection.php copy app\Livewire\Registration\Steps\EntityTypeSelection.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\PersonalDetails.php copy app\Livewire\Registration\Steps\PersonalDetails.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\PersonalKycDocuments.php copy app\Livewire\Registration\Steps\PersonalKycDocuments.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\ReviewSubmit.php copy app\Livewire\Registration\Steps\ReviewSubmit.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
if exist app\Livewire\Registration\Steps\TaxInformation.php copy app\Livewire\Registration\Steps\TaxInformation.php "%BACKUP_DIR%\app\Livewire\Registration\Steps\" /Y
echo.

rem Backup Auth models
echo Backing up Auth models...
if exist app\Models\Auth\RegistrationAttempt.php copy app\Models\Auth\RegistrationAttempt.php "%BACKUP_DIR%\app\Models\Auth\" /Y
echo.

rem Backup Services
echo Backing up Registration Service...
if exist app\Services\RegistrationService.php copy app\Services\RegistrationService.php "%BACKUP_DIR%\app\Services\" /Y
echo.

rem Backup Registration views
echo Backing up Registration views...
if exist resources\views\livewire\registration\registration-complete.blade.php copy resources\views\livewire\registration\registration-complete.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\" /Y
if exist resources\views\livewire\registration\registration-wizard.blade.php copy resources\views\livewire\registration\registration-wizard.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\" /Y
echo.

rem Backup Registration step views
echo Backing up Registration step views...
if exist resources\views\livewire\registration\steps\additional-documents.blade.php copy resources\views\livewire\registration\steps\additional-documents.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\business-information.blade.php copy resources\views\livewire\registration\steps\business-information.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\business-kyc-documents.blade.php copy resources\views\livewire\registration\steps\business-kyc-documents.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\email-verification.blade.php copy resources\views\livewire\registration\steps\email-verification.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\entity-type-selection.blade.php copy resources\views\livewire\registration\steps\entity-type-selection.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\personal-details.blade.php copy resources\views\livewire\registration\steps\personal-details.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\personal-kyc-documents.blade.php copy resources\views\livewire\registration\steps\personal-kyc-documents.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\review-submit.blade.php copy resources\views\livewire\registration\steps\review-submit.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
if exist resources\views\livewire\registration\steps\tax-information.blade.php copy resources\views\livewire\registration\steps\tax-information.blade.php "%BACKUP_DIR%\resources\views\livewire\registration\steps\" /Y
echo.

rem Backup Routes
echo Backing up Routes...
if exist routes\entity-registration.php copy routes\entity-registration.php "%BACKUP_DIR%\routes\" /Y
echo.

rem Backup Migrations
echo Backing up Migrations...
if exist database\migrations\2025_07_12_112811_add_multi_stage_fields_to_registration_attempts_table.php copy database\migrations\2025_07_12_112811_add_multi_stage_fields_to_registration_attempts_table.php "%BACKUP_DIR%\database\migrations\" /Y
if exist database\migrations\2025_07_13_add_personal_kyc_documents_to_registration_attempts.php copy database\migrations\2025_07_13_add_personal_kyc_documents_to_registration_attempts.php "%BACKUP_DIR%\database\migrations\" /Y
echo.

rem Count files copied
set /a total_files=0
for /r "%BACKUP_DIR%" %%F in (*) do set /a total_files+=1

echo -----------------------------------
echo Backup completed successfully!
echo Total files backed up: %total_files%
echo All registration module files have been copied to %BACKUP_DIR%
echo -----------------------------------
echo.

pause 