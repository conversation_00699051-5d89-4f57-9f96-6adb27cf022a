<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // 1. System Roles Table
        Schema::create('system_roles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->string('role_name')->comment('Human-readable role name (e.g., "Admin")');
            $table->text('description')->nullable()->comment('Detailed role description');
            $table->foreignId('parent_role_id')->nullable()->constrained('system_roles')->onDelete('set null')->comment('Hierarchical parent role');
            $table->unsignedTinyInteger('hierarchy_level')->default(1)->comment('Level in role hierarchy (1=top)');
            $table->string('guard_type')->default('web')->comment('Type of access guard (web, api, etc)');
            $table->date('active_from')->nullable()->comment('Date when role becomes active');
            $table->date('active_until')->nullable()->comment('Date when role expires (null = permanent)');
            $table->boolean('is_active')->default(true)->comment('Soft delete flag');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->text('notes')->nullable()->comment('Administrative notes');
            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->index(['hierarchy_level', 'is_active']);
            $table->index('entity_id');
            $table->unique(['entity_id', 'role_name'], 'unique_entity_role');
        });

        // 2. Guard Types Table
        Schema::create('access_guard_types', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->string('guard_name')->unique()->comment('Guard identifier (web, api, mobile)');
            $table->text('description')->nullable()->comment('Guard purpose description');
            $table->boolean('is_active')->default(true)->comment('Activation status');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->index('entity_id');
        });

        // 3. Organization Departments Table
        Schema::create('organization_departments', function (Blueprint $table) {
            $table->id();

            // Core Department Information
            $table->string('dept_name')->unique()->comment('Full, official name of the department (e.g., Sales & Business Development)');
            $table->string('dept_code', 20)->unique()->nullable()->comment('Short, unique alphanumeric code for the department (e.g., SALES, BD01)'); // Increased length for flexibility
            $table->text('description')->nullable()->comment('Detailed description of the department\'s functions and responsibilities.'); // New field for detailed description
            $table->string('email', 100)->nullable()->comment('Primary email address for the department'); // New field for contact
            $table->string('phone', 20)->nullable()->comment('Primary phone number for the department'); // New field for contact

            // Hierarchical Structure (Optional but highly recommended for organizations)
            $table->unsignedBigInteger('parent_id')->nullable()->comment('ID of the parent department, if it\'s a sub-department'); // For hierarchical structures
            $table->foreign('parent_id')->references('id')->on('organization_departments')->onDelete('set null'); // Foreign key to itself

            // Operational Status
            $table->boolean('is_active')->default(true)->comment('Activation status of the department (active/inactive)');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->integer('display_order')->default(0)->comment('Order for displaying departments in lists/charts'); // New field for sorting


            // Example if needed: $table->foreignId('organization_id')->nullable()->constrained()->comment('Links to the main organization/company');
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier (e.g., ENT-001)');
            // Audit fields (Kept and slightly re-ordered for common convention)
            $table->unsignedBigInteger('created_by')->nullable()->comment('User ID who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last updated this record');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who soft deleted this record');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored this record'); // Usually handled implicitly by softDeletes restoration
            // Custom restoration timestamp (Important for explicit tracking)
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when record was restored from soft-delete'); // Added back

            // Timestamps
            $table->timestamps(); // created_at and updated_at
            $table->softDeletes(); // deleted_at (Laravel's soft delete handles restoration timestamp implicitly)

            // Indexes (Added new ones and refined existing)
            $table->index('dept_name'); // Index on name for faster lookups
            $table->index('dept_code');
            $table->index('is_active');
            $table->index('parent_id'); // Important for hierarchical queries
            // Add full-text search index if description is frequently searched
            // $table->fullText('description'); // Requires specific database configurations (e.g., MySQL InnoDB)
        });

        // 4. System Permissions Table
        Schema::create('system_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->string('permission_code')->unique()->comment('Machine-readable permission code');
            $table->string('permission_name')->comment('Human-readable permission name');
            $table->string('permission_type')->comment('Type of permission (e.g., "create", "read", "update", "delete")');
            $table->text('description')->comment('Human-readable permission description');
            $table->boolean('is_active')->default(true)->comment('Activation status');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->index('entity_id');
        });

        // 5. Permission Groups Table
        Schema::create('permission_groups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->string('group_name')->unique()->comment('Permission bundle name');
            $table->json('permission_ids')->nullable()->comment('Array of permission IDs in this group');
            $table->date('active_from')->nullable()->comment('Group activation date');
            $table->date('active_until')->nullable()->comment('Group expiration date');
            $table->boolean('is_active')->default(true)->comment('Activation status');
            $table->text('notes')->nullable()->comment('Administrative notes');
            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->index('entity_id');
        });

        // 6. User Role Assignments Table
        Schema::create('user_role_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->foreignId('user_id')->constrained()->cascadeOnDelete()->comment('Associated user');
            $table->foreignId('role_id')->constrained('system_roles')->cascadeOnDelete()->comment('Assigned role');
            $table->date('assigned_from')->nullable()->comment('Assignment start date');
            $table->date('assigned_until')->nullable()->comment('Assignment end date');
            $table->boolean('is_active')->default(true)->comment('Active status');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->text('assignment_notes')->nullable()->comment('Assignment-specific notes');
            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->unique(['user_id', 'role_id', 'entity_id'], 'user_role_entity_unique')->comment('Prevent duplicate assignments');
            $table->index(['assigned_from', 'assigned_until']);
            $table->index('entity_id');
        });

        // 7. User Guard Access Table
        Schema::create('user_guard_access', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->foreignId('user_id')->constrained()->cascadeOnDelete()->comment('Associated user');
            $table->foreignId('guard_id')->constrained('access_guard_types')->cascadeOnDelete()->comment('Guard type');
            $table->date('access_from')->nullable()->comment('Access start date');
            $table->date('access_until')->nullable()->comment('Access end date');
            $table->boolean('is_active')->default(true)->comment('Activation status');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->text('access_notes')->nullable()->comment('Access-specific notes');
            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->unique(['user_id', 'guard_id', 'entity_id'], 'user_guard_entity_unique')->comment('Prevent duplicate access');
            $table->index('entity_id');
        });

        // 8. User Department Assignments Table
        Schema::create('user_department_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->unsignedBigInteger('created_by')->nullable();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete()->comment('Associated user');
            $table->foreignId('department_id')->constrained('organization_departments')->cascadeOnDelete()->comment('Department');
            $table->date('assigned_from')->nullable()->comment('Assignment start date');
            $table->date('assigned_until')->nullable()->comment('Assignment end date');
            $table->boolean('is_active')->default(true)->comment('Activation status');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->text('assignment_notes')->nullable()->comment('Assignment notes');
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedBigInteger('deleted_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('restored_by')->nullable();
            $table->timestamp('restored_at')->nullable();

            $table->index(['user_id', 'department_id']);
            $table->index('entity_id');
        });

        // 9. Permission Grants Table
        Schema::create('permission_grants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Associated entity identifier');
            $table->foreignId('permission_id')->constrained('system_permissions')->cascadeOnDelete()->comment('Permission being granted');
            $table->foreignId('role_id')->nullable()->constrained('system_roles')->cascadeOnDelete()->comment('Optional role grant');
            $table->foreignId('guard_id')->nullable()->constrained('access_guard_types')->cascadeOnDelete()->comment('Optional guard restriction');
            $table->foreignId('department_id')->nullable()->constrained('organization_departments')->cascadeOnDelete()->comment('Optional department scope');
            //            $table->foreignId('menu_id')->nullable()->constrained('menus')->cascadeOnDelete()->comment('Optional menu item');
            $table->date('granted_from')->nullable()->comment('Grant start date');
            $table->date('granted_until')->nullable()->comment('Grant expiration date');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            $table->boolean('is_active')->default(true)->comment('Activation status');
            $table->text('grant_notes')->nullable()->comment('Grant administration notes');
            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            // Composite indexes
            $table->index(['permission_id', 'role_id', 'department_id']);
            $table->index(['guard_id', 'is_active']);
            $table->index('entity_id');
        });
    }

    public function down()
    {
        // Drop tables in reverse order to respect foreign key constraints
        Schema::dropIfExists('permission_grants');
        Schema::dropIfExists('user_department_assignments');
        Schema::dropIfExists('user_guard_access');
        Schema::dropIfExists('user_role_assignments');
        Schema::dropIfExists('permission_groups');
        Schema::dropIfExists('system_permissions');
        Schema::dropIfExists('organization_departments');
        Schema::dropIfExists('access_guard_types');
        Schema::dropIfExists('system_roles');
    }
};
