<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Registration\RegistrationWizard;
use App\Livewire\Registration\RegistrationComplete;

/*
|--------------------------------------------------------------------------
| Entity Registration Routes
|--------------------------------------------------------------------------
|
| Routes for dealer/distributor registration using the multi-stage wizard
|
*/

// Registration routes
Route::middleware('web')->group(function () {
    // Main registration wizard with optional step parameter
    Route::get('/register/entity/{step?}', RegistrationWizard::class)
        ->where('step', '[1-9]')
        ->name('entity.register')
        ->middleware('guest'); // Add guest middleware to ensure proper layout
    
    // Resume registration with token
    Route::get('/register/entity/resume/{token}', function ($token) {
        $attempt = \App\Models\Auth\RegistrationAttempt::where('resume_token', $token)
            ->where('is_submitted', false)
            ->where('expires_at', '>', now())
            ->first();

        if ($attempt) {
            session(['registration_attempt_id' => $attempt->id]);
            return redirect()->route('entity.register');
        }

        return redirect()->route('entity.register')->with('error', 'Invalid or expired registration link.');
    })->name('entity.register.resume')->middleware('guest'); // Add guest middleware

    // Registration complete page - Using Livewire component with proper layout
    Route::get('/register/entity/complete', RegistrationComplete::class)
        ->name('entity.register.complete')
        ->middleware('guest'); // Add guest middleware
}); 