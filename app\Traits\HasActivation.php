<?php
// app/Traits/HasActivation.php
namespace App\Traits;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Schema;
use App\Models\User;

/**
 * Trait HasActivation
 *
 * Provides activation/inactivation functionality for models.
 * Used by models that can be activated or inactivated by users.
 *
 * Required database columns:
 * - is_active (boolean)
 * - inactivated_by (int, nullable)
 * - inactivated_at (timestamp, nullable)
 *
 * Optional database columns (for full activation tracking):
 * - activated_by (int, nullable)
 * - activated_at (timestamp, nullable)
 *
 * @package App\Traits
 */
trait HasActivation
{
    /**
     * Boot the trait.
     */
    public static function bootHasActivation()
    {
        // You can add any boot logic here if needed
    }

    /**
     * Activate the model.
     *
     * @param int|null $userId The user performing the activation
     * @return bool
     */
    public function activate(?int $userId = null): bool
    {
        $this->is_active = true;

        // Only set activated_by and activated_at if these columns exist
        if ($this->hasActivatedByColumns()) {
            $this->activated_by = $userId ?? auth()->id();
            $this->activated_at = now();
        }

        // Clear inactivation data
        $this->inactivated_by = null;
        $this->inactivated_at = null;

        return $this->save();
    }

    /**
     * Inactivate the model.
     *
     * @param int|null $userId The user performing the inactivation
     * @return bool
     */
    public function inactivate(?int $userId = null): bool
    {
        $this->is_active = false;
        $this->inactivated_by = $userId ?? auth()->id();
        $this->inactivated_at = now();

        return $this->save();
    }

    /**
     * Toggle activation status.
     *
     * @param int|null $userId The user performing the action
     * @return bool
     */
    public function toggleActivation(?int $userId = null): bool
    {
        return $this->is_active
            ? $this->inactivate($userId)
            : $this->activate($userId);
    }

    /**
     * Check if the model is active (basic check).
     * Override this method in your model for business-specific logic.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->is_active === true;
    }

    /**
     * Check if the model is inactive.
     *
     * @return bool
     */
    public function isInactive(): bool
    {
        return $this->is_active === false;
    }

    /**
     * Get the user who activated this record.
     * Only available for models with activated_by column.
     *
     * @return BelongsTo|null
     */
    public function activatedBy(): ?BelongsTo
    {
        if (!$this->hasActivatedByColumns()) {
            return null;
        }

        return $this->belongsTo(User::class, 'activated_by');
    }

    /**
     * Get the user who inactivated this record.
     *
     * @return BelongsTo
     */
    public function inactivatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inactivated_by');
    }

    /**
     * Get activation status text.
     *
     * @return string
     */
    public function getActivationStatusAttribute(): string
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Get activation status with details.
     *
     * @return array
     */
    public function getActivationDetailsAttribute(): array
    {
        $details = [
            'status' => $this->activation_status,
            'is_active' => $this->is_active,
        ];

        if ($this->hasActivatedByColumns() && $this->is_active && $this->activated_at) {
            $details['activated'] = [
                'at' => $this->activated_at,
                'by' => $this->activatedBy?->name,
                'by_id' => $this->activated_by,
            ];
        }

        if (!$this->is_active && $this->inactivated_at) {
            $details['inactivated'] = [
                'at' => $this->inactivated_at,
                'by' => $this->inactivatedBy?->name,
                'by_id' => $this->inactivated_by,
            ];
        }

        return $details;
    }

    /**
     * Get the duration since activation.
     *
     * @return \Carbon\CarbonInterval|null
     */
    public function getActiveDurationAttribute()
    {
        if (!$this->hasActivatedByColumns() || !$this->is_active || !$this->activated_at) {
            return null;
        }

        return $this->activated_at->diffAsCarbonInterval();
    }

    /**
     * Get the duration since inactivation.
     *
     * @return \Carbon\CarbonInterval|null
     */
    public function getInactiveDurationAttribute()
    {
        if ($this->is_active || !$this->inactivated_at) {
            return null;
        }

        return $this->inactivated_at->diffAsCarbonInterval();
    }

    /**
     * Scope to only active records.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to only inactive records.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope to records activated by a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActivatedBy($query, int $userId)
    {
        if (!$this->hasActivatedByColumns()) {
            return $query;
        }

        return $query->where('activated_by', $userId);
    }

    /**
     * Scope to records inactivated by a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInactivatedBy($query, int $userId)
    {
        return $query->where('inactivated_by', $userId);
    }

    /**
     * Scope to recently activated records.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecentlyActivated($query, int $days = 7)
    {
        if (!$this->hasActivatedByColumns()) {
            return $query->active();
        }

        return $query->active()
            ->where('activated_at', '>', now()->subDays($days));
    }

    /**
     * Scope to recently inactivated records.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $days
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecentlyInactivated($query, int $days = 7)
    {
        return $query->inactive()
            ->where('inactivated_at', '>', now()->subDays($days));
    }

    /**
     * Check if the model has activated_by columns.
     *
     * FIXED: Now properly checks if columns exist instead of hardcoding table names
     *
     * @return bool
     */
    protected function hasActivatedByColumns(): bool
    {
        // Method 1: Check if columns are in fillable array
        $fillable = $this->getFillable();
        $hasInFillable = in_array('activated_by', $fillable) && in_array('activated_at', $fillable);

        if ($hasInFillable) {
            return true;
        }

        // Method 2: Check database schema (more reliable but slightly slower)
        try {
            return Schema::hasColumn($this->getTable(), 'activated_by') &&
                Schema::hasColumn($this->getTable(), 'activated_at');
        } catch (\Exception $e) {
            // Fallback: if schema check fails, assume no columns
            return false;
        }
    }
}

/**
 * UPDATED USAGE EXAMPLES:
 *
 * // For Entity (without activated_by columns)
 * class Entity extends Model
 * {
 *     use HasActivation;
 *
 *     // Override for business logic
 *     public function isOperational(): bool
 *     {
 *         return $this->isActive() && $this->approval_status === 'approved';
 *     }
 * }
 *
 * // For User (with activated_by columns)
 * class User extends Authenticatable
 * {
 *     use HasActivation;
 *
 *     // Override for business logic
 *     public function canLogin(): bool
 *     {
 *         return $this->isActive() &&
 *                $this->approval_status === 'approved' &&
 *                $this->email_verified_at !== null;
 *     }
 * }
 */
