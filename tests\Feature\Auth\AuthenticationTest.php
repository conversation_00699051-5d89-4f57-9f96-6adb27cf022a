<?php

use App\Livewire\Auth\Login;
use App\Models\Auth\LoginHistory;
use App\Models\User;
use App\Services\LoginTrackingService;
use App\Services\LoginValidationService;
use Livewire\Livewire;
use Mo<PERSON>y\MockInterface;

test('login screen can be rendered', function () {
    $response = $this->get('/login');

    $response->assertStatus(200);
});

test('users can authenticate using the login screen', function () {
    $user = User::factory()->create([
        'email_verified_at' => now(),
        'is_active' => true,
    ]);

    // Mock the login tracking service
    $loginTracker = $this->mock(LoginTrackingService::class, function (MockInterface $mock) use ($user) {
        $mock->shouldReceive('recordSuccessfulLogin')
            ->once()
            ->andReturn(new LoginHistory([
                'user_id' => $user->id,
                'login_successful' => true,
            ]));
    });

    // Mock the login validation service
    $loginValidator = $this->mock(LoginValidationService::class, function (MockInterface $mock) {
        $mock->shouldReceive('validateLogin')
            ->once()
            ->andReturn(true);
        $mock->shouldReceive('isSuperAdmin')
            ->once()
            ->andReturn(false);
        $mock->shouldReceive('setSessionContext')
            ->once();
    });

    $response = Livewire::test(Login::class)
        ->set('email', $user->email)
        ->set('password', 'password')
        ->call('login');

    $response
        ->assertHasNoErrors()
        ->assertRedirect(route('dashboard', absolute: false));

    $this->assertAuthenticated();
});

test('users can not authenticate with invalid password', function () {
    $user = User::factory()->create();

    // Mock the login tracking service
    $loginTracker = $this->mock(LoginTrackingService::class, function (MockInterface $mock) use ($user) {
        $mock->shouldReceive('recordFailedLogin')
            ->once()
            ->andReturn(new LoginHistory([
                'user_id' => $user->id,
                'login_successful' => false,
            ]));
    });

    $response = Livewire::test(Login::class)
        ->set('email', $user->email)
        ->set('password', 'wrong-password')
        ->call('login');

    $response->assertHasErrors('email');

    $this->assertGuest();
});

test('users can logout', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)->post('/logout');

    $response->assertRedirect('/');

    $this->assertGuest();
});