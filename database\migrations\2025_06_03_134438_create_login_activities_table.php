<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This creates two tables:
     * 1. login_histories - Detailed record of every login attempt
     * 2. user_login_stats - Daily aggregated statistics per user
     */
    public function up(): void
    {
        // Table 1: login_histories - Granular login attempt records
        Schema::create('login_histories', function (Blueprint $table) {
            // ========================
            // Identification
            // ========================
            $table->id()->comment('Primary key');
            $table->foreignId('user_id')->nullable()
                ->constrained()
                ->onDelete('cascade')
                ->comment('Reference to users table');
                
            // RBAC Context (Originally added in 2025_07_15)
            $table->foreignId('role_id')->nullable()
                ->constrained('system_roles')
                ->onDelete('set null')
                ->comment('Active role for this session');
                
            $table->foreignId('department_id')->nullable()
                ->constrained('organization_departments')
                ->onDelete('set null')
                ->comment('Active department for this session');
                
            $table->string('entity_id', 25)->nullable()
                ->comment('Active entity identifier for this session');

            // ========================
            // Network Information
            // ========================
            $table->string('ip_address', 45)
                ->comment('IPv4 or IPv6 address of login source');
            $table->string('asn', 50)
                ->nullable()
                ->comment('Autonomous System Number - identifies the IP owner network');
            $table->string('isp', 100)
                ->nullable()
                ->comment('Internet Service Provider name');
            $table->string('connection_type', 50)
                ->nullable()
                ->comment('Connection type: cellular, broadband, corporate, etc.');
            $table->boolean('is_proxy')
                ->default(false)
                ->comment('True if connection uses proxy/VPN/Tor');

            // ========================
            // Device Fingerprinting
            // ========================
            $table->text('user_agent')
                ->comment('Raw HTTP User-Agent header');
            $table->string('device_type', 20)
                ->nullable()
                ->comment('Device category: mobile, tablet, desktop, tv, smartwatch');
            $table->string('device_brand', 50)
                ->nullable()
                ->comment('Device manufacturer: Apple, Samsung, etc.');
            $table->string('device_model', 100)
                ->nullable()
                ->comment('Specific device model');
            $table->boolean('is_bot')
                ->default(false)
                ->comment('True if automated bot detected');
            $table->boolean('is_headless')
                ->default(false)
                ->comment('True if headless browser detected');

            // Session Management Fields (Originally added in 2025_07_16)
            $table->string('session_label')->nullable()
                ->comment('User-defined label for this session (e.g., "Work Laptop")');
            $table->string('transferred_from')->nullable()
                ->comment('Session ID this session was transferred from');
            $table->string('transferred_to')->nullable()
                ->comment('Session ID this session was transferred to');
            $table->text('device_fingerprint')->nullable()
                ->comment('Unique fingerprint for device identification');
            $table->boolean('fingerprint_changed')->default(false)
                ->comment('Whether fingerprint changed during session (potential hijacking)');
            $table->integer('risk_score')->nullable()
                ->comment('Calculated risk score (0-100) based on login factors');
            $table->boolean('is_cross_entity_access')->default(false)
                ->comment('Whether this session involves cross-entity access by Super Admin');
            $table->string('original_entity_id', 25)->nullable()
                ->comment('Original entity ID for Super Admin cross-entity access');
            $table->integer('original_role_id')->nullable()
                ->comment('Original role ID for Super Admin cross-entity access');

            // ========================
            // Software Environment
            // ========================
            $table->string('platform', 50)
                ->nullable()
                ->comment('Operating system: Windows, macOS, Android, iOS, Linux');
            $table->string('platform_version', 50)
                ->nullable()
                ->comment('OS version number');
            $table->string('browser', 50)
                ->nullable()
                ->comment('Browser name: Chrome, Firefox, Safari, Edge');
            $table->string('browser_version', 50)
                ->nullable()
                ->comment('Browser version number');
            $table->string('session_id', 255)
                ->nullable()
                ->index()
                ->comment('Laravel session identifier');

            // ========================
            // Geographic Information
            // ========================
            $table->string('country', 100)
                ->nullable()
                ->comment('Country name from IP geolocation');
            $table->string('country_code', 10)
                ->nullable()
                ->comment('ISO 3166-1 alpha-2 country code');
            $table->string('region', 100)
                ->nullable()
                ->comment('State/region name');
            $table->string('city', 100)
                ->nullable()
                ->comment('City name');
            $table->string('timezone', 50)
                ->nullable()
                ->comment('Timezone identifier');
            $table->decimal('latitude', 10, 7)
                ->nullable()
                ->comment('GPS latitude coordinate');
            $table->decimal('longitude', 10, 7)
                ->nullable()
                ->comment('GPS longitude coordinate');

            // ========================
            // Security Flags
            // ========================
            $table->boolean('suspicious_location')
                ->default(false)
                ->comment('True if login location differs from user patterns');
            $table->boolean('new_device')
                ->default(false)
                ->comment('True if device signature not previously seen for user');
            $table->boolean('unusual_timing')
                ->default(false)
                ->comment('True if login outside user normal activity hours');
            $table->string('threat_level', 20)
                ->nullable()
                ->comment('Calculated risk: low, medium, high, critical');
            $table->json('risk_factors')
                ->nullable()
                ->comment('Array of detected risk indicators');

            // ========================
            // Session Tracking
            // ========================
            $table->timestamp('login_at')
                ->useCurrent()
                ->comment('Authentication timestamp');
            $table->unsignedTinyInteger('login_hour')
                ->nullable()
                ->comment('Hour of day (0-23) for analytics');
            $table->string('login_day', 10)
                ->nullable()
                ->comment('Weekday name for analytics');
            $table->string('login_origin', 2048)
                ->nullable()
                ->comment('URL or service that initiated login');
            $table->timestamp('logout_at')
                ->nullable()
                ->comment('Session termination timestamp');
            $table->integer('duration_seconds')
                ->nullable()
                ->comment('Active session duration');
            $table->string('logout_reason', 100)
                ->nullable()
                ->comment('Termination reason: user, timeout, forced, etc.');

            // ========================
            // Authentication Details
            // ========================
            $table->string('login_method', 50)
                ->nullable()
                ->comment('Authentication method: password, oauth, magic_link');
            $table->string('guard', 50)
                ->nullable()
                ->comment('Laravel auth guard used');
            $table->boolean('login_successful')
                ->default(true)
                ->comment('Authentication result status');
            $table->text('failure_reason')
                ->nullable()
                ->comment('Description of authentication failure');
            $table->boolean('mfa_used')
                ->default(false)
                ->comment('Whether multi-factor auth was employed');
            $table->string('security_action', 50)
                ->nullable()
                ->comment('System response: none, challenge, block, etc.');

            // Transferred Flag (Originally added in 2025_07_23_202133)
            $table->boolean('is_transferred')->default(false)
                ->comment('Whether this session has been transferred');

            // ========================
            // Indexes (Originally added in 2025_07_23_221239)
            // ========================
            $table->index('ip_address');
            $table->index('login_at');
            $table->index(['user_id', 'login_at']);
            $table->index('device_type');
            $table->index('platform');
            $table->index('browser');
            $table->index('country');
            $table->index('city');
            $table->index('threat_level');
            $table->index('login_successful');
            $table->index(['session_id'], 'idx_session_id');
            $table->index(['user_id', 'session_id'], 'idx_user_session');
            $table->index(['transferred_from'], 'idx_transferred_from');
            $table->index(['transferred_to'], 'idx_transferred_to');
            $table->index(['user_id', 'logout_at', 'login_successful'], 'idx_active_sessions');

            $table->timestamps();
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
        });

        // Add MySQL-specific constraint (Originally added in 2025_07_23_221930)
        DB::statement('ALTER TABLE login_histories ADD CONSTRAINT check_no_circular_transfer CHECK (transferred_to IS NULL OR transferred_to != session_id)');

        // Table 2: user_login_stats - Daily aggregated metrics (Original)
        Schema::create('user_login_stats', function (Blueprint $table) {
            $table->id()->comment('Primary key');
            $table->foreignId('user_id')
                ->constrained()
                ->onDelete('cascade')
                ->comment('Reference to users table');
            $table->date('login_date')
                ->comment('Date for aggregated statistics');
            $table->unsignedInteger('total_logins')
                ->default(0)
                ->comment('Total login attempts');
            $table->unsignedInteger('successful_logins')
                ->default(0)
                ->comment('Successful authentications');
            $table->unsignedInteger('failed_logins')
                ->default(0)
                ->comment('Failed attempts');
            $table->unsignedInteger('suspicious_logins')
                ->default(0)
                ->comment('Logins with risk flags');
            $table->unsignedInteger('new_device_logins')
                ->default(0)
                ->comment('Logins from unrecognized devices');
            $table->unsignedInteger('unique_ip_count')
                ->default(0)
                ->comment('Distinct IP addresses used');
            $table->unsignedInteger('unique_device_count')
                ->default(0)
                ->comment('Distinct device fingerprints');
            $table->unsignedInteger('unique_location_count')
                ->default(0)
                ->comment('Distinct geographic locations');
            $table->unsignedInteger('avg_session_duration')
                ->nullable()
                ->comment('Mean session length in seconds');
            $table->unsignedInteger('median_session_duration')
                ->nullable()
                ->comment('Median session length in seconds');
            $table->unique(['user_id', 'login_date']);
            $table->timestamps();
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_login_stats');
        Schema::dropIfExists('login_histories');
    }
};