<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\Auth\RegistrationAttempt;
use Illuminate\Support\Facades\Hash;

class PersonalDetails extends Component
{
    public $attempt;
    
    // Personal Information
    public $ownerName = '';
    public $ownerEmail = '';
    public $ownerPhone = '';
    public $password = '';
    public $passwordConfirmation = '';
    
    // Personal Contact (pre-filled)
    public $personalContactName = '';
    public $personalContactEmail = '';
    public $personalContactPhone = '';
    public $personalContactTitle = '';
    public $personalContactPosition = '';
    
    // Personal Address (pre-filled)
    public $personalAddressLine1 = '';
    public $personalAddressLine2 = '';
    public $personalState = '';
    public $personalCity = '';
    public $personalPostalCode = '';
    
    // Reference data
    public $states = [];
    public $cities = [];
    
    protected function rules()
    {
        return [
            'ownerName' => 'required|string|max:255',
            'ownerEmail' => 'required|email|max:255',
            'ownerPhone' => 'required|regex:/^[6-9]\d{9}$/',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/'
            ],
            'passwordConfirmation' => 'required|same:password',
            'personalContactName' => 'required|string|max:255',
            'personalContactEmail' => 'nullable|email|max:255',
            'personalContactPhone' => 'required|regex:/^[6-9]\d{9}$/',
            'personalContactTitle' => 'nullable|string|max:255',
            'personalContactPosition' => 'nullable|string|max:255',
            'personalAddressLine1' => 'required|string|max:255',
            'personalAddressLine2' => 'nullable|string|max:255',
            'personalState' => 'required|string|size:2',
            'personalCity' => 'required|string|max:100',
            'personalPostalCode' => 'required|regex:/^[1-9][0-9]{5}$/'
        ];
    }
    
    protected $messages = [
        'ownerName.required' => 'Owner name is required',
        'ownerPhone.regex' => 'Please enter a valid Indian mobile number',
        'password.required' => 'Password is required',
        'password.min' => 'Password must be at least 8 characters',
        'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'passwordConfirmation.same' => 'Password confirmation does not match',
        'personalContactPhone.regex' => 'Please enter a valid Indian mobile number',
        'personalPostalCode.regex' => 'Please enter a valid 6-digit postal code'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        if (!$this->attempt) {
            return redirect()->route('register');
        }
        
        // Load states
        $this->loadStates();
        
        // Pre-fill email from Stage 1
        $this->ownerEmail = $this->attempt->email;
        
        // Load saved data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        
        // Pre-fill from business information
        if (isset($stagesData['business_information'])) {
            $businessData = $stagesData['business_information'];
            
            // Pre-fill contact info from first business contact
            if (isset($businessData['contacts'][0])) {
                $firstContact = $businessData['contacts'][0];
                $this->personalContactName = $firstContact['name'];
                $this->personalContactEmail = $firstContact['email'];
                $this->personalContactPhone = $firstContact['phone'];
                $this->personalContactTitle = $firstContact['title'] ?? '';
                $this->personalContactPosition = $firstContact['position'] ?? '';
            }
            
            // Pre-fill address from first business address
            if (isset($businessData['addresses'][0])) {
                $firstAddress = $businessData['addresses'][0];
                $this->personalAddressLine1 = $firstAddress['address_line1'];
                $this->personalAddressLine2 = $firstAddress['address_line2'];
                $this->personalState = $firstAddress['state'];
                $this->personalCity = $firstAddress['city'];
                $this->personalPostalCode = $firstAddress['postal_code'];
                
                // Load cities for the pre-filled state
                if ($this->personalState) {
                    $this->loadCities($this->personalState);
                }
            }
        }
        
        // Load saved personal details if exists
        if (isset($stagesData['personal_details'])) {
            $personalData = $stagesData['personal_details'];
            $this->ownerName = $personalData['owner_name'] ?? $this->ownerName;
            $this->ownerPhone = $personalData['owner_phone'] ?? $this->ownerPhone;
            $this->personalContactName = $personalData['personal_contact_name'] ?? $this->personalContactName;
            $this->personalContactEmail = $personalData['personal_contact_email'] ?? $this->personalContactEmail;
            $this->personalContactPhone = $personalData['personal_contact_phone'] ?? $this->personalContactPhone;
            $this->personalContactTitle = $personalData['personal_contact_title'] ?? $this->personalContactTitle;
            $this->personalContactPosition = $personalData['personal_contact_position'] ?? $this->personalContactPosition;
            $this->personalAddressLine1 = $personalData['personal_address_line1'] ?? $this->personalAddressLine1;
            $this->personalAddressLine2 = $personalData['personal_address_line2'] ?? $this->personalAddressLine2;
            $this->personalState = $personalData['personal_state'] ?? $this->personalState;
            $this->personalCity = $personalData['personal_city'] ?? $this->personalCity;
            $this->personalPostalCode = $personalData['personal_postal_code'] ?? $this->personalPostalCode;
            
            // Always reload cities when mounting with saved state
            if ($this->personalState) {
                $this->loadCities($this->personalState);
            }
        }
    }
    
    public function loadStates()
    {
        // Same as BusinessInformation component
        $this->states = [
            ['code' => 'AN', 'name' => 'Andaman and Nicobar Islands'],
            ['code' => 'AP', 'name' => 'Andhra Pradesh'],
            ['code' => 'AR', 'name' => 'Arunachal Pradesh'],
            ['code' => 'AS', 'name' => 'Assam'],
            ['code' => 'BR', 'name' => 'Bihar'],
            ['code' => 'CH', 'name' => 'Chandigarh'],
            ['code' => 'CT', 'name' => 'Chhattisgarh'],
            ['code' => 'DN', 'name' => 'Dadra and Nagar Haveli and Daman and Diu'],
            ['code' => 'DL', 'name' => 'Delhi'],
            ['code' => 'GA', 'name' => 'Goa'],
            ['code' => 'GJ', 'name' => 'Gujarat'],
            ['code' => 'HR', 'name' => 'Haryana'],
            ['code' => 'HP', 'name' => 'Himachal Pradesh'],
            ['code' => 'JK', 'name' => 'Jammu and Kashmir'],
            ['code' => 'JH', 'name' => 'Jharkhand'],
            ['code' => 'KA', 'name' => 'Karnataka'],
            ['code' => 'KL', 'name' => 'Kerala'],
            ['code' => 'LA', 'name' => 'Ladakh'],
            ['code' => 'LD', 'name' => 'Lakshadweep'],
            ['code' => 'MP', 'name' => 'Madhya Pradesh'],
            ['code' => 'MH', 'name' => 'Maharashtra'],
            ['code' => 'MN', 'name' => 'Manipur'],
            ['code' => 'ML', 'name' => 'Meghalaya'],
            ['code' => 'MZ', 'name' => 'Mizoram'],
            ['code' => 'NL', 'name' => 'Nagaland'],
            ['code' => 'OR', 'name' => 'Odisha'],
            ['code' => 'PY', 'name' => 'Puducherry'],
            ['code' => 'PB', 'name' => 'Punjab'],
            ['code' => 'RJ', 'name' => 'Rajasthan'],
            ['code' => 'SK', 'name' => 'Sikkim'],
            ['code' => 'TN', 'name' => 'Tamil Nadu'],
            ['code' => 'TG', 'name' => 'Telangana'],
            ['code' => 'TR', 'name' => 'Tripura'],
            ['code' => 'UP', 'name' => 'Uttar Pradesh'],
            ['code' => 'UT', 'name' => 'Uttarakhand'],
            ['code' => 'WB', 'name' => 'West Bengal']
        ];
    }
    
    public function loadCities($stateCode)
    {
        // Same cities data as BusinessInformation component
        $citiesByState = [
            'MH' => ['Mumbai', 'Pune', 'Nagpur', 'Thane', 'Nashik', 'Aurangabad'],
            'KA' => ['Bengaluru', 'Mysuru', 'Mangaluru', 'Hubballi', 'Belagavi'],
            'DL' => ['New Delhi', 'Central Delhi', 'East Delhi', 'North Delhi', 'South Delhi', 'West Delhi'],
            'GJ' => ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Gandhinagar'],
            'TN' => ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
        ];
        
        $this->cities = $citiesByState[$stateCode] ?? [];
    }
    
    public function updatedPersonalState($value)
    {
        $this->loadCities($value);
        $this->personalCity = '';
    }
    
    public function saveAndContinue()
    {
        $this->validate();
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['personal_details'] = [
            'owner_name' => $this->ownerName,
            'owner_phone' => $this->ownerPhone,
            'password' => Hash::make($this->password), // Hash password for storage
            'personal_contact_name' => $this->personalContactName,
            'personal_contact_email' => $this->personalContactEmail,
            'personal_contact_phone' => $this->personalContactPhone,
            'personal_contact_title' => $this->personalContactTitle,
            'personal_contact_position' => $this->personalContactPosition,
            'personal_address_line1' => $this->personalAddressLine1,
            'personal_address_line2' => $this->personalAddressLine2,
            'personal_state' => $this->personalState,
            'personal_city' => $this->personalCity,
            'personal_postal_code' => $this->personalPostalCode
        ];
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('personal_details', $stagesCompleted)) {
            $stagesCompleted[] = 'personal_details';
        }
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'tax_information'
        ]);
        
        $this->dispatch('stepCompleted', 'personal_details');
    }
    
    public function stepBack()
    {
        $this->dispatch('stepBack');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.personal-details');
    }
} 