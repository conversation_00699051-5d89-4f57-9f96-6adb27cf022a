@props([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'resize' => 'vertical',
    'invalid' => null,
    'rows' => 4,
])

@php
$invalid ??= ($name && $errors->has($name));

$resizeClasses = match ($resize) {
    'none' => 'resize-none',
    'both' => 'resize',
    'horizontal' => 'resize-x',
    'vertical' => 'resize-y',
    default => 'resize-y',
};

$baseClasses = 'block p-3 w-full shadow-sm border rounded-lg bg-white dark:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';

$stateClasses = $invalid 
    ? 'border-red-500 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600 dark:text-red-100 dark:placeholder-red-400'
    : 'border-zinc-300 text-zinc-900 placeholder-zinc-400 focus:border-blue-500 focus:ring-blue-500 dark:border-zinc-600 dark:text-zinc-100 dark:placeholder-zinc-400';

$classes = "$baseClasses $stateClasses $resizeClasses text-sm";
@endphp

<textarea
    {{ $attributes->merge([
        'class' => $classes,
        'rows' => $rows,
        'name' => $name
    ]) }}
    @if ($invalid) aria-invalid="true" @endif
>{{ $slot }}</textarea>
