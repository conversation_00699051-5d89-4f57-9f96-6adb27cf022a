<?php
// app/CodeGenerator/CodeGeneratorServiceProvider.php

namespace App\CodeGenerator;

use Illuminate\Support\ServiceProvider;

class CodeGeneratorServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('code.generator', function ($app) {
            return new CodeGenerator();
        });
    }

    public function boot()
    {
        // No boot actions needed
    }
}
