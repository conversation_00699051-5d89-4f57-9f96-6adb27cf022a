<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Rbac\UserRoleAssignment;

class CheckUserHasAnyRole
{
    /**
     * Handle an incoming request.
     * Ensures that authenticated user has at least one active role.
     * Manages role selection when user has multiple roles.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();
            
            // Get all active role assignments for the user
            $roleAssignments = UserRoleAssignment::with('role')
                ->where('user_id', $user->id)
                ->where('is_active', true)
                ->where('approval_status', 'approved')
                ->where(function ($query) {
                    $today = now()->toDateString();
                    $query->whereNull('assigned_from')
                        ->orWhere('assigned_from', '<=', $today);
                })
                ->where(function ($query) {
                    $today = now()->toDateString();
                    $query->whereNull('assigned_until')
                        ->orWhere('assigned_until', '>=', $today);
                })
                ->get();
            
            // If user has no roles, redirect to an error page
            if ($roleAssignments->isEmpty()) {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                return redirect()->route('login')->with('status', 'Your account does not have any active roles.');
            }
            
            // Check if a role is already selected in the session
            $activeRoleId = session('active_role_id');
            
            // If no role is selected or the selected role is not in active assignments
            if (!$activeRoleId || !$roleAssignments->contains('role_id', $activeRoleId)) {
                // If user has only one role, select it automatically
                if ($roleAssignments->count() === 1) {
                    session(['active_role_id' => $roleAssignments->first()->role_id]);
                    session(['active_role_name' => $roleAssignments->first()->role->role_name]);
                } else {
                    // User has multiple roles but none selected, redirect to role selection
                    return response()->view('livewire.auth.role-selection', [
                        'roles' => $roleAssignments,
                    ]);
                }
            }
        }

        return $next($request);
    }
} 