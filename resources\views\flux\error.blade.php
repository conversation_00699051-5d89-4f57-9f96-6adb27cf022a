@props([
    'name' => null,
    'message' => null,
    'nested' => true,
])

@php
$message ??= $name ? $errors->first($name) : null;

if ($name && (is_null($message) || $message === '') && $nested) {
    $message = $errors->first($name . '.*');
}

$classes = 'mt-2 text-sm font-medium text-red-500 dark:text-red-400 flex items-center gap-1';
$classes .= $message ? '' : ' hidden';
@endphp

<div role="alert" aria-live="polite" aria-atomic="true" {{ $attributes->merge(['class' => $classes]) }} data-flux-error>
    @if ($message)
        <flux:icon name="exclamation-triangle" class="h-4 w-4 flex-shrink-0" />
        {{ $message }}
    @endif
</div>
