<div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-white dark:bg-zinc-900">
    <div class="w-full sm:max-w-2xl mt-6 px-6 py-8 bg-white dark:bg-zinc-800 shadow-xl border border-zinc-200 dark:border-zinc-700 overflow-hidden sm:rounded-xl">
        <!-- Header -->
        <header class="mb-6 text-center">
            <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
                <flux:icon name="exclamation-triangle" class="w-8 h-8 text-red-600 dark:text-red-400" />
            </div>
            <h1 class="text-2xl font-semibold text-zinc-900 dark:text-zinc-100 mb-2">
                {{ __('Session Limit Reached') }}
            </h1>
            <div class="text-sm text-zinc-600 dark:text-zinc-400 space-y-2">
                <p>{{ __('You have reached the maximum number of concurrent sessions allowed for your account.') }}</p>
                <p>{{ __('Please terminate one of your existing sessions to continue:') }}</p>
            </div>
        </header>

        <!-- Active Sessions -->
        @if(isset($activeSessions) && count($activeSessions) > 0)
            <section class="mb-6" aria-labelledby="active-sessions-heading">
                <h2 id="active-sessions-heading" class="sr-only">{{ __('Active Sessions') }}</h2>
                <div class="space-y-3">
                    @foreach($activeSessions as $session)
                        <article class="p-4 border border-zinc-200 dark:border-zinc-700 rounded-xl bg-zinc-50 dark:bg-zinc-900/50 transition-colors hover:border-zinc-300 dark:hover:border-zinc-600">
                            <div class="flex justify-between items-start">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center gap-2 mb-2">
                                        <div class="w-8 h-8 rounded-lg bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center flex-shrink-0">
                                            <flux:icon name="computer-desktop" class="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <h3 class="font-medium text-zinc-900 dark:text-zinc-100 truncate">
                                                {{ $session->browser ?? 'Unknown Browser' }} {{ $session->browser_version ?? '' }}
                                            </h3>
                                            <p class="text-sm text-zinc-600 dark:text-zinc-400">
                                                {{ $session->platform ?? 'Unknown OS' }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs text-zinc-500 dark:text-zinc-400">
                                        <div>{{ __('Device: ') }}{{ $session->device_type ?? 'Unknown Device' }}</div>
                                        <div>{{ __('Location: ') }}{{ $session->getLocationString() }}</div>
                                        <div>{{ __('Login: ') }}{{ $session->login_at ? $session->login_at->diffForHumans() : 'Unknown time' }}</div>
                                        <div>{{ __('IP: ') }}{{ $session->ip_address ?? 'Unknown' }}</div>
                                    </div>
                                </div>

                                <div class="flex items-center ml-4">
                                    @if($session->session_id === $currentSessionId)
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800">
                                            {{ __('Current') }}
                                        </span>
                                    @else
                                        <form action="{{ route('auth.terminate-session') }}" method="post" class="inline">
                                            @csrf
                                            <input type="hidden" name="session_id" value="{{ $session->session_id }}">
                                            <flux:button type="submit"
                                                         variant="outline"
                                                         size="sm"
                                                         class="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20 dark:hover:border-red-700">
                                                {{ __('Terminate') }}
                                            </flux:button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>
            </section>
        @else
            <div class="p-6 border border-yellow-200 dark:border-yellow-800 rounded-xl bg-yellow-50 dark:bg-yellow-900/20 mb-6">
                <div class="flex items-center gap-3">
                    <flux:icon name="exclamation-triangle" class="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        {{ __('No active sessions found. Please try again.') }}
                    </p>
                </div>
            </div>
        @endif

        <!-- Actions -->
        <footer class="flex flex-col sm:flex-row gap-3 sm:justify-between">
            <form action="{{ route('logout') }}" method="post" class="flex-1 sm:flex-initial">
                @csrf
                <flux:button type="submit" variant="outline" class="w-full sm:w-auto">
                    {{ __('Logout') }}
                </flux:button>
            </form>

            <flux:button :href="route('login')" variant="primary" class="w-full sm:w-auto">
                {{ __('Try Again') }}
            </flux:button>
        </footer>
    </div>
</div>