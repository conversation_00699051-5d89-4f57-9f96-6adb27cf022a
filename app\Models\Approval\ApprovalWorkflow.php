<?php
// app\Models\Approval\ApprovalWorkflow.php
namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Entity\Entity;
use App\Traits\HasAuditFields;

/**
 * Class ApprovalWorkflow
 *
 * Defines approval workflows for different actions and entity types.
 * This is the core configuration for how approvals work in the system.
 *
 * @property int $id
 * @property string|null $entity_id Business entity identifier (null = global rule)
 * @property string $approvable_type Model class this workflow applies to
 * @property string $action_type Specific action (create|update|delete|activate)
 * @property string $name Human-readable workflow name
 * @property bool $is_active Whether workflow is currently active
 * @property string $strategy Approval strategy (single|sequential|parallel)
 * @property int $required_approvals For parallel: number of approvals needed
 * @property int $max_rejections Number of rejections that cancel the request
 * @property bool $allow_self_approval Whether requester can approve own request
 * @property array|null $custom_conditions Special rules (amount thresholds, etc.)
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class ApprovalWorkflow extends Model
{
    use SoftDeletes, HasAuditFields;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'entity_id',
        'approvable_type',
        'action_type',
        'name',
        'is_active',
        'strategy',
        'required_approvals',
        'max_rejections',
        'allow_self_approval',
        'custom_conditions',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'allow_self_approval' => 'boolean',
        'custom_conditions' => 'array',
        'required_approvals' => 'integer',
        'max_rejections' => 'integer',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    /**
     * Available approval strategies.
     */
    const STRATEGIES = [
        'single' => 'Any one approver can approve',
        'sequential' => 'All steps must be approved in order',
        'parallel' => 'Required number of approvals from any approvers',
    ];

    /**
     * Common action types.
     */
    const ACTION_TYPES = [
        'create' => 'Creating new record',
        'update' => 'Updating existing record',
        'delete' => 'Deleting record',
        'activate' => 'Activating record',
        'inactivate' => 'Inactivating record',
        'restore' => 'Restoring deleted record',
    ];

    /**
     * Get human-readable strategy description.
     *
     * @return string
     */
    public function getStrategyDescriptionAttribute(): string
    {
        return self::STRATEGIES[$this->strategy] ?? 'Unknown strategy';
    }

    /**
     * Check if workflow uses sequential approval.
     *
     * @return bool
     */
    public function isSequential(): bool
    {
        return $this->strategy === 'sequential';
    }

    /**
     * Check if workflow uses parallel approval.
     *
     * @return bool
     */
    public function isParallel(): bool
    {
        return $this->strategy === 'parallel';
    }

    /**
     * Check if workflow uses single approval.
     *
     * @return bool
     */
    public function isSingle(): bool
    {
        return $this->strategy === 'single';
    }

    /**
     * Check if workflow applies to a specific entity or is global.
     *
     * @return bool
     */
    public function isGlobal(): bool
    {
        return $this->entity_id === null;
    }

    /**
     * Check if custom conditions are met.
     *
     * @param array $data Data to check against conditions
     * @return bool
     */
    public function checkCustomConditions(array $data): bool
    {
        if (empty($this->custom_conditions)) {
            return true;
        }

        foreach ($this->custom_conditions as $condition) {
            if (!$this->evaluateCondition($condition, $data)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     *
     * @param array $condition
     * @param array $data
     * @return bool
     */
    protected function evaluateCondition(array $condition, array $data): bool
    {
        $field = $condition['field'] ?? null;
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? null;

        if (!$field || !isset($data[$field])) {
            return false;
        }

        $dataValue = $data[$field];

        return match ($operator) {
            '=' => $dataValue == $value,
            '!=' => $dataValue != $value,
            '>' => $dataValue > $value,
            '>=' => $dataValue >= $value,
            '<' => $dataValue < $value,
            '<=' => $dataValue <= $value,
            'in' => in_array($dataValue, (array)$value),
            'not_in' => !in_array($dataValue, (array)$value),
            'contains' => str_contains($dataValue, $value),
            default => false,
        };
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the entity this workflow belongs to.
     *
     * @return BelongsTo
     */
    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    /**
     * Get all steps in this workflow.
     *
     * @return HasMany
     */
    public function steps(): HasMany
    {
        return $this->hasMany(ApprovalStep::class, 'workflow_id')
            ->orderBy('step_number');
    }

    /**
     * Get active steps in this workflow.
     *
     * @return HasMany
     */
    public function activeSteps(): HasMany
    {
        return $this->steps()->where('is_required', true);
    }

    /**
     * Get all approval requests using this workflow.
     *
     * @return HasMany
     */
    public function approvalRequests(): HasMany
    {
        return $this->hasMany(ApprovalRequest::class, 'workflow_id');
    }

    /**
     * Get pending approval requests.
     *
     * @return HasMany
     */
    public function pendingRequests(): HasMany
    {
        return $this->approvalRequests()->where('status', 'pending');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to active workflows only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to workflows for a specific entity.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $entityId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEntity($query, string $entityId)
    {
        return $query->where('entity_id', $entityId);
    }

    /**
     * Scope to global workflows.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeGlobal($query)
    {
        return $query->whereNull('entity_id');
    }

    /**
     * Scope to workflows for a specific model type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $modelClass
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForModel($query, string $modelClass)
    {
        return $query->where('approvable_type', $modelClass);
    }

    /**
     * Scope to workflows for a specific action.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $action
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForAction($query, string $action)
    {
        return $query->where('action_type', $action);
    }

    // ===== STATIC METHODS =====

    /**
     * Find the applicable workflow for a given context.
     * Prioritizes entity-specific over global workflows.
     *
     * @param string $modelClass
     * @param string $actionType
     * @param string|null $entityId
     * @return self|null
     */
    public static function findApplicable(
        string $modelClass,
        string $actionType,
        ?string $entityId = null
    ): ?self {
        // First try entity-specific workflow
        if ($entityId) {
            $workflow = self::active()
                ->forEntity($entityId)
                ->forModel($modelClass)
                ->forAction($actionType)
                ->first();

            if ($workflow) {
                return $workflow;
            }
        }

        // Fall back to global workflow
        return self::active()
            ->global()
            ->forModel($modelClass)
            ->forAction($actionType)
            ->first();
    }

    /**
     * Create a default workflow for entity registration.
     *
     * @param string|null $entityId
     * @return self
     */
    public static function createRegistrationWorkflow(?string $entityId = null): self
    {
        return self::create([
            'entity_id' => $entityId,
            'approvable_type' => Entity::class,
            'action_type' => 'create',
            'name' => 'Entity Registration Approval',
            'is_active' => true,
            'strategy' => 'sequential',
            'required_approvals' => 1,
            'max_rejections' => 1,
            'allow_self_approval' => false,
            'custom_conditions' => [
                ['field' => 'entity_type', 'operator' => 'in', 'value' => ['distributor', 'dealer']],
            ],
        ]);
    }
}
