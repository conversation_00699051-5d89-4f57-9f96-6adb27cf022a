<?php
//app\Agent\Facades\Agent.php
namespace App\Agent\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static bool isMobile()
 * @method static bool isTablet()
 * @method static bool isDesktop()
 * @method static bool isSmartwatch()
 * @method static bool isTV()
 * @method static string|null browser()
 * @method static string|null browserVersion()
 * @method static string|null platform()
 * @method static string|null platformVersion()
 * @method static string|null deviceType()
 * @method static string|null deviceBrand()
 * @method static string|null deviceModel()
 * @method static bool isBot()
 * @method static array languages()
 * @method static bool isHeadlessChrome()
 * @method static bool isProxy()
 * @method static bool isTor()
 * @method static bool isVPN()
 * @method static array toArray()
 * @method static string toJson(int $options = 0)
 * @method static \App\Agent\Agent analyze()
 * @method static bool isWindows()
 * @method static bool isMac()
 * @method static bool isLinux()
 * @method static bool isAndroid()
 * @method static bool isIOS()
 * @method static bool isApple()
 * @method static bool isSamsung()
 * @method static bool isGoogle()
 * @method static bool isHuawei()
 * @method static bool isXiaomi()
 * @method static bool isOnePlus()
 * @method static bool isSony()
 * @method static \App\Agent\Agent setUserAgent(string $userAgent)
 */
class Agent extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'agent';
    }
}
