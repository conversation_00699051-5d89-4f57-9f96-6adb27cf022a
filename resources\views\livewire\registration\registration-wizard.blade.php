<div class="min-h-screen bg-gradient-to-br from-zinc-50 via-white to-blue-50 dark:from-zinc-900 dark:via-zinc-900 dark:to-zinc-800">
    <!-- Background Pattern -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 dark:opacity-5"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 dark:opacity-5"></div>
        <div class="absolute top-40 left-1/2 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 dark:opacity-5"></div>
    </div>

    <div class="relative z-10 min-h-screen flex flex-col">
        <!-- Header -->
        <header class="px-4 sm:px-6 lg:px-8 pt-6 sm:pt-8 pb-4">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center justify-between">
                    <!-- Logo -->
                    <a href="/"
                       class="group flex items-center space-x-2 sm:space-x-3 transition-opacity hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-zinc-900 rounded-lg p-2"
                       aria-label="{{ __('Go to Home') }}">
                        <div class="flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 shadow-lg transition-transform group-hover:scale-105">
                            <x-app-logo-icon class="h-6 w-6 sm:h-7 sm:w-7 fill-current text-white" />
                        </div>
                        <span class="text-lg sm:text-xl font-semibold text-zinc-900 dark:text-white hidden sm:block">Registration Portal</span>
                    </a>

                    <!-- Help Button -->
                    <flux:button variant="ghost" size="sm" icon="information-circle" href="#" class="text-zinc-700 dark:text-zinc-300">
                        <span class="hidden sm:inline">{{ __('Need Help?') }}</span>
                        <span class="sm:hidden">{{ __('Help') }}</span>
                    </flux:button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 px-4 sm:px-6 lg:px-8 pb-8">
            <div class="max-w-4xl mx-auto">
                <!-- Progress Section -->
                <section class="mb-6 sm:mb-8" aria-labelledby="registration-title">
                    <!-- Title and Step Counter -->
                    <div class="text-center mb-4 sm:mb-6">
                        <h1 id="registration-title" class="text-2xl sm:text-3xl font-bold text-zinc-900 dark:text-white mb-1 sm:mb-2">
                            {{ $attempt && $attempt->entity_type ? ucfirst($attempt->entity_type) : 'Entity' }} Registration
                        </h1>
                        <p class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400">
                            Complete all steps to register your {{ $attempt && $attempt->entity_type ? strtolower($attempt->entity_type) : 'entity' }}
                        </p>
                    </div>

                    <!-- Enhanced Progress Bar -->
                    <div class="bg-white dark:bg-zinc-800 rounded-2xl shadow-md border border-gray-100 dark:border-zinc-700 p-4 sm:p-6 mb-6 sm:mb-8">
                        <!-- Step Counter -->
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <div class="flex items-baseline">
                                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Step</span>
                                    <span class="text-2xl font-bold text-gray-900 dark:text-white ml-2">{{ $currentStep }}</span>
                                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400 ml-1">of {{ $totalSteps }}</span>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900 dark:text-white hidden sm:block">{{ $steps[$currentStep]['name'] }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ round(($currentStep / $totalSteps) * 100) }}% Complete</p>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="relative">
                            <div class="overflow-hidden h-3 text-xs flex rounded-full bg-zinc-100 dark:bg-zinc-700">
                                <div
                                    class="relative overflow-hidden shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500 transition-all duration-700 ease-out"
                                    style="width: {{ ($currentStep / $totalSteps) * 100 }}%"
                                >
                                    <div class="absolute inset-0 bg-white/20"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Indicators -->
                        <div class="mt-6">
                            <div class="relative">
                                <!-- Connector Line (Background) -->
                                <div class="absolute top-5 left-0 right-0 h-0.5 bg-zinc-200 dark:bg-zinc-700"></div>
                                <div class="absolute top-5 left-0 h-0.5 bg-blue-500 dark:bg-blue-400 transition-all duration-700"
                                     style="width: {{ (($currentStep - 1) / ($totalSteps - 1)) * 100 }}%"></div>
                                
                                <!-- Steps Container -->
                                <div class="relative flex justify-between">
                                    @foreach($steps as $step => $info)
                                        <div class="flex flex-col items-center group">
                                            <!-- Step Circle -->
                                            <div class="relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300 bg-white dark:bg-zinc-800 {{ $currentStep >= $step ? 'border-blue-500 bg-blue-500 dark:border-blue-400 dark:bg-blue-400 text-white shadow-lg shadow-blue-500/30' : 'border-zinc-300 dark:border-zinc-600 text-zinc-500 dark:text-zinc-400 group-hover:border-zinc-400 dark:group-hover:border-zinc-500' }}">
                                                @if($currentStep > $step)
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                @else
                                                    <span class="text-sm font-semibold">{{ $step }}</span>
                                                @endif
                                            </div>
                                            
                                            <!-- Step Label - Only show for current step on mobile -->
                                            <span class="mt-2 text-xs font-medium text-center {{ $currentStep >= $step ? 'text-zinc-900 dark:text-white' : 'text-zinc-500 dark:text-zinc-400' }} {{ $currentStep == $step ? 'block' : 'hidden' }} sm:block max-w-[60px] sm:max-w-none">
                                                {{ $info['name'] }}
                                            </span>

                                            <!-- Tooltip for non-current steps on mobile -->
                                            @if($currentStep != $step)
                                                <div class="sm:hidden absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-zinc-900 dark:bg-zinc-700 text-white text-xs rounded-md py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20">
                                                    {{ $info['name'] }}
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            
                            <!-- Mobile Step Information -->
                            <div class="mt-6 sm:hidden">
                                <div class="bg-zinc-50 dark:bg-zinc-800/50 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs font-medium text-zinc-500 dark:text-zinc-400">Current:</span>
                                            <span class="text-sm font-semibold text-zinc-900 dark:text-white">{{ $steps[$currentStep]['name'] }}</span>
                                        </div>
                                        <span class="text-xs font-medium text-zinc-500 dark:text-zinc-400">Step {{ $currentStep }}/{{ $totalSteps }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step Content Card -->
                <div class="bg-white dark:bg-zinc-800 rounded-2xl shadow-md border border-gray-100 dark:border-zinc-700 overflow-hidden">
                    <div class="p-6 sm:p-8 lg:p-10">
                        @switch($currentStep)
                            @case(1)
                                @livewire('registration.steps.email-verification')
                                @break
                            @case(2)
                                @livewire('registration.steps.entity-type-selection')
                                @break
                            @case(3)
                                @livewire('registration.steps.business-information')
                                @break
                            @case(4)
                                @livewire('registration.steps.personal-details')
                                @break
                            @case(5)
                                @livewire('registration.steps.tax-information')
                                @break
                            @case(6)
                                @livewire('registration.steps.business-kyc-documents')
                                @break
                            @case(7)
                                @livewire('registration.steps.additional-documents')
                                @break
                            @case(8)
                                @livewire('registration.steps.personal-kyc-documents')
                                @break
                            @case(9)
                                @livewire('registration.steps.review-submit')
                                @break
                        @endswitch
                    </div>
                </div>

                <!-- Help Section -->
                <footer class="mt-6 sm:mt-8 text-center">
                    <p class="text-xs sm:text-sm text-zinc-600 dark:text-zinc-400">
                        Having trouble?
                        <flux:link href="#" class="font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                            Contact our support team
                        </flux:link>
                    </p>
                </footer>
            </div>
        </div>
    </div>



    <!-- Keep existing debug information and scripts -->
    @if(config('app.debug'))
        <!-- Debug section remains unchanged -->
        <div class="mt-6 max-w-4xl mx-auto px-4">
            <flux:card variant="outline" title="Debug Information" class="text-xs font-mono">
                <div class="space-y-2">
                    <p>Current Step: {{ $currentStep }}</p>
                    <p>Total Steps: {{ $totalSteps }}</p>
                    <p>Registration Attempt ID: {{ $attempt->id ?? 'not set' }}</p>
                    <p>Current Stage: {{ $attempt->current_stage ?? 'not set' }}</p>
                    <p>Completed Stages: <pre class="mt-1 bg-gray-100 dark:bg-gray-800 p-2 rounded">{{ json_encode($attempt->completed_stages ?? [], JSON_PRETTY_PRINT) }}</pre></p>
                    <div id="event-log" class="mt-4">
                        <h4 class="font-bold">Event Log:</h4>
                        <ul class="list-disc pl-5 mt-1" id="event-list"></ul>
                    </div>
                </div>
            </flux:card>
        </div>
    @endif

    <!-- Keep all existing scripts unchanged -->
    @if(config('app.debug'))
        <script>
            document.addEventListener('livewire:initialized', () => {
                // Existing debug scripts remain unchanged
                Livewire.on('*', (eventName, ...args) => {
                    console.log(`Livewire Event: ${eventName}`, args);
                    
                    const eventList = document.getElementById('event-list');
                    if (eventList) {
                        const li = document.createElement('li');
                        const timestamp = new Date().toLocaleTimeString();
                        li.textContent = `[${timestamp}] ${eventName}: ${JSON.stringify(args)}`;
                        eventList.prepend(li);
                        
                        if (eventList.children.length > 10) {
                            eventList.removeChild(eventList.lastChild);
                        }
                    }
                });
                
                Livewire.on('stepCompleted', (step) => {
                    console.log(`Step Completed: ${step}`);
                });
                
                Livewire.on('stepBack', () => {
                    console.log('Step Back triggered');
                });
                
                Livewire.on('refreshComponent', () => {
                    console.log('Refresh Component triggered');
                });
            });
        </script>
    @endif

    <!-- Keep existing event handling scripts unchanged -->
    <script>
        document.addEventListener('livewire:initialized', () => {
            Livewire.on('kyc-step-completed', () => {
                console.log('KYC Step Completed Event Received');
                
                const wizardComponent = Livewire.find(
                    document.querySelector('[wire\\:id]').getAttribute('wire:id')
                );
                
                if (wizardComponent) {
                    console.log('Found wizard component, forcing step change');
                    wizardComponent.set('currentStep', 9);
                    wizardComponent.call('$refresh');
                    console.log('Step changed to 9 (Review & Submit)');
                } else {
                    console.error('Could not find wizard component');
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('step', '9');
                    window.location.href = currentUrl.toString();
                }
            });
            
            Livewire.on('directRedirect', (params) => {
                console.log('Direct Redirect Event Received', params);
                
                if (params && params.url) {
                    console.log('Redirecting to:', params.url);
                    setTimeout(() => {
                        window.location.href = params.url;
                    }, 1000);
                } else {
                    console.error('No URL provided for redirect');
                }
            });
        });
    </script>
</div>