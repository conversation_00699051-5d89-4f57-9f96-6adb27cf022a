{{-- resources/views/components/session-timer.blade.php - Enhanced Version --}}
<div
  role="timer"
  aria-label="{{ __('Session Timer') }}"
  x-data="sessionTimer()"
  x-init="init()"
  class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-2 bg-white dark:bg-zinc-800 rounded-xl shadow-md border border-zinc-100 dark:border-zinc-700 transition-all duration-300"
>
  <!-- Date/Time Section -->
  <div class="flex items-center gap-3">
    <div class="p-2 bg-blue-100/50 dark:bg-blue-900/20 rounded-lg">
      <flux:icon name="clock" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
    </div>
    <div class="flex flex-col">
      <span x-text="formattedDate()" class="font-medium text-zinc-700 dark:text-zinc-300" aria-label="{{ __('Current Date') }}"></span>
      <span x-text="formattedTime()" class="text-sm text-zinc-500 dark:text-zinc-400" aria-label="{{ __('Current Time') }}"></span>
    </div>
  </div>
  <!-- Countdown Section -->
  <div class="flex items-center gap-3">
    <div
      class="p-2 rounded-lg transition-colors duration-500"
      x-bind:class="remainingSeconds > WARNING_THRESHOLD_SECONDS ? 'bg-amber-100/50 dark:bg-amber-900/20' : 'bg-red-100/50 dark:bg-red-900/20'"
    >
      <flux:icon name="clock"
          class="h-5 w-5 transition-colors duration-500"
          x-bind:class="remainingSeconds > WARNING_THRESHOLD_SECONDS ? 'text-amber-600 dark:text-amber-400' : 'text-red-600 dark:text-red-400'" />
    </div>
    <div class="flex flex-col">
      <span class="text-xs text-zinc-500 dark:text-zinc-400">{{ __('Session expires in') }}</span>
      <span
        x-text="formattedCountdownText()"
        class="font-medium transition-colors duration-500"
        x-bind:class="remainingSeconds > WARNING_THRESHOLD_SECONDS ? 'text-amber-600 dark:text-amber-400' : 'text-red-600 dark:text-red-400 animate-pulse'"
      ></span>
    </div>
  </div>
</div>

@push('scripts')
<script>
function sessionTimer() {
  return {
    SAFETY_BUFFER_SECONDS: 7,
    WARNING_THRESHOLD_SECONDS: 300,
    currentTime: new Date(),
    sessionLifetime: {{ config('session.lifetime') * 60 }},
    remainingSeconds: 0,
    intervalId: null,
    minuteText: '{{ __("minute") }}',
    minutesText: '{{ __("minutes") }}',
    
    init() {
      const lifetime = {{ config('session.lifetime') * 60 }};
      const lastActivity = {{ session('last_activity', time()) ? time() - session('last_activity', time()) : 0 }};
      this.remainingSeconds = Math.max(0, (lifetime - this.SAFETY_BUFFER_SECONDS) - lastActivity);
      
      if (this.remainingSeconds <= 0) {
        this.autoLogout();
        return;
      }
      
      this.updateTimes();
      this.intervalId = setInterval(() => {
        this.updateTimes();
        this.remainingSeconds = Math.max(0, this.remainingSeconds - 1);
        if (this.remainingSeconds <= 0) {
          clearInterval(this.intervalId);
          this.autoLogout();
        }
      }, 1000);
    },
    
    updateTimes() {
      this.currentTime = new Date();
    },
    
    formattedTime() {
      return this.currentTime.toLocaleTimeString(undefined, {
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit'
      });
    },
    
    formattedDate() {
      return this.currentTime.toLocaleDateString(undefined, {
        weekday: 'short', 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric'
      });
    },
    
    formattedCountdownMinutes() {
      const mins = Math.ceil(this.remainingSeconds / 60);
      return mins > 0 ? mins : 0;
    },
    
    formattedCountdownText() {
      const mins = this.formattedCountdownMinutes();
      return mins + ' ' + (mins === 1 ? this.minuteText : this.minutesText);
    },
    
    autoLogout() {
      window.location.href = '{{ route("login") }}';
    }
  }
}
</script>
@endpush