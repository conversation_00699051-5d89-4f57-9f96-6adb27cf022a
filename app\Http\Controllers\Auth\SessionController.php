<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Auth\LoginHistory;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\UserRoleAssignment;
use App\Services\LoginTrackingService;
use App\Services\LoginValidationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class SessionController extends Controller
{
    protected LoginTrackingService $loginTracker;
    protected LoginValidationService $loginValidator;
    
    public function __construct(LoginTrackingService $loginTracker, LoginValidationService $loginValidator)
    {
        $this->loginTracker = $loginTracker;
        $this->loginValidator = $loginValidator;
    }
    
    /**
     * Terminate a specific session
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function terminateSession(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string',
        ]);
        
        $sessionId = $request->input('session_id');
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login');
        }
        
        // Find and terminate the specified session
        $session = LoginHistory::where('user_id', $user->id)
            ->where('session_id', $sessionId)
            ->whereNull('logout_at')
            ->first();
        
        if ($session) {
            $session->update([
                'logout_at' => now(),
                'logout_reason' => 'terminated_by_user',
                'duration_seconds' => now()->diffInSeconds($session->login_at),
            ]);
        }
        
        // If we're terminating our own session, log out
        if ($sessionId === Session::getId()) {
            Auth::logout();
            Session::invalidate();
            Session::regenerateToken();
            return redirect()->route('login');
        }
        
        return redirect()->route('auth.session-management')
            ->with('status', 'Session terminated successfully.');
    }
    
    /**
     * Handle role selection for users with multiple roles
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function selectRole(Request $request)
    {
        $request->validate([
            'role_id' => 'required|exists:system_roles,id',
        ]);
        
        $roleId = $request->input('role_id');
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login');
        }
        
        // Check for duplicate active session with same role
        $duplicateSession = LoginHistory::activeSessions()
            ->where('user_id', $user->id)
            ->where('role_id', $roleId)
            ->where('session_id', '!=', Session::getId())
            ->exists();
            
        if ($duplicateSession) {
            return back()->withErrors([
                'role_id' => 'You already have an active session with this role. Multiple sessions with the same role are not allowed.',
            ]);
        }
        
        // Set the session context with the selected role
        $this->loginValidator->setSessionContext($user, $roleId);
        
        // Update the login history record with the selected role
        LoginHistory::where('user_id', $user->id)
            ->where('session_id', Session::getId())
            ->whereNull('logout_at')
            ->update([
                'role_id' => $roleId,
                'department_id' => Session::get('active_department_id'),
                'entity_id' => Session::get('active_entity_id')
            ]);
        
        // Regenerate session for security
        Session::regenerate();
        
        return redirect()->intended(route('dashboard'));
    }
} 