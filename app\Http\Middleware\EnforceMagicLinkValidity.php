<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;
use App\Models\MagicLink;

class EnforceMagicLinkValidity
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Only check for authenticated users
        if (Auth::check() && Session::has('magic_link_id')) {
            $magicLinkId = Session::get('magic_link_id');
            $magicLink = MagicLink::find($magicLinkId);
            if ($magicLink) {
                // Check if cancelled
                if ($magicLink->cancelled_at) {
                    Auth::logout();
                    Session::flush();
                    return redirect()->route('login')->withErrors(['email' => 'Your magic link session was cancelled. Please log in again.']);
                }
                // Check if expired by time
                $loginTime = Session::get('magic_link_login_time');
                $validity = $magicLink->validity_in_minutes;
                if ($loginTime && now()->diffInMinutes($loginTime) >= $validity) {
                    Auth::logout();
                    Session::flush();
                    return redirect()->route('login')->withErrors(['email' => 'Your magic link session has expired. Please log in again.']);
                }
            }
        }
        return $next($request);
    }
} 