<?php
// app/Models/Auth/LoginHistory.php
namespace App\Models\Auth;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\User;
use App\Traits\LogsDatabaseChanges;

/**
 * Class LoginHistory
 *
 * Comprehensive login tracking with device fingerprinting and security analysis.
 * Records every login attempt with detailed information for security monitoring.
 *
 * @property int $id
 * @property int|null $user_id Reference to users table
 * @property int|null $role_id Active role for this session
 * @property int|null $department_id Active department for this session
 * @property string|null $entity_id Active entity for this session
 * @property bool $is_cross_entity_access Whether this session involves cross-entity access
 * @property string|null $original_entity_id Original entity ID for cross-entity access
 * @property int|null $original_role_id Original role ID for cross-entity access
 * @property string $ip_address IPv4 or IPv6 address
 * @property string|null $asn Autonomous System Number
 * @property string|null $isp Internet Service Provider
 * @property string|null $connection_type Connection type (cellular, broadband, etc.)
 * @property bool $is_proxy True if proxy/VPN/Tor detected
 * @property string $user_agent Raw HTTP User-Agent header
 * @property string|null $device_type Device category (mobile, tablet, desktop, etc.)
 * @property string|null $device_brand Device manufacturer
 * @property string|null $device_model Specific device model
 * @property bool $is_bot True if automated bot detected
 * @property bool $is_headless True if headless browser detected
 * @property string|null $platform Operating system
 * @property string|null $platform_version OS version
 * @property string|null $browser Browser name
 * @property string|null $browser_version Browser version
 * @property string|null $session_id Laravel session identifier
 * @property string|null $session_label User-defined label for this session
 * @property string|null $transferred_from Session ID this session was transferred from
 * @property string|null $transferred_to Session ID this session was transferred to
 * @property string|null $device_fingerprint Unique fingerprint for device identification
 * @property bool $fingerprint_changed Whether fingerprint changed during session
 * @property string|null $country Country name
 * @property string|null $country_code ISO country code
 * @property string|null $region State/region
 * @property string|null $city City name
 * @property string|null $timezone Timezone identifier
 * @property float|null $latitude GPS latitude
 * @property float|null $longitude GPS longitude
 * @property bool $suspicious_location True if unusual location
 * @property bool $new_device True if device not seen before
 * @property bool $unusual_timing True if outside normal hours
 * @property string|null $threat_level Risk assessment (low|medium|high|critical)
 * @property array|null $risk_factors Array of risk indicators
 * @property \Carbon\Carbon $login_at Authentication timestamp
 * @property int|null $login_hour Hour of day (0-23)
 * @property string|null $login_day Weekday name
 * @property string|null $login_origin URL or service
 * @property \Carbon\Carbon|null $logout_at Session end timestamp
 * @property int|null $duration_seconds Session duration
 * @property string|null $logout_reason Termination reason
 * @property string|null $login_method Authentication method used
 * @property string|null $guard Laravel auth guard
 * @property bool $login_successful Authentication result
 * @property string|null $failure_reason Why login failed
 * @property bool $mfa_used Multi-factor auth used
 * @property string|null $security_action System response taken
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class LoginHistory extends Model
{
    use SoftDeletes, HasFactory, LogsDatabaseChanges;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'role_id',
        'department_id',
        'entity_id',
        'is_cross_entity_access',
        'original_entity_id',
        'original_role_id',
        'ip_address',
        'asn',
        'isp',
        'connection_type',
        'is_proxy',
        'user_agent',
        'device_type',
        'device_brand',
        'device_model',
        'is_bot',
        'is_headless',
        'platform',
        'platform_version',
        'browser',
        'browser_version',
        'session_id',
        'session_label',
        'transferred_from',
        'transferred_to',
        'is_transferred',
        'device_fingerprint',
        'fingerprint_changed',
        'country',
        'country_code',
        'region',
        'city',
        'timezone',
        'latitude',
        'longitude',
        'suspicious_location',
        'new_device',
        'unusual_timing',
        'threat_level',
        'risk_factors',
        'login_at',
        'login_hour',
        'login_day',
        'login_origin',
        'logout_at',
        'duration_seconds',
        'logout_reason',
        'login_method',
        'guard',
        'login_successful',
        'failure_reason',
        'mfa_used',
        'security_action',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_proxy' => 'boolean',
        'is_bot' => 'boolean',
        'is_headless' => 'boolean',
        'suspicious_location' => 'boolean',
        'new_device' => 'boolean',
        'unusual_timing' => 'boolean',
        'risk_factors' => 'array',
        'login_at' => 'datetime',
        'logout_at' => 'datetime',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'login_successful' => 'boolean',
        'mfa_used' => 'boolean',
        'login_hour' => 'integer',
        'duration_seconds' => 'integer',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
        'is_cross_entity_access' => 'boolean',
        'fingerprint_changed' => 'boolean',
        'risk_score' => 'integer',
        'is_transferred' => 'boolean',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = ['risk_score', 'session_duration_formatted'];

    /**
     * Calculate risk score based on various factors.
     *
     * @return int Score from 0-100
     */
    public function getRiskScoreAttribute(): int
    {
        $score = 0;

        if ($this->is_proxy) $score += 20;
        if ($this->is_bot) $score += 30;
        if ($this->is_headless) $score += 25;
        if ($this->suspicious_location) $score += 15;
        if ($this->new_device) $score += 10;
        if ($this->unusual_timing) $score += 10;

        // Additional factors from risk_factors array
        if ($this->risk_factors) {
            $score += count($this->risk_factors) * 5;
        }

        return min($score, 100);
    }

    /**
     * Get formatted session duration.
     *
     * @return string|null
     */
    public function getSessionDurationFormattedAttribute(): ?string
    {
        if (!$this->duration_seconds) {
            return null;
        }

        $hours = floor($this->duration_seconds / 3600);
        $minutes = floor(($this->duration_seconds % 3600) / 60);
        $seconds = $this->duration_seconds % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Check if this login is high risk.
     *
     * @return bool
     */
    public function isHighRisk(): bool
    {
        return $this->threat_level === 'high' ||
            $this->threat_level === 'critical' ||
            $this->risk_score >= 70;
    }

    /**
     * Check if login came from a mobile device.
     *
     * @return bool
     */
    public function isMobileDevice(): bool
    {
        return in_array($this->device_type, ['mobile', 'tablet']);
    }

    /**
     * Get location string.
     *
     * @return string
     */
    public function getLocationString(): string
    {
        $parts = array_filter([
            $this->city,
            $this->region,
            $this->country
        ]);

        return implode(', ', $parts) ?: 'Unknown';
    }

    /**
     * Get the session this session was transferred from.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function originalSession(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        \Log::debug('=== LoginHistory@originalSession relation accessed ===');
        return $this->belongsTo(LoginHistory::class, 'transferred_from', 'session_id');
    }

    /**
     * Get the session this session was transferred to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function transferredSession(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        \Log::debug('=== LoginHistory@transferredSession relation accessed ===');
        return $this->hasOne(LoginHistory::class, 'transferred_from', 'session_id');
    }

    /**
     * Check if this session was transferred.
     *
     * @return bool
     */
    public function isTransferred(): bool
    {
        \Log::debug('=== LoginHistory@isTransferred called ===');
        $result = !is_null($this->transferred_from) || !is_null($this->transferred_to);
        \Log::debug('Result: ' . ($result ? 'true' : 'false'));
        \Log::debug('transferred_from: ' . ($this->transferred_from ?? 'null'));
        \Log::debug('transferred_to: ' . ($this->transferred_to ?? 'null'));
        return $result;
    }

    /**
     * Check if this session was transferred to another device.
     *
     * @return bool
     */
    public function wasTransferredAway(): bool
    {
        return !empty($this->transferred_to);
    }

    /**
     * Check if this session involves cross-entity access.
     *
     * @return bool
     */
    public function isCrossEntityAccess(): bool
    {
        return $this->is_cross_entity_access;
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the user associated with this login.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to successful logins only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSuccessful($query)
    {
        return $query->where('login_successful', true);
    }

    /**
     * Scope to failed logins only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('login_successful', false);
    }

    /**
     * Scope to suspicious logins.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSuspicious($query)
    {
        return $query->where(function ($q) {
            $q->where('suspicious_location', true)
                ->orWhere('is_proxy', true)
                ->orWhere('is_bot', true)
                ->orWhereIn('threat_level', ['high', 'critical']);
        });
    }

    /**
     * Scope to logins from new devices.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNewDevices($query)
    {
        return $query->where('new_device', true);
    }

    /**
     * Scope to logins from mobile devices.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMobile($query)
    {
        return $query->whereIn('device_type', ['mobile', 'tablet']);
    }

    /**
     * Scope to logins from a specific IP.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $ip
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFromIp($query, string $ip)
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * Scope to logins from a specific country.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $countryCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFromCountry($query, string $countryCode)
    {
        return $query->where('country_code', $countryCode);
    }

    /**
     * Scope to recent logins.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $hours
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('login_at', '>', now()->subHours($hours));
    }

    /**
     * Scope to active sessions (not logged out).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActiveSessions($query)
    {
        \Log::debug('=== LoginHistory@scopeActiveSessions ===');
        $result = $query->whereNull('logout_at')
            ->where('login_successful', true)
            ->where(function($query) {
                // Exclude sessions that have been transferred to another device
                // A transferred session should not be considered active anymore
                $query->whereNull('transferred_to')
                    ->orWhere('transferred_to', '');
            });
        \Log::debug('Query: ' . $result->toSql());
        \Log::debug('Bindings: ' . json_encode($result->getBindings()));
        return $result;
    }

    /**
     * Scope query to only include truly active sessions (not logged out and not transferred).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTrulyActiveSessions($query)
    {
        return $query->whereNull('logout_at')
            ->where('login_successful', true)
            ->where(function($query) {
                // Exclude sessions that have been transferred
                $query->whereNull('transferred_to')
                    ->orWhere('transferred_to', '');
            })
            ->where(function($query) {
                // Exclude sessions that were created by transfer
                $query->whereNull('transferred_from')
                    ->orWhere('transferred_from', '');
            });
    }

    /**
     * Scope query to only include sessions with cross-entity access.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCrossEntityAccess($query)
    {
        return $query->where('is_cross_entity_access', true);
    }

    /**
     * Scope query to only include transferred sessions.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTransferred($query)
    {
        return $query->whereNotNull('transferred_from');
    }

    /**
     * Scope query to only include sessions transferred away.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTransferredAway($query)
    {
        return $query->whereNotNull('transferred_to');
    }

    /**
     * Scope query to only include high-risk sessions.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $threshold Risk score threshold (default 70)
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHighRisk($query, int $threshold = 70)
    {
        return $query->where('risk_score', '>=', $threshold);
    }

    /**
     * Scope query to only include sessions with potential hijacking.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePotentialHijacking($query)
    {
        return $query->where('fingerprint_changed', true);
    }

    /**
     * Scope query to find sessions by device fingerprint.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $fingerprint
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByFingerprint($query, string $fingerprint)
    {
        return $query->where('device_fingerprint', $fingerprint);
    }

    /**
     * Scope query to find sessions with specific label.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $label
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByLabel($query, string $label)
    {
        return $query->where('session_label', 'like', "%$label%");
    }

    // ===== STATIC METHODS =====

    /**
     * Record a new login attempt.
     *
     * @param array $data
     * @return self
     */
    public static function recordLogin(array $data): self
    {
        $data['login_at'] = $data['login_at'] ?? now();
        $data['login_hour'] = $data['login_at']->hour;
        $data['login_day'] = $data['login_at']->format('l');

        return self::create($data);
    }

    /**
     * Get suspicious activity summary for a user.
     *
     * @param int $userId
     * @param int $days
     * @return array
     */
    public static function getSuspiciousActivity(int $userId, int $days = 30): array
    {
        $since = now()->subDays($days);

        $query = self::where('user_id', $userId)
            ->where('login_at', '>', $since);

        return [
            'total_logins' => $query->count(),
            'failed_logins' => $query->failed()->count(),
            'suspicious_logins' => $query->suspicious()->count(),
            'new_devices' => $query->newDevices()->count(),
            'unique_ips' => $query->distinct('ip_address')->count('ip_address'),
            'unique_countries' => $query->distinct('country_code')->count('country_code'),
            'proxy_usage' => $query->where('is_proxy', true)->count(),
            'high_risk_logins' => $query->whereIn('threat_level', ['high', 'critical'])->count(),
        ];
    }
}
