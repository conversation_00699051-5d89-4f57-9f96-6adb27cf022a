<?php
// app/Models/Auth/UserLoginStat.php
namespace App\Models\Auth;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Traits\LogsDatabaseChanges;

/**
 * Class UserLoginStat
 *
 * Stores aggregated login statistics per user per day.
 * 
 * @property int $id
 * @property int $user_id
 * @property \Carbon\Carbon $login_date
 * @property int $total_logins
 * @property int $successful_logins
 * @property int $failed_logins
 * @property int $suspicious_logins
 * @property int $new_device_logins
 * @property int $unique_ip_count
 * @property int $unique_device_count
 * @property int $unique_location_count
 * @property int|null $avg_session_duration
 * @property int|null $median_session_duration
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class UserLoginStat extends Model
{
    use SoftDeletes, LogsDatabaseChanges;
    
    protected $fillable = [
        'user_id',
        'login_date',
        'total_logins',
        'successful_logins',
        'failed_logins',
        'suspicious_logins',
        'new_device_logins',
        'unique_ip_count',
        'unique_device_count',
        'unique_location_count',
        'avg_session_duration',
        'median_session_duration',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    protected $casts = [
        'login_date' => 'date',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
