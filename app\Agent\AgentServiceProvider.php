<?php

namespace App\Agent;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Http\Request;

class AgentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $configPath = __DIR__ . '/../../config/agent.php';
        
        // Check if the config file exists
        if (file_exists($configPath)) {
            $this->mergeConfigFrom($configPath, 'agent');
        }

        $this->app->singleton('agent', function ($app) {
            return new Agent(
                $app['request']->server(),
                $app['config']->get('agent')
            );
        });

        $this->app->alias('agent', Agent::class);
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/../../config/agent.php' => config_path('agent.php'),
            ], 'agent-config');
        }

        // Standard Blade directives for basic device checks
        Blade::directive('ifAgent', function ($expression) {
            return "<?php if (app('agent')->is{$expression}()): ?>";
        });

        Blade::directive('endifAgent', function () {
            return "<?php endif; ?>";
        });
        
        // Additional Blade directives for common device checks
        Blade::if('mobile', function () {
            return app('agent')->isMobile();
        });
        
        Blade::if('tablet', function () {
            return app('agent')->isTablet();
        });
        
        Blade::if('desktop', function () {
            return app('agent')->isDesktop();
        });
        
        Blade::if('bot', function () {
            return app('agent')->isBot();
        });
        
        // Request macro
        Request::macro('agent', function () {
            return app('agent');
        });
    }
}
