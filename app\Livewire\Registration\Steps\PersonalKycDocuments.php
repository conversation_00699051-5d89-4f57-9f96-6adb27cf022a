<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Auth\RegistrationAttempt;

class PersonalKycDocuments extends Component
{
    use WithFileUploads;

    public $attempt;
    public $identityDocument;
    public $addressProof;
    public $existingDocuments = [];
    
    // New properties for document metadata
    public $identityExpiry = null;
    public $identityVerificationStatus = 'pending';
    public $addressExpiry = null;
    public $addressVerificationStatus = 'pending';

    protected $rules = [
        'identityDocument' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
        'addressProof' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
    ];

    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        if (!$this->attempt) {
            return redirect()->route('register');
        }
        
        // Load existing documents
        if (!empty($this->attempt->personal_kyc_documents)) {
            $this->existingDocuments = $this->attempt->personal_kyc_documents;
            
            // Load expiry dates and verification statuses
            foreach ($this->existingDocuments as $doc) {
                if (isset($doc['type'])) {
                    if ($doc['type'] === 'identity') {
                        $this->identityExpiry = $doc['expiry_date'] ?? null;
                        $this->identityVerificationStatus = $doc['verification_status'] ?? 'pending';
                    } elseif ($doc['type'] === 'address') {
                        $this->addressExpiry = $doc['expiry_date'] ?? null;
                        $this->addressVerificationStatus = $doc['verification_status'] ?? 'pending';
                    }
                }
            }
        }
    }

    public function uploadIdentityDocument()
    {
        $this->validate([
            'identityDocument' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240',
            'identityExpiry' => 'nullable|date'
        ]);

        $path = $this->identityDocument->store('kyc_documents/personal', 'public');
        
        // Update existing documents array
        $existingDocs = $this->existingDocuments ?? [];
        
        // Add or update identity document
        $identityDocIndex = null;
        foreach ($existingDocs as $index => $doc) {
            if (isset($doc['type']) && $doc['type'] === 'identity') {
                $identityDocIndex = $index;
                break;
            }
        }
        
        $docData = [
            'type' => 'identity',
            'filename' => $path,
            'original_name' => $this->identityDocument->getClientOriginalName(),
            'uploaded_at' => now()->format('Y-m-d H:i:s'),
            'expiry_date' => $this->identityExpiry,
            'verification_status' => $this->identityVerificationStatus
        ];
        
        if ($identityDocIndex !== null) {
            $existingDocs[$identityDocIndex] = $docData;
        } else {
            $existingDocs[] = $docData;
        }
        
        // Save to registration attempt
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['personal_kyc_documents'] = $existingDocs;
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'personal_kyc_documents' => $existingDocs // Using the new column
        ]);
        
        $this->existingDocuments = $existingDocs;
        $this->identityDocument = null;
        
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Identity document uploaded successfully!'
        ]);
    }
    
    public function uploadAddressProof()
    {
        $this->validate([
            'addressProof' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240',
            'addressExpiry' => 'nullable|date'
        ]);

        $path = $this->addressProof->store('kyc_documents/personal', 'public');
        
        // Update existing documents array
        $existingDocs = $this->existingDocuments ?? [];
        
        // Add or update address document
        $addressDocIndex = null;
        foreach ($existingDocs as $index => $doc) {
            if (isset($doc['type']) && $doc['type'] === 'address') {
                $addressDocIndex = $index;
                break;
            }
        }
        
        $docData = [
            'type' => 'address',
            'filename' => $path,
            'original_name' => $this->addressProof->getClientOriginalName(),
            'uploaded_at' => now()->format('Y-m-d H:i:s'),
            'expiry_date' => $this->addressExpiry,
            'verification_status' => $this->addressVerificationStatus
        ];
        
        if ($addressDocIndex !== null) {
            $existingDocs[$addressDocIndex] = $docData;
        } else {
            $existingDocs[] = $docData;
        }
        
        // Save to registration attempt
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['personal_kyc_documents'] = $existingDocs;
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'personal_kyc_documents' => $existingDocs // Using the new column
        ]);
        
        $this->existingDocuments = $existingDocs;
        $this->addressProof = null;
        
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Address proof uploaded successfully!'
        ]);
    }

    public function removeDocument($type)
    {
        // \Log::debug("PersonalKycDocuments::removeDocument - Starting with type: {$type}");
        
        try {
            // Refresh the model to get the latest state
            $this->attempt->refresh();
            
            $documents = $this->attempt->personal_kyc_documents ?? [];
            // \Log::debug("PersonalKycDocuments::removeDocument - Current documents before removal: " . json_encode($documents));
            
            // Find and remove the document of the specified type
            $newDocuments = [];
            $removed = false;
            
            foreach ($documents as $doc) {
                if (!isset($doc['type']) || $doc['type'] !== $type) {
                    $newDocuments[] = $doc;
                } else {
                    $removed = true;
                    // \Log::debug("PersonalKycDocuments::removeDocument - Removing document of type '{$type}'");
                }
            }
            
            if ($removed) {
                // \Log::debug("PersonalKycDocuments::removeDocument - Documents after removal: " . json_encode($newDocuments));
                
                // Update registration attempt
                $this->attempt->update([
                    'personal_kyc_documents' => $newDocuments,
                ]);
                // \Log::debug("PersonalKycDocuments::removeDocument - Registration attempt updated with filtered documents");
                
                // Refresh existing documents
                $this->existingDocuments = $newDocuments;
                // \Log::debug("PersonalKycDocuments::removeDocument - Refreshed existingDocuments property");
                
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => 'Document removed successfully!'
                ]);
                
                // \Log::debug("PersonalKycDocuments::removeDocument - Success notification dispatched");
            } else {
                // \Log::warning("PersonalKycDocuments::removeDocument - No document found with type '{$type}'");
                
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Document not found!'
                ]);
            }
        } catch (\Exception $e) {
            // \Log::error("PersonalKycDocuments::removeDocument - Error: " . $e->getMessage());
            // \Log::error("PersonalKycDocuments::removeDocument - Stack trace: " . $e->getTraceAsString());
            
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Error removing document: ' . $e->getMessage()
            ]);
        }
        
        // \Log::debug('PersonalKycDocuments::removeDocument - Completed');
    }
    
    public function completeStep()
    {
        // \Log::debug('PersonalKycDocuments::completeStep - Starting');
        
        try {
            if (!$this->attempt) {
                // \Log::error("PersonalKycDocuments::completeStep - Registration attempt not found");
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Registration session not found. Please try again.'
                ]);
                return;
            }
            
            // Check if the user has uploaded required documents
            $documents = $this->existingDocuments;
            // \Log::debug("PersonalKycDocuments::completeStep - Checking documents: " . json_encode($this->existingDocuments));
            
            $hasIdentity = false;
            $hasAddress = false;
            
            foreach ($documents as $doc) {
                if (isset($doc['type'])) {
                    if ($doc['type'] === 'identity') {
                        $hasIdentity = true;
                    } elseif ($doc['type'] === 'address') {
                        $hasAddress = true;
                    }
                }
            }
            
            // \Log::info("PersonalKycDocuments::completeStep - Document Status - Has Identity: " . ($hasIdentity ? 'Yes' : 'No') . 
            //     ", Has Address: " . ($hasAddress ? 'Yes' : 'No'));
            
            if (!$hasIdentity || !$hasAddress) {
                // \Log::warning("PersonalKycDocuments::completeStep - Missing required documents. Identity: " . ($hasIdentity ? 'Yes' : 'No') . ", Address: " . ($hasAddress ? 'Yes' : 'No'));
                
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Please upload both identity document and address proof before proceeding.'
                ]);
                return;
            }
            
            // Update completed stages
            $completedStages = $this->attempt->completed_stages ?? [];
            
            if (!is_array($completedStages)) {
                // \Log::warning("PersonalKycDocuments::completeStep - completed_stages is not an array, resetting to empty array");
                $completedStages = [];
            }
            
            // \Log::debug("PersonalKycDocuments::completeStep - Current completed stages: " . json_encode($completedStages));
            
            // Mark this step as complete if not already
            if (!in_array('personal_kyc_documents', $completedStages)) {
                $completedStages[] = 'personal_kyc_documents';
                // \Log::debug("PersonalKycDocuments::completeStep - Added 'personal_kyc_documents' to completed stages");
            }
            
            // Update registration attempt
            $this->attempt->update([
                'current_stage' => 'review_submit',
                'completed_stages' => $completedStages
            ]);
            
            // \Log::info("PersonalKycDocuments::completeStep - Updating registration attempt - Current stage: review_submit, Completed stages: " . json_encode($completedStages));
            
            // \Log::debug("PersonalKycDocuments::completeStep - Registration attempt updated successfully");
            
            // Notify parent component that this step is complete
            $this->dispatch('stepCompleted', 'personal-kyc-documents');
            // \Log::info("PersonalKycDocuments::completeStep - Dispatched stepCompleted event");
            
        } catch (\Exception $e) {
            // \Log::error('PersonalKycDocuments::completeStep - Error: ' . $e->getMessage());
            // \Log::error('PersonalKycDocuments::completeStep - Stack trace: ' . $e->getTraceAsString());
            
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Error completing step: ' . $e->getMessage()
            ]);
        }
        
        // \Log::debug('PersonalKycDocuments::completeStep - Completed');
    }
    
    public function goBack()
    {
        // \Log::debug('PersonalKycDocuments::goBack - Dispatching stepBack event');
        $this->dispatch('stepBack');
    }
    
    public function render()
    {
        // \Log::debug('PersonalKycDocuments::render - Rendering component');
        return view('livewire.registration.steps.personal-kyc-documents');
    }
}