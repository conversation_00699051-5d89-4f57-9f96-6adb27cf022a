<div>
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">Personal KYC Documents</h2>
        <p class="text-gray-600">
            Please upload your personal identification and address proof documents to verify your identity.
        </p>
    </div>

    <!-- Debug Information (visible only in development) -->
    @if(config('app.debug'))
        <div class="mb-4 p-3 bg-gray-100 border border-gray-300 rounded text-xs font-mono">
            <h3 class="font-bold mb-1">Debug Info:</h3>
            <p>existingDocuments count: {{ is_array($existingDocuments) ? count($existingDocuments) : 'not an array' }}</p>
            <p>existingDocuments data: <pre>{{ json_encode($existingDocuments, JSON_PRETTY_PRINT) }}</pre></p>
            <p>Registration attempt ID: {{ $attempt->id ?? 'not set' }}</p>
            <p>Current stage: {{ $attempt->current_stage ?? 'not set' }}</p>
            <p>Completed stages: <pre>{{ json_encode($attempt->completed_stages ?? [], JSON_PRETTY_PRINT) }}</pre></p>
            
            <div class="mt-3 p-2 bg-blue-100 border border-blue-300 rounded">
                <p class="font-bold">Debug Actions:</p>
                <div class="flex space-x-2 mt-1">
                    <a href="{{ route('entity.register', ['step' => 9]) }}" class="px-2 py-1 bg-blue-500 text-white rounded text-xs">Go to Step 9</a>
                    <button onclick="@this.completeStep()" class="px-2 py-1 bg-green-500 text-white rounded text-xs">Call completeStep()</button>
                    <button onclick="window.location.reload()" class="px-2 py-1 bg-gray-500 text-white rounded text-xs">Reload Page</button>
                </div>
            </div>
        </div>
    @endif

    <!-- Identity Document Section -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold mb-2">Identity Document</h3>
        <p class="text-gray-600 mb-4">
            Please upload a valid government-issued photo ID (passport, driver's license, or national ID card).
        </p>

        @php
            $hasIdentityDoc = false;
            $identityDoc = null;
            if (is_array($existingDocuments)) {
                foreach($existingDocuments as $doc) {
                    if(isset($doc['type']) && $doc['type'] === 'identity') {
                        $hasIdentityDoc = true;
                        $identityDoc = $doc;
                        break;
                    }
                }
            }
        @endphp

        @if($hasIdentityDoc)
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 mb-4">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="font-medium">Identity Document</p>
                        <p class="text-sm text-gray-500">{{ $identityDoc['original_name'] }}</p>
                        <p class="text-xs text-gray-400">Uploaded: {{ $identityDoc['uploaded_at'] }}</p>
                    </div>
                    <div class="flex space-x-2">
                        <a href="{{ Storage::url($identityDoc['filename']) }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </a>
                        <button wire:click="removeDocument('identity')" class="text-red-600 hover:text-red-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        @else
            <div class="bg-white rounded-lg p-6 border border-gray-200 mb-4">
                <form wire:submit.prevent="uploadIdentityDocument">
                    <div class="mb-4">
                        <label for="identityDocument" class="block text-sm font-medium text-gray-700 mb-1">Upload Identity Document</label>
                        <input type="file" id="identityDocument" wire:model="identityDocument" class="block w-full text-sm text-gray-500
                            file:mr-4 file:py-2 file:px-4
                            file:rounded-md file:border-0
                            file:text-sm file:font-semibold
                            file:bg-blue-50 file:text-blue-700
                            hover:file:bg-blue-100">
                        @error('identityDocument') <span class="text-red-500 text-xs mt-1">{{ $message }}</span> @enderror
                        <p class="mt-1 text-xs text-gray-500">Accepted formats: PDF, JPG, PNG (max 10MB)</p>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Upload Document
                        </button>
                    </div>
                </form>
                
                <div class="mt-4 border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date (Optional)</label>
                            <input 
                                type="date"
                                wire:model="identityExpiry"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            >
                            @error('identityExpiry') <span class="text-red-500 text-xs mt-1">{{ $message }}</span> @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Verification Status</label>
                            <select 
                                wire:model="identityVerificationStatus"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                                disabled
                            >
                                <option value="pending">Pending Verification</option>
                                <option value="verified">Verified</option>
                                <option value="rejected">Rejected</option>
                                <option value="expired">Expired</option>
                            </select>
                            <p class="mt-1 text-xs text-gray-500">Status will be updated after review</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Address Proof Section -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold mb-2">Proof of Address</h3>
        <p class="text-gray-600 mb-4">
            Please upload a document showing your current residential address (utility bill, bank statement, etc.) issued within the last 3 months.
        </p>

        @php
            $hasAddressDoc = false;
            $addressDoc = null;
            if (is_array($existingDocuments)) {
                foreach($existingDocuments as $doc) {
                    if(isset($doc['type']) && $doc['type'] === 'address') {
                        $hasAddressDoc = true;
                        $addressDoc = $doc;
                        break;
                    }
                }
            }
        @endphp

        @if($hasAddressDoc)
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 mb-4">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="font-medium">Proof of Address</p>
                        <p class="text-sm text-gray-500">{{ $addressDoc['original_name'] }}</p>
                        <p class="text-xs text-gray-400">Uploaded: {{ $addressDoc['uploaded_at'] }}</p>
                    </div>
                    <div class="flex space-x-2">
                        <a href="{{ Storage::url($addressDoc['filename']) }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </a>
                        <button wire:click="removeDocument('address')" class="text-red-600 hover:text-red-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        @else
            <div class="bg-white rounded-lg p-6 border border-gray-200 mb-4">
                <form wire:submit.prevent="uploadAddressProof">
                    <div class="mb-4">
                        <label for="addressProof" class="block text-sm font-medium text-gray-700 mb-1">Upload Proof of Address</label>
                        <input type="file" id="addressProof" wire:model="addressProof" class="block w-full text-sm text-gray-500
                            file:mr-4 file:py-2 file:px-4
                            file:rounded-md file:border-0
                            file:text-sm file:font-semibold
                            file:bg-blue-50 file:text-blue-700
                            hover:file:bg-blue-100">
                        @error('addressProof') <span class="text-red-500 text-xs mt-1">{{ $message }}</span> @enderror
                        <p class="mt-1 text-xs text-gray-500">Accepted formats: PDF, JPG, PNG (max 10MB)</p>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            Upload Document
                        </button>
                    </div>
                </form>
                
                <div class="mt-4 border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date (Optional)</label>
                            <input 
                                type="date"
                                wire:model="addressExpiry"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            >
                            @error('addressExpiry') <span class="text-red-500 text-xs mt-1">{{ $message }}</span> @enderror
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Verification Status</label>
                            <select 
                                wire:model="addressVerificationStatus"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                                disabled
                            >
                                <option value="pending">Pending Verification</option>
                                <option value="verified">Verified</option>
                                <option value="rejected">Rejected</option>
                                <option value="expired">Expired</option>
                            </select>
                            <p class="mt-1 text-xs text-gray-500">Status will be updated after review</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Document Status Summary -->
    <div class="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 class="text-lg font-semibold mb-2">Document Status</h3>
        <ul class="list-disc pl-5">
            <li class="mb-1">
                Identity Document: 
                <span class="{{ $hasIdentityDoc ? 'text-green-600 font-medium' : 'text-red-600' }}">
                    {{ $hasIdentityDoc ? 'Uploaded' : 'Not uploaded' }}
                </span>
            </li>
            <li>
                Proof of Address: 
                <span class="{{ $hasAddressDoc ? 'text-green-600 font-medium' : 'text-red-600' }}">
                    {{ $hasAddressDoc ? 'Uploaded' : 'Not uploaded' }}
                </span>
            </li>
        </ul>
        <p class="mt-2 text-sm text-gray-600">
            {{ ($hasIdentityDoc && $hasAddressDoc) ? 'All required documents are uploaded. You can proceed to the next step.' : 'Please upload all required documents before proceeding.' }}
        </p>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
        <button wire:click="goBack" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
            Previous Step
        </button>
        
        @if($hasIdentityDoc && $hasAddressDoc)
            <button 
                wire:click="completeStep" 
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                Next Step
            </button>
        @else
            <button class="px-4 py-2 bg-blue-600 text-white rounded-md opacity-50 cursor-not-allowed" disabled>
                Next Step
            </button>
        @endif
    </div>
</div> 