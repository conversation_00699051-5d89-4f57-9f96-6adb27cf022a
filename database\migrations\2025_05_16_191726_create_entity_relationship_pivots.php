<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Addressables - for both users and entities
        Schema::create('addressables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('address_id')->constrained()->onDelete('cascade');
            $table->string('addressable_type'); // Will store 'App\Models\User' or 'App\Models\Entity\Entity'
            $table->unsignedBigInteger('addressable_id'); // User ID or Entity ID
            $table->boolean('is_primary')->default(false);
            $table->timestamps();

            // Compound index for better performance
            $table->index(['addressable_type', 'addressable_id']);
            $table->index(['address_id', 'addressable_type']);
        });

        // Contactables - for both users and entities
        Schema::create('contactables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contact_id')->constrained()->onDelete('cascade');
            $table->string('contactable_type');
            $table->unsignedBigInteger('contactable_id');
            $table->boolean('is_primary')->default(false);
            $table->timestamps();

            $table->index(['contactable_type', 'contactable_id']);
            $table->index(['contact_id', 'contactable_type']);
        });

        // Kycables - for both users and entities
        Schema::create('kycables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('kyc_id')->constrained()->onDelete('cascade');
            $table->string('kycable_type');
            $table->unsignedBigInteger('kycable_id');
            $table->boolean('is_primary')->default(false);
            $table->timestamps();

            $table->index(['kycable_type', 'kycable_id']);
            $table->index(['kyc_id', 'kycable_type']);
        });

        // Taxables - for both users and entities
        Schema::create('taxables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tax_id')->constrained()->onDelete('cascade');
            $table->string('taxable_type');
            $table->unsignedBigInteger('taxable_id');
            $table->boolean('is_primary')->default(false);
            $table->timestamps();

            $table->index(['taxable_type', 'taxable_id']);
            $table->index(['tax_id', 'taxable_type']);
        });

        // Notifiables - for both users and entities
        Schema::create('notifiables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('notification_id')->constrained()->onDelete('cascade');
            $table->string('notifiable_type');
            $table->unsignedBigInteger('notifiable_id');
            $table->boolean('is_primary')->default(false);
            $table->timestamps();

            $table->index(['notifiable_type', 'notifiable_id']);
            $table->index(['notification_id', 'notifiable_type']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('notifiables');
        Schema::dropIfExists('taxables');
        Schema::dropIfExists('kycables');
        Schema::dropIfExists('contactables');
        Schema::dropIfExists('addressables');
    }
};
