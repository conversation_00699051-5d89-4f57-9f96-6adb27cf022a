<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;
use App\Services\LoginTrackingService;
use App\Services\LoginValidationService;
use App\Services\DatabaseChangeLogger;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register LoginTrackingService
        $this->app->singleton(LoginTrackingService::class, function ($app) {
            return new LoginTrackingService();
        });
        
        // Register LoginValidationService
        $this->app->singleton(LoginValidationService::class, function ($app) {
            return new LoginValidationService();
        });
        
        // Register DatabaseChangeLogger
        $this->app->singleton(DatabaseChangeLogger::class, function ($app) {
            return new DatabaseChangeLogger();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register Flux view namespace for backward compatibility
        View::addNamespace('flux', resource_path('views/flux'));
        
        // Register Flux components as anonymous components for backward compatibility
        Blade::anonymousComponentPath(resource_path('views/flux'), 'flux');
    }
}
