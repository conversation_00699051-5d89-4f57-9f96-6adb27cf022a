<?php
// app/Models/Approval/ApprovalEscalation.php
namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Traits\HasAuditFields;

/**
 * Class ApprovalEscalation
 *
 * Tracks escalations when approval steps take too long.
 * Helps ensure approvals don't get stuck indefinitely.
 *
 * @property int $id
 * @property string $request_id UUID of approval request
 * @property int $step_number Which workflow step was escalated
 * @property int|null $escalated_to_user_id User escalation was sent to
 * @property string $reason Why escalation was triggered
 * @property string $status Current escalation status
 * @property \Carbon\Carbon $created_at When escalation was created
 * @property \Carbon\Carbon|null $updated_at When escalation was last updated
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class ApprovalEscalation extends Model
{
    use SoftDeletes, HasAuditFields;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'request_id',
        'step_number',
        'escalated_to_user_id',
        'reason',
        'status',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'step_number' => 'integer',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    /**
     * Available escalation statuses.
     */
    const STATUSES = [
        'pending' => 'Escalation sent, awaiting response',
        'acknowledged' => 'Escalation acknowledged by recipient',
        'resolved' => 'Escalation resolved - approval given',
        'cancelled' => 'Escalation cancelled',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = ['is_pending', 'is_resolved', 'elapsed_time'];

    /**
     * Check if escalation is still pending.
     *
     * @return bool
     */
    public function getIsPendingAttribute(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if escalation has been resolved.
     *
     * @return bool
     */
    public function getIsResolvedAttribute(): bool
    {
        return $this->status === 'resolved';
    }

    /**
     * Get time elapsed since escalation.
     *
     * @return \Carbon\CarbonInterval
     */
    public function getElapsedTimeAttribute()
    {
        return $this->created_at->diffAsCarbonInterval(now());
    }

    /**
     * Get human-readable status description.
     *
     * @return string
     */
    public function getStatusDescriptionAttribute(): string
    {
        return self::STATUSES[$this->status] ?? 'Unknown status';
    }

    /**
     * Mark escalation as acknowledged.
     *
     * @return bool
     */
    public function acknowledge(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }

        $this->status = 'acknowledged';
        return $this->save();
    }

    /**
     * Mark escalation as resolved.
     *
     * @return bool
     */
    public function resolve(): bool
    {
        if (!in_array($this->status, ['pending', 'acknowledged'])) {
            return false;
        }

        $this->status = 'resolved';
        return $this->save();
    }

    /**
     * Cancel the escalation.
     *
     * @return bool
     */
    public function cancel(): bool
    {
        if ($this->status === 'resolved') {
            return false;
        }

        $this->status = 'cancelled';
        return $this->save();
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the approval request this escalation belongs to.
     *
     * @return BelongsTo
     */
    public function request(): BelongsTo
    {
        return $this->belongsTo(ApprovalRequest::class, 'request_id');
    }

    /**
     * Get the user this escalation was sent to.
     *
     * @return BelongsTo
     */
    public function escalatedToUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'escalated_to_user_id');
    }

    /**
     * Get the workflow step that was escalated.
     *
     * @return BelongsTo
     */
    public function step(): BelongsTo
    {
        return $this->belongsTo(ApprovalStep::class, 'step_number', 'step_number')
            ->where('workflow_id', $this->request->workflow_id);
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to pending escalations.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to resolved escalations.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope to escalations for a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('escalated_to_user_id', $userId);
    }

    /**
     * Scope to recent escalations.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $hours
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>', now()->subHours($hours));
    }

    // ===== STATIC METHODS =====

    /**
     * Create an escalation for a delayed approval step.
     *
     * @param ApprovalRequest $request
     * @param int $stepNumber
     * @param int|null $escalateToUserId
     * @param string $reason
     * @return self
     */
    public static function createForDelayedStep(
        ApprovalRequest $request,
        int $stepNumber,
        ?int $escalateToUserId = null,
        string $reason = 'Step approval overdue'
    ): self {
        return self::create([
            'request_id' => $request->id,
            'step_number' => $stepNumber,
            'escalated_to_user_id' => $escalateToUserId,
            'reason' => $reason,
            'status' => 'pending',
        ]);
    }

    /**
     * Get escalation statistics for reporting.
     *
     * @param \Carbon\Carbon|null $from
     * @param \Carbon\Carbon|null $to
     * @return array
     */
    public static function getStatistics(
        ?\Carbon\Carbon $from = null,
        ?\Carbon\Carbon $to = null
    ): array {
        $query = self::query();

        if ($from) {
            $query->where('created_at', '>=', $from);
        }

        if ($to) {
            $query->where('created_at', '<=', $to);
        }

        $escalations = $query->get();

        return [
            'total_escalations' => $escalations->count(),
            'pending_escalations' => $escalations->where('status', 'pending')->count(),
            'resolved_escalations' => $escalations->where('status', 'resolved')->count(),
            'cancelled_escalations' => $escalations->where('status', 'cancelled')->count(),
            'average_resolution_time' => $escalations
                ->where('status', 'resolved')
                ->avg(function ($escalation) {
                    return $escalation->created_at->diffInHours($escalation->updated_at);
                }),
        ];
    }
}
