<?php
// app/Models/CodeGenerationAudit.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use App\Models\Entity\Entity;

/**
 * Class CodeGenerationAudit
 *
 * Audit trail for all code generation activities in the system.
 * Tracks who generated codes, when, from where, and under what circumstances.
 *
 * @property int $id
 * @property string $code_type The type of code generated (e.g., 'supplier', 'purchase_order')
 * @property string $generated_code The actual code that was generated (e.g., SUP-MH-0001)
 * @property string $action The action performed (generate|reset|preview|failed_attempt)
 * @property int|null $generated_by User ID who generated the code
 * @property int|null $entity_id Entity ID where the code was generated
 * @property string|null $ip_address IP address of the client (supports IPv6)
 * @property string|null $user_agent Browser/device information
 * @property array|null $metadata Additional context in JSON format
 * @property \Carbon\Carbon $created_at When the code was generated
 */
class CodeGenerationAudit extends Model
{
    /**
     * Disable automatic timestamp handling since we only use created_at.
     * We'll handle created_at manually in the fillable array.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'code_generation_audit';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'code_type',
        'generated_code',
        'action',
        'generated_by',
        'entity_id',
        'ip_address',
        'user_agent',
        'metadata',
        'created_at', // Manually handle this field
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * The attributes that should be appended.
     *
     * @var array<string>
     */
    protected $appends = ['is_successful', 'action_description'];

    /**
     * Boot method to handle created_at manually.
     */
    protected static function boot()
    {
        parent::boot();

        // Set created_at when creating a new record
        static::creating(function ($model) {
            if (!$model->created_at) {
                $model->created_at = now();
            }
        });
    }

    /**
     * Check if the code generation was successful.
     *
     * @return bool
     */
    public function getIsSuccessfulAttribute(): bool
    {
        return $this->action === 'generate';
    }

    /**
     * Get human-readable action description.
     *
     * @return string
     */
    public function getActionDescriptionAttribute(): string
    {
        return match ($this->action) {
            'generate' => 'Code generated successfully',
            'reset' => 'Sequence reset performed',
            'preview' => 'Preview requested',
            'failed_attempt' => 'Generation failed',
            default => 'Unknown action'
        };
    }

    /**
     * Check if this audit entry represents a security concern.
     *
     * @return bool
     */
    public function isSecurityConcern(): bool
    {
        // Multiple failed attempts from same IP
        if ($this->action === 'failed_attempt') {
            $recentFailures = self::where('ip_address', $this->ip_address)
                ->where('action', 'failed_attempt')
                ->where('created_at', '>', now()->subMinutes(5))
                ->count();

            return $recentFailures > 3;
        }

        // Reset action from non-admin user
        if ($this->action === 'reset' && $this->generated_by) {
            // This would check if user is admin once roles are implemented
            return false;
        }

        return false;
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the user who generated the code.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the entity where the code was generated.
     *
     * @return BelongsTo
     */
    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to successful generations only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSuccessful($query)
    {
        return $query->where('action', 'generate');
    }

    /**
     * Scope to failed attempts only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('action', 'failed_attempt');
    }

    /**
     * Scope to specific code type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('code_type', $type);
    }

    /**
     * Scope to specific user's activities.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('generated_by', $userId);
    }

    /**
     * Scope to specific entity's activities.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $entityId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByEntity($query, int $entityId)
    {
        return $query->where('entity_id', $entityId);
    }

    /**
     * Scope to specific IP address.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $ipAddress
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFromIp($query, string $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope to recent activities.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $minutes
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $minutes = 60)
    {
        return $query->where('created_at', '>', now()->subMinutes($minutes));
    }

    // ===== STATIC METHODS =====

    /**
     * Log a code generation activity.
     *
     * @param string $codeType
     * @param string $generatedCode
     * @param string $action
     * @param array $context Additional context data
     * @return self
     */
    public static function log(
        string $codeType,
        string $generatedCode,
        string $action = 'generate',
        array $context = []
    ): self {
        return self::create([
            'code_type' => $codeType,
            'generated_code' => $generatedCode,
            'action' => $action,
            'generated_by' => auth()->id(),
            'entity_id' => session('current_entity_id'),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'metadata' => $context,
        ]);
    }

    /**
     * Get generation statistics for a specific type.
     *
     * @param string $codeType
     * @param \Carbon\Carbon|null $from
     * @param \Carbon\Carbon|null $to
     * @return array
     */
    public static function getStatistics(
        string $codeType,
        ?\Carbon\Carbon $from = null,
        ?\Carbon\Carbon $to = null
    ): array {
        $query = self::ofType($codeType);

        if ($from) {
            $query->where('created_at', '>=', $from);
        }

        if ($to) {
            $query->where('created_at', '<=', $to);
        }

        return [
            'total_generated' => $query->successful()->count(),
            'failed_attempts' => $query->failed()->count(),
            'unique_users' => $query->distinct('generated_by')->count('generated_by'),
            'unique_entities' => $query->distinct('entity_id')->count('entity_id'),
            'last_generated' => $query->successful()->latest('created_at')->first()?->created_at,
        ];
    }
}
