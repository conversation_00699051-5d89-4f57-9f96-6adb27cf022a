<?php

namespace App\Livewire\Admin\Rbac\Roles;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Rbac\SystemRole;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;

#[Layout('components.admin.layout')]
class RoleIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $filterHierarchy = '';
    public $filterStatus = '';
    
    protected $queryString = [
        'search' => ['except' => ''],
        'filterHierarchy' => ['except' => ''],
        'filterStatus' => ['except' => '']
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function getRolesProperty()
    {
        $query = SystemRole::with(['entity', 'parentRole', 'userAssignments'])
            ->where('entity_id', Auth::user()->entity_id);

        if ($this->search) {
            $query->where(function($q) {
                $q->where('role_name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->filterHierarchy) {
            $query->where('hierarchy_level', $this->filterHierarchy);
        }

        if ($this->filterStatus) {
            if ($this->filterStatus === 'active') {
                $query->operational();
            } else {
                $query->where('is_active', false);
            }
        }

        return $query->orderBy('hierarchy_level')
                    ->orderBy('role_name')
                    ->paginate(15);
    }

    public function render()
    {
        return view('livewire.admin.rbac.roles.role-index', [
            'roles' => $this->roles
        ])->with([
            'heading' => __('Role Management'),
            'subheading' => __('Manage system roles and permissions'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Roles'), 'url' => route('admin.rbac.roles.index')]
            ]
        ]);
    }
}
