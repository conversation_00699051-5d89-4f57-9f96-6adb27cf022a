<div>
    <div class="max-w-lg mx-auto px-4 sm:px-0">
        <!-- Step Header -->
        <header class="text-center mb-6 sm:mb-8">
            <div class="inline-flex items-center justify-center w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 text-white mb-4 shadow-lg shadow-blue-500/30">
                <flux:icon name="mail" class="w-7 h-7 sm:w-8 sm:h-8" />
            </div>
            <h1 class="text-xl sm:text-2xl font-bold text-zinc-900 dark:text-white mb-2">Email Verification</h1>
            <p class="text-sm sm:text-base text-zinc-600 dark:text-zinc-400 px-4 sm:px-0">We'll send you a verification code to confirm your email address</p>
        </header>

        <!-- Success Message -->
        @if (session()->has('message'))
        <div class="mb-6 transition-all duration-300 ease-out">
            <flux:alert variant="success" class="rounded-xl">
                <flux:icon name="check-circle" class="h-5 w-5" />
                {{ session('message') }}
            </flux:alert>
        </div>
        @endif

        <!-- Error Message -->
        @if (session()->has('error'))
        <div class="mb-6 transition-all duration-300 ease-out">
            <flux:alert variant="danger" class="rounded-xl">
                <flux:icon name="exclamation-triangle" class="h-5 w-5" />
                {{ session('error') }}
            </flux:alert>
        </div>
        @endif

        <form wire:submit.prevent="{{ $showOtpField ? 'verifyOtp' : 'sendOtp' }}" class="space-y-6" aria-label="{{ __('Email Verification Form') }}">
            <!-- Email Input -->
            <div class="space-y-2">
                <flux:input
                    wire:model="email"
                    :label="__('Email Address')"
                    type="email"
                    id="email"
                    placeholder="<EMAIL>"
                    :disabled="$showOtpField"
                    class="{{ $showOtpField ? 'opacity-60' : '' }}"
                    required />
            </div>

            <!-- OTP Input (Conditional) -->
            @if($showOtpField)
            <div class="space-y-2 transition-all duration-300 ease-out">
                <flux:input
                    wire:model="otp"
                    :label="__('Verification Code')"
                    type="text"
                    id="otp"
                    placeholder="000000"
                    maxlength="6"
                    autocomplete="one-time-code"
                    class="text-center text-xl sm:text-2xl tracking-widest font-mono"
                    required />
            </div>

            <!-- Resend OTP -->
            <div class="flex items-center justify-between text-sm">
                <span class="text-zinc-600 dark:text-zinc-400">Didn't receive the code?</span>
                <flux:button
                    type="button"
                    wire:click="resendOtp"
                    variant="ghost"
                    size="sm"
                    class="font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    wire:loading.attr="disabled">
                    Resend OTP
                </flux:button>
            </div>
            @endif

            <!-- Submit Button -->
            <flux:button
                type="submit"
                variant="primary"
                class="w-full"
                wire:loading.attr="disabled">
                <span wire:loading wire:target="{{ $showOtpField ? 'verifyOtp' : 'sendOtp' }}">
                    <flux:icon name="loading" class="animate-spin h-5 w-5 mr-2" />
                    {{ __('Processing...') }}
                </span>
                <span wire:loading.remove wire:target="{{ $showOtpField ? 'verifyOtp' : 'sendOtp' }}">
                    @if($showOtpField)
                        <flux:icon name="check" class="w-5 h-5 mr-2" />
                        {{ __('Verify Code') }}
                    @else
                        <flux:icon name="mail" class="w-5 h-5 mr-2" />
                        {{ __('Send Verification Code') }}
                    @endif
                </span>
            </flux:button>
        </form>

        <!-- Info Message -->
        @if($otpSent)
        <div class="mt-6 text-center transition-all duration-300 ease-out">
            <div class="inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <flux:icon name="clock" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                <span class="text-sm font-medium text-blue-700 dark:text-blue-300">
                    Code expires in 30 minutes
                </span>
            </div>
        </div>
        @endif

        <!-- Security Note -->
        <footer class="mt-6 sm:mt-8 p-3 sm:p-4 bg-zinc-50 dark:bg-zinc-800/50 rounded-xl">
            <div class="flex items-start">
                <flux:icon name="shield-check" class="w-5 h-5 text-zinc-400 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" />
                <div class="text-sm text-zinc-600 dark:text-zinc-400">
                    <p class="font-medium mb-1">Your security is our priority</p>
                    <p class="text-xs">We'll never share your email address with third parties. The verification code ensures only you can access your account.</p>
                </div>
            </div>
        </footer>
    </div>
</div>