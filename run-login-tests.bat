@echo off
echo Running Login Tests Suite for Windows

REM Reset the testing environment
php artisan config:clear
php artisan cache:clear

REM Temporarily remove the banned_attempts migration if it exists
if exist database\migrations\2025_07_15_000001_create_banned_attempts_table.php (
  echo Moving banned_attempts migration out of the way for testing...
  rename database\migrations\2025_07_15_000001_create_banned_attempts_table.php banned_attempts_table.php.bak
)

REM Create a clean database for testing
echo Setting up fresh test database...
php artisan migrate:fresh --env=testing

REM Restore the banned_attempts migration file if we moved it
if exist banned_attempts_table.php.bak (
  move banned_attempts_table.php.bak database\migrations\2025_07_15_000001_create_banned_attempts_table.php
)

REM Run the individual test files with detailed output
echo.
echo Running Login Tests...
php artisan test tests/Feature/Auth/LoginTest.php --env=testing --testdox

echo.
echo Running Session Management Tests...
php artisan test tests/Feature/Auth/SessionManagementTest.php --env=testing --testdox

echo.
echo Running Agent Integration Tests...
php artisan test tests/Feature/Auth/AgentIntegrationTest.php --env=testing --testdox

echo.
echo Login Tests Complete! 