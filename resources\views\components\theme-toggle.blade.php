{{-- Theme Toggle Component - Enhanced Version --}}
{{-- resources/views/components/theme-toggle.blade.php --}}
<div class="flex items-center space-x-1 rounded-lg bg-zinc-100 p-1 dark:bg-zinc-800" role="group" aria-label="{{ __('Theme Selection') }}">
    <button
        type="button"
        class="flex h-8 w-8 items-center justify-center rounded-md text-zinc-600 hover:bg-white hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-700 dark:hover:text-zinc-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-100 dark:focus:ring-offset-zinc-800"
        title="{{ __('Light Mode') }}"
        aria-label="{{ __('Switch to Light Mode') }}"
        onclick="setTheme('light')">
        <flux:icon.sun class="h-4 w-4" />
    </button>

    <button
        type="button"
        class="flex h-8 w-8 items-center justify-center rounded-md text-zinc-600 hover:bg-white hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-700 dark:hover:text-zinc-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-100 dark:focus:ring-offset-zinc-800"
        title="{{ __('Dark Mode') }}"
        aria-label="{{ __('Switch to Dark Mode') }}"
        onclick="setTheme('dark')">
        <flux:icon.moon class="h-4 w-4" />
    </button>

    <button
        type="button"
        class="flex h-8 w-8 items-center justify-center rounded-md text-zinc-600 hover:bg-white hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-700 dark:hover:text-zinc-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-100 dark:focus:ring-offset-zinc-800"
        title="{{ __('System') }}"
        aria-label="{{ __('Use System Theme') }}"
        onclick="setTheme('system')">
        <flux:icon.computer-desktop class="h-4 w-4" />
    </button>
</div>

<script>
    function setTheme(theme) {
        localStorage.setItem('theme', theme);
        updateTheme();
    }

    function updateTheme() {
        const theme = localStorage.getItem('theme') || 'system';
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        const activeTheme = theme === 'system' ? systemTheme : theme;

        document.documentElement.classList.toggle('dark', activeTheme === 'dark');
    }

    // Initialize theme on load
    updateTheme();
</script>