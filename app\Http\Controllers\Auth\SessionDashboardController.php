<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Auth\LoginHistory;
use App\Services\LoginTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SessionDashboardController extends Controller
{
    protected LoginTrackingService $loginTracker;
    
    public function __construct(LoginTrackingService $loginTracker)
    {
        $this->loginTracker = $loginTracker;
    }
    
    /**
     * Display the session management dashboard.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        \Log::debug('=== SessionDashboardController@index START ===');
        \Log::debug('Current Session ID: ' . session()->getId());
        \Log::debug('Session Data: ' . json_encode(session()->all()));
        
        $user = Auth::user();
        \Log::debug('User: ' . json_encode($user->toArray()));
        
        $currentSessionId = session()->getId();
        \Log::debug('Current Session ID: ' . $currentSessionId);
        
        // Get active sessions - only include sessions that are truly active (no logout time)
        $activeSessionsQuery = LoginHistory::where('user_id', $user->id)
            ->whereNull('logout_at')  // Only sessions that haven't been logged out
            ->with(['originalSession', 'transferredSession']);
            
        \Log::debug('Active Sessions Query: ' . $activeSessionsQuery->toSql());
        \Log::debug('Active Sessions Query Bindings: ' . json_encode($activeSessionsQuery->getBindings()));
        
        $activeSessions = $activeSessionsQuery->orderBy('login_at', 'desc')->get();
        \Log::debug('Active Sessions Count: ' . $activeSessions->count());
        \Log::debug('Active Sessions: ' . json_encode($activeSessions->toArray()));
        
        // Check if current session is in the active sessions
        $currentSessionExists = $activeSessions->where('session_id', $currentSessionId)->count() > 0;
        \Log::debug('Current Session Exists: ' . ($currentSessionExists ? 'Yes' : 'No'));
        
        // Check if this session was previously transferred
        $wasTransferred = session()->has('transferred_from_session');
        \Log::debug('Was Transferred: ' . ($wasTransferred ? 'Yes' : 'No'));
        
        // Also check database for transferred sessions
        $wasTransferredInDb = LoginHistory::where('session_id', $currentSessionId)
            ->where('user_id', $user->id)
            ->where(function($query) {
                $query->whereNotNull('transferred_from')
                    ->orWhere('is_transferred', true);
            })
            ->exists();
        \Log::debug('Was Transferred in DB: ' . ($wasTransferredInDb ? 'Yes' : 'No'));
        
        // Only create a new login record if:
        // 1. Current session doesn't exist in active sessions
        // 2. AND it's not a transferred session (to avoid duplicates)
        if (!$currentSessionExists && !$wasTransferred && !$wasTransferredInDb) {
            \Log::debug('Creating new login record for current session');
            // Create a login record for the current session
            $loginHistory = $this->loginTracker->recordSuccessfulLogin(
                $request,
                $user->id,
                session('session_label', 'Current Session')
            );
            \Log::debug('New Login Record: ' . json_encode($loginHistory->toArray()));
            
            // Add to active sessions collection
            $activeSessions->prepend($loginHistory);
            \Log::debug('Updated Active Sessions Count: ' . $activeSessions->count());
        }
            
        // Get recent sessions (last 30 days) - include logged out and transferred sessions
        $recentSessionsQuery = LoginHistory::where('user_id', $user->id)
            ->whereNotNull('logout_at')  // Only sessions that have been logged out
            ->where('login_at', '>=', now()->subDays(30));
            
        \Log::debug('Recent Sessions Query: ' . $recentSessionsQuery->toSql());
        \Log::debug('Recent Sessions Query Bindings: ' . json_encode($recentSessionsQuery->getBindings()));
        
        $recentSessions = $recentSessionsQuery->orderBy('login_at', 'desc')->limit(20)->get();
        \Log::debug('Recent Sessions Count: ' . $recentSessions->count());
        
        // Get suspicious sessions
        $suspiciousSessionsQuery = LoginHistory::where('user_id', $user->id)
            ->where(function($query) {
                $query->where('fingerprint_changed', true)
                    ->orWhere('risk_score', '>=', 70)
                    ->orWhere('suspicious_location', true);
            });
            
        \Log::debug('Suspicious Sessions Query: ' . $suspiciousSessionsQuery->toSql());
        
        $suspiciousSessions = $suspiciousSessionsQuery->orderBy('login_at', 'desc')->limit(10)->get();
        \Log::debug('Suspicious Sessions Count: ' . $suspiciousSessions->count());
        
        // Get session statistics
        $stats = [
            'total_active' => $activeSessions->count(),
            'total_recent' => $recentSessions->count(),
            'total_suspicious' => $suspiciousSessions->count(),
            'avg_session_duration' => LoginHistory::where('user_id', $user->id)
                ->whereNotNull('duration_seconds')
                ->where('duration_seconds', '>', 0)  // Only include positive durations
                ->avg('duration_seconds'),
            'most_used_device' => LoginHistory::where('user_id', $user->id)
                ->selectRaw('device_type, COUNT(*) as count')
                ->groupBy('device_type')
                ->orderByDesc('count')
                ->first()?->device_type,
            'most_used_browser' => LoginHistory::where('user_id', $user->id)
                ->selectRaw('browser, COUNT(*) as count')
                ->groupBy('browser')
                ->orderByDesc('count')
                ->first()?->browser,
        ];
        \Log::debug('Session Stats: ' . json_encode($stats));
        
        \Log::debug('=== SessionDashboardController@index END ===');
        
        return view('auth.session-dashboard', [
            'activeSessions' => $activeSessions,
            'recentSessions' => $recentSessions,
            'suspiciousSessions' => $suspiciousSessions,
            'stats' => $stats,
            'currentSessionId' => $currentSessionId,
        ]);
    }
    
    /**
     * Terminate a session.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function terminateSession(Request $request)
    {
        \Log::debug('=== SessionDashboardController@terminateSession START ===');
        \Log::debug('Request Data: ' . json_encode($request->all()));
        \Log::debug('Current Session ID: ' . session()->getId());
        
        $request->validate([
            'session_id' => 'required|string',
        ]);
        
        $sessionId = $request->input('session_id');
        \Log::debug('Session ID to terminate: ' . $sessionId);
        
        $user = Auth::user();
        \Log::debug('User: ' . json_encode($user->toArray()));
        
        // Find the specified session
        $sessionQuery = LoginHistory::where('user_id', $user->id)
            ->where('session_id', $sessionId)
            ->whereNull('logout_at');
            
        \Log::debug('Session Query: ' . $sessionQuery->toSql());
        \Log::debug('Session Query Bindings: ' . json_encode($sessionQuery->getBindings()));
        
        $session = $sessionQuery->first();
        \Log::debug('Session Found: ' . ($session ? 'Yes' : 'No'));
        
        if ($session) {
            \Log::debug('Session Before Update: ' . json_encode($session->toArray()));
            
            // Calculate duration correctly using the database record's login_at
            $loginTime = $session->login_at;
            $now = now();
            $duration = max(0, $now->timestamp - $loginTime->timestamp);
            
            \Log::debug('Login Time: ' . $loginTime);
            \Log::debug('Now: ' . $now);
            \Log::debug('Duration (seconds): ' . $duration);
            
            // Update the session directly to ensure it's terminated
            $session->update([
                'logout_at' => $now,
                'logout_reason' => 'user_terminated',
                'duration_seconds' => $duration,
            ]);
            
            \Log::debug('Session After Update: ' . json_encode($session->fresh()->toArray()));
            
            // If terminating current session, log out the user
            if ($sessionId === session()->getId()) {
                \Log::debug('Terminating current session, logging out user');
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                \Log::debug('=== SessionDashboardController@terminateSession END ===');
                return redirect()->route('login')
                    ->with('status', 'Your session has been terminated.');
            }
            
            // If this session was transferred, also terminate any related sessions
            if ($session->transferred_to) {
                \Log::debug('This session was transferred to: ' . $session->transferred_to);
                
                // Find and terminate the transferred session
                $transferredSession = LoginHistory::where('session_id', $session->transferred_to)
                    ->where('user_id', $user->id)
                    ->whereNull('logout_at')
                    ->first();
                    
                if ($transferredSession) {
                    \Log::debug('Found transferred session: ' . json_encode($transferredSession->toArray()));
                    
                    // Calculate duration for transferred session
                    $transferredLoginTime = $transferredSession->login_at;
                    $transferredDuration = max(0, $now->timestamp - $transferredLoginTime->timestamp);
                    
                    $transferredSession->update([
                        'logout_at' => $now,
                        'logout_reason' => 'original_session_terminated',
                        'duration_seconds' => $transferredDuration,
                    ]);
                    
                    \Log::debug('Terminated transferred session');
                }
            }
            
            // If this session was created by transfer, also terminate the original session if still active
            if ($session->transferred_from) {
                \Log::debug('This session was transferred from: ' . $session->transferred_from);
                
                // Find and terminate the original session if it's still active
                $originalSession = LoginHistory::where('session_id', $session->transferred_from)
                    ->where('user_id', $user->id)
                    ->whereNull('logout_at')
                    ->first();
                    
                if ($originalSession) {
                    \Log::debug('Found original session still active: ' . json_encode($originalSession->toArray()));
                    
                    // Calculate duration for original session
                    $originalLoginTime = $originalSession->login_at;
                    $originalDuration = max(0, $now->timestamp - $originalLoginTime->timestamp);
                    
                    $originalSession->update([
                        'logout_at' => $now,
                        'logout_reason' => 'transferred_session_terminated',
                        'duration_seconds' => $originalDuration,
                    ]);
                    
                    \Log::debug('Terminated original session');
                }
            }
            
            \Log::debug('=== SessionDashboardController@terminateSession END ===');
            return redirect()->back()
                ->with('status', 'Session terminated successfully.');
        }
        
        \Log::debug('Session not found or already terminated');
        \Log::debug('=== SessionDashboardController@terminateSession END ===');
        
        return redirect()->back()
            ->with('error', 'Session not found or already terminated.');
    }
    
    /**
     * Update session label.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSessionLabel(Request $request)
    {
        \Log::debug('=== SessionDashboardController@updateSessionLabel START ===');
        \Log::debug('Request Data: ' . json_encode($request->all()));
        \Log::debug('Current Session ID: ' . session()->getId());
        
        $request->validate([
            'session_id' => 'required|string',
            'session_label' => 'required|string|max:255',
        ]);
        
        $sessionId = $request->input('session_id');
        $label = $request->input('session_label');
        $user = Auth::user();
        
        \Log::debug('Session ID: ' . $sessionId);
        \Log::debug('New Label: ' . $label);
        \Log::debug('User: ' . json_encode($user->toArray()));
        
        // Update the session label
        $success = $this->loginTracker->updateSessionLabel($user->id, $sessionId, $label);
        \Log::debug('Update Success: ' . ($success ? 'Yes' : 'No'));
        
        \Log::debug('=== SessionDashboardController@updateSessionLabel END ===');
        
        if ($success) {
            return redirect()->back()
                ->with('status', 'Session label updated successfully.');
        }
        
        return redirect()->back()
            ->with('error', 'Failed to update session label.');
    }
    
    /**
     * Create a session transfer code.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function createTransferCode(Request $request)
    {
        \Log::debug('=== SessionDashboardController@createTransferCode START ===');
        \Log::debug('Request Data: ' . json_encode($request->all()));
        \Log::debug('Current Session ID: ' . session()->getId());
        
        $request->validate([
            'session_id' => 'required|string',
        ]);
        
        $sessionId = $request->input('session_id');
        \Log::debug('Session ID for transfer: ' . $sessionId);
        \Log::debug('Current Session ID: ' . session()->getId());
        
        // Only allow transferring the current session
        if ($sessionId !== session()->getId()) {
            \Log::debug('Error: Attempted to transfer a non-current session');
            \Log::debug('=== SessionDashboardController@createTransferCode END ===');
            
            return redirect()->back()
                ->with('error', 'You can only transfer your current session.');
        }
        
        // Restrict transfer code generation for transferred sessions
        if (session('is_transferred_session')) {
            \Log::debug('Error: Attempted to generate transfer code from a transferred session');
            \Log::debug('=== SessionDashboardController@createTransferCode END ===');
            return redirect()->back();
        }
        
        // Create a transfer code
        $transferCode = $this->loginTracker->createSessionTransfer($sessionId);
        \Log::debug('Generated Transfer Code: ' . $transferCode);
        
        \Log::debug('=== SessionDashboardController@createTransferCode END ===');
        
        return redirect()->back()
            ->with('transfer_code', $transferCode)
            ->with('status', 'Session transfer code created successfully. Use this code on your other device to continue your session: ' . $transferCode);
    }

    public function historyDashboard()
    {
        $user = auth()->user();
        $magicLinks = \App\Models\MagicLink::where('user_id', $user->id)->orderByDesc('created_at')->get();
        $loginHistories = \App\Models\Auth\LoginHistory::where('user_id', $user->id)->orderByDesc('login_at')->get();
        $loginStats = \App\Models\Auth\UserLoginStat::where('user_id', $user->id)->orderByDesc('login_date')->get();
        $transferredSessions = \App\Models\Auth\LoginHistory::where('user_id', $user->id)
            ->where(function($q) {
                $q->whereNotNull('transferred_from')->orWhereNotNull('transferred_to');
            })
            ->orderByDesc('login_at')->get();
        return view('auth.history-dashboard', compact('magicLinks', 'loginHistories', 'loginStats', 'transferredSessions'));
    }
} 