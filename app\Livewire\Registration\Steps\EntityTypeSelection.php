<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\Entity\Entity;
use App\Models\Auth\RegistrationAttempt;

class EntityTypeSelection extends Component
{
    public $entityType = '';
    public $distributorId = '';
    public $showDistributorField = false;
    public $attempt;
    
    protected function rules()
    {
        $rules = [
            'entityType' => 'required|in:dealer,distributor'
        ];
        
        if ($this->entityType === 'dealer' && $this->distributorId) {
            $rules['distributorId'] = [
                'required',
                'regex:/^DIS-[A-Z]{2}-\d{4}$/',
                function ($attribute, $value, $fail) {
                    $distributor = Entity::where('entity_id', $value)
                        ->where('entity_type', 'distributor')
                        ->where('is_active', true)
                        ->first();
                    
                    if (!$distributor) {
                        $fail('Invalid distributor ID or distributor is inactive. Please contact your distributor.');
                    }
                }
            ];
        }
        
        return $rules;
    }
    
    protected $messages = [
        'entityType.required' => 'Please select entity type',
        'entityType.in' => 'Entity type must be either distributor or dealer',
        'distributorId.regex' => 'Invalid distributor ID format'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        if (!$this->attempt) {
            return redirect()->route('register');
        }
        
        // Load saved data if exists
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        if (isset($stagesData['entity_type_selection'])) {
            $this->entityType = $stagesData['entity_type_selection']['entity_type'] ?? '';
            $this->distributorId = $stagesData['entity_type_selection']['distributor_id'] ?? '';
            $this->showDistributorField = $this->entityType === 'dealer';
        }
    }
    
    public function updatedEntityType($value)
    {
        $this->showDistributorField = $value === 'dealer';
        if ($value !== 'dealer') {
            $this->distributorId = '';
        }
    }
    
    public function saveAndContinue()
    {
        $this->validate();
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['entity_type_selection'] = [
            'entity_type' => $this->entityType,
            'distributor_id' => $this->distributorId ?: null
        ];
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('entity_type_selection', $stagesCompleted)) {
            $stagesCompleted[] = 'entity_type_selection';
        }
        
        $this->attempt->update([
            'entity_type' => $this->entityType,
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'business_information'
        ]);
        
        $this->dispatch('stepCompleted', 'entity_type_selection');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.entity-type-selection');
    }
} 