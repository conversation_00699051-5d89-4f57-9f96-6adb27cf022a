@props([
    'variant' => 'default',
    'color' => null,
    'size' => 'base',
])

@php
$sizeClasses = match ($size) {
    'sm' => 'text-xs py-1 px-2',
    'lg' => 'text-sm py-1.5 px-3',
    default => 'text-xs py-1 px-2',
};

$variantClasses = match ($variant) {
    'solid' => match ($color) {
        'red' => 'bg-red-500 text-white',
        'orange' => 'bg-orange-500 text-white',
        'amber' => 'bg-amber-500 text-white',
        'yellow' => 'bg-yellow-500 text-white',
        'lime' => 'bg-lime-500 text-white',
        'green' => 'bg-green-500 text-white',
        'emerald' => 'bg-emerald-500 text-white',
        'teal' => 'bg-teal-500 text-white',
        'cyan' => 'bg-cyan-500 text-white',
        'sky' => 'bg-sky-500 text-white',
        'blue' => 'bg-blue-500 text-white',
        'indigo' => 'bg-indigo-500 text-white',
        'violet' => 'bg-violet-500 text-white',
        'purple' => 'bg-purple-500 text-white',
        'fuchsia' => 'bg-fuchsia-500 text-white',
        'pink' => 'bg-pink-500 text-white',
        'rose' => 'bg-rose-500 text-white',
        'success' => 'bg-green-500 text-white',
        'danger' => 'bg-red-500 text-white',
        'warning' => 'bg-amber-500 text-white',
        'info' => 'bg-blue-500 text-white',
        default => 'bg-zinc-500 text-white',
    },
    'outline' => match ($color) {
        'red' => 'border border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400',
        'orange' => 'border border-orange-200 bg-orange-50 text-orange-700 dark:border-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
        'amber' => 'border border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-400',
        'yellow' => 'border border-yellow-200 bg-yellow-50 text-yellow-700 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
        'lime' => 'border border-lime-200 bg-lime-50 text-lime-700 dark:border-lime-800 dark:bg-lime-900/20 dark:text-lime-400',
        'green' => 'border border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400',
        'emerald' => 'border border-emerald-200 bg-emerald-50 text-emerald-700 dark:border-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400',
        'teal' => 'border border-teal-200 bg-teal-50 text-teal-700 dark:border-teal-800 dark:bg-teal-900/20 dark:text-teal-400',
        'cyan' => 'border border-cyan-200 bg-cyan-50 text-cyan-700 dark:border-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400',
        'sky' => 'border border-sky-200 bg-sky-50 text-sky-700 dark:border-sky-800 dark:bg-sky-900/20 dark:text-sky-400',
        'blue' => 'border border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        'indigo' => 'border border-indigo-200 bg-indigo-50 text-indigo-700 dark:border-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400',
        'violet' => 'border border-violet-200 bg-violet-50 text-violet-700 dark:border-violet-800 dark:bg-violet-900/20 dark:text-violet-400',
        'purple' => 'border border-purple-200 bg-purple-50 text-purple-700 dark:border-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
        'fuchsia' => 'border border-fuchsia-200 bg-fuchsia-50 text-fuchsia-700 dark:border-fuchsia-800 dark:bg-fuchsia-900/20 dark:text-fuchsia-400',
        'pink' => 'border border-pink-200 bg-pink-50 text-pink-700 dark:border-pink-800 dark:bg-pink-900/20 dark:text-pink-400',
        'rose' => 'border border-rose-200 bg-rose-50 text-rose-700 dark:border-rose-800 dark:bg-rose-900/20 dark:text-rose-400',
        'success' => 'border border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400',
        'danger' => 'border border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400',
        'warning' => 'border border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-400',
        'info' => 'border border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
        default => 'border border-zinc-200 bg-zinc-50 text-zinc-700 dark:border-zinc-700 dark:bg-zinc-800 dark:text-zinc-300',
    },
    default => 'border border-zinc-200 bg-zinc-50 text-zinc-700 dark:border-zinc-700 dark:bg-zinc-800 dark:text-zinc-300',
};

$baseClasses = 'inline-flex items-center font-medium whitespace-nowrap rounded-md';
$classes = "$baseClasses $sizeClasses $variantClasses";
@endphp

<span {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</span>
