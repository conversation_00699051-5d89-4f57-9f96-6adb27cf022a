@props([
    'component',
])

@php
    $componentExists = view()->exists('flux::' . $component);
@endphp

@if ($componentExists)
    <x-flux::{{ $component }} {{ $attributes }}>{{ $slot }}</x-flux::{{ $component }}>
@else
    <div class="p-4 border border-red-300 bg-red-100 text-red-800 rounded">
        <strong>Component Error:</strong> The component "{{ $component }}" does not exist.
    </div>
@endif 