#!/bin/bash

# Reset the testing environment
php artisan config:clear
php artisan cache:clear

# Remove the banned_attempts table migration to avoid conflicts
if [ -f database/migrations/2025_07_15_000001_create_banned_attempts_table.php ]; then
  echo "Moving banned_attempts migration out of the way for testing..."
  mv database/migrations/2025_07_15_000001_create_banned_attempts_table.php database/migrations/2025_07_15_000001_create_banned_attempts_table.php.bak
fi

# Create a clean database for testing
echo "Setting up fresh test database..."
php artisan migrate:fresh --env=testing

# Restore the banned_attempts migration file if we moved it
if [ -f database/migrations/2025_07_15_000001_create_banned_attempts_table.php.bak ]; then
  mv database/migrations/2025_07_15_000001_create_banned_attempts_table.php.bak database/migrations/2025_07_15_000001_create_banned_attempts_table.php
fi

# Run the individual test files with detailed output
echo "Running Login Tests..."
php artisan test tests/Feature/Auth/LoginTest.php --env=testing --testdox

echo "Running Session Management Tests..."
php artisan test tests/Feature/Auth/SessionManagementTest.php --env=testing --testdox

echo "Running Agent Integration Tests..."
php artisan test tests/Feature/Auth/AgentIntegrationTest.php --env=testing --testdox

# Run all login-related tests together
# echo "Running all login tests..."
# php artisan test tests/Feature/Auth/LoginTest.php tests/Feature/Auth/SessionManagementTest.php tests/Feature/Auth/AgentIntegrationTest.php 