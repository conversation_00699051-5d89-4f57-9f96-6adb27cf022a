{{-- resources/views/components/flux/card.blade.php --}}
@props([
'class' => '',
'variant' => 'default', // 'default' or 'outline'
'header' => null,
'footer' => null,
'title' => null,
'subtitle' => null,
'actions' => null,
'padding' => 'p-6',
'hover' => false,
])

@php
$baseClasses = 'rounded-lg overflow-hidden flex flex-col';

$variantClasses = match($variant) {
'outline' => 'border border-zinc-200 dark:border-zinc-700',
default => 'bg-white dark:bg-zinc-800 shadow-sm',
};

$hoverClasses = $hover ? 'hover:shadow-md transition-all duration-200' : '';
@endphp

<div {{ $attributes->merge(['class' => "$baseClasses $variantClasses $hoverClasses $class"]) }}>
    @if($header || $title || $actions)
    <div class="border-b border-zinc-100 dark:border-zinc-700 px-6 py-4 flex items-center justify-between gap-4">
        <div>
            @if($title)
            <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">{{ $title }}</h3>
            @endif
            @if($subtitle)
            <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">{{ $subtitle }}</p>
            @endif
        </div>

        @if($actions)
        <div class="flex items-center gap-2">
            {{ $actions }}
        </div>
        @endif

        {{ $header }}
    </div>
    @endif

    <div class="flex-1 {{ $padding }}">
        {{ $slot }}
    </div>

    @if($footer)
    <div class="border-t border-zinc-100 dark:border-zinc-700 px-6 py-4 bg-zinc-50 dark:bg-zinc-700/20">
        {{ $footer }}
    </div>
    @endif
</div>