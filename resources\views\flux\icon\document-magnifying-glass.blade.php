@props([
    'variant' => 'outline',
])

@php
$sizeClasses = match($variant) {
    'outline' => 'h-6 w-6',
    'solid' => 'h-6 w-6',
    'mini' => 'h-5 w-5',
    'micro' => 'h-4 w-4',
    default => 'h-6 w-6',
};

$classes = "shrink-0 $sizeClasses";
@endphp

<svg {{ $attributes->merge(['class' => $classes]) }} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h.375m3 0h.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46 3.243-8.161 7.5-8.876a9.06 9.06 0 0 1 1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"/>
</svg>
