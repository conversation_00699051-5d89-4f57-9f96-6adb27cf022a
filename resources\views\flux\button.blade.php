@props([
    'variant' => 'primary',
    'size' => 'base',
    'disabled' => false,
    'type' => 'button',
    'icon' => null,
    'iconPosition' => 'left',
    'square' => false,
])

@php
    $baseClasses = 'inline-flex items-center justify-center font-medium focus:outline-none transition-colors';
    $baseClasses .= ' disabled:opacity-70 disabled:cursor-not-allowed';
    
    // Size classes
    $sizeClasses = match($size) {
        'xs' => 'text-xs px-2 py-1 rounded',
        'sm' => 'text-sm px-3 py-1.5 rounded-md',
        'base' => 'text-sm px-4 py-2 rounded-lg',
        'lg' => 'text-base px-5 py-2.5 rounded-lg',
        default => 'text-sm px-4 py-2 rounded-lg',
    };
    
    // Square button adjustments
    if ($square) {
        $sizeClasses = match($size) {
            'xs' => 'text-xs p-1 rounded',
            'sm' => 'text-sm p-1.5 rounded-md',
            'base' => 'text-sm p-2 rounded-lg',
            'lg' => 'text-base p-2.5 rounded-lg',
            default => 'text-sm p-2 rounded-lg',
        };
    }
    
    // Variant classes
    $variantClasses = match($variant) {
        'primary' => 'bg-zinc-800 text-white hover:bg-zinc-700 dark:bg-zinc-700 dark:hover:bg-zinc-600',
        'secondary' => 'bg-zinc-200 text-zinc-800 hover:bg-zinc-300 dark:bg-zinc-600 dark:text-zinc-100 dark:hover:bg-zinc-500',
        'outline' => 'bg-white text-zinc-800 border border-zinc-300 hover:bg-zinc-50 dark:bg-zinc-800 dark:text-white dark:border-zinc-600 dark:hover:bg-zinc-700',
        'ghost' => 'text-zinc-700 hover:bg-zinc-100 dark:text-zinc-300 dark:hover:bg-zinc-700',
        'danger' => 'bg-red-600 text-white hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600',
        'success' => 'bg-green-600 text-white hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600',
        'warning' => 'bg-amber-500 text-white hover:bg-amber-600 dark:bg-amber-500 dark:hover:bg-amber-600',
        'info' => 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600',
        default => 'bg-zinc-800 text-white hover:bg-zinc-700 dark:bg-zinc-700 dark:hover:bg-zinc-600',
    };
    
    $iconClasses = 'w-4 h-4';
    $iconSpacing = $slot->isEmpty() ? '' : ($iconPosition === 'left' ? 'mr-2' : 'ml-2');
@endphp

<button 
    type="{{ $type }}" 
    {{ $disabled ? 'disabled' : '' }}
    {{ $attributes->merge(['class' => "$baseClasses $sizeClasses $variantClasses"]) }}
>
    @if($icon && $iconPosition === 'left')
        <span class="{{ $iconClasses }} {{ $iconSpacing }}">
            @if($icon === 'arrow-left')
                @include('flux.icon.arrow-left')
            @elseif($icon === 'arrow-right')
                @include('flux.icon.arrow-right')
            @elseif($icon === 'loading')
                @include('flux.icon.loading')
            @endif
        </span>
    @endif
    
    {{ $slot }}
    
    @if($icon && $iconPosition === 'right')
        <span class="{{ $iconClasses }} {{ $iconSpacing }}">
            @if($icon === 'arrow-left')
                @include('flux.icon.arrow-left')
            @elseif($icon === 'arrow-right')
                @include('flux.icon.arrow-right')
            @elseif($icon === 'loading')
                @include('flux.icon.loading')
            @endif
        </span>
    @endif
</button> 