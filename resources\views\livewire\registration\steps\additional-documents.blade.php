<div>
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">Additional Documents</h2>
        <p class="text-gray-600">
            Upload any additional supporting documents that may be required for your registration.
        </p>
    </div>

    <!-- Existing Documents Section -->
    @if(count($existingDocuments) > 0)
        <div class="mb-8">
            <h3 class="text-lg font-semibold mb-4">Uploaded Documents</h3>
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <ul class="divide-y divide-gray-200">
                    @foreach($existingDocuments as $index => $document)
                        <li class="py-3 flex justify-between items-center">
                            <div>
                                <p class="font-medium">{{ $document['description'] }}</p>
                                <p class="text-sm text-gray-500">{{ $document['original_name'] }}</p>
                                <p class="text-xs text-gray-400">Uploaded: {{ $document['uploaded_at'] }}</p>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{{ Storage::url($document['filename']) }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </a>
                                <button wire:click="removeDocument({{ $index }})" class="text-red-600 hover:text-red-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    @endif

    <!-- Upload Form -->
    <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4">Upload Additional Document</h3>
        <div class="bg-white rounded-lg p-6 border border-gray-200">
            <form wire:submit.prevent="uploadDocument">
                <div class="mb-4">
                    <label for="otherDocument" class="block text-sm font-medium text-gray-700 mb-1">Select Document</label>
                    <input type="file" id="otherDocument" wire:model="otherDocument" class="block w-full text-sm text-gray-500
                        file:mr-4 file:py-2 file:px-4
                        file:rounded-md file:border-0
                        file:text-sm file:font-semibold
                        file:bg-blue-50 file:text-blue-700
                        hover:file:bg-blue-100">
                    @error('otherDocument') <span class="text-red-500 text-xs mt-1">{{ $message }}</span> @enderror
                    <p class="mt-1 text-xs text-gray-500">Accepted formats: PDF, JPG, PNG (max 10MB)</p>
                </div>

                <div class="mb-4">
                    <label for="otherDocumentDescription" class="block text-sm font-medium text-gray-700 mb-1">Document Description</label>
                    <input type="text" id="otherDocumentDescription" wire:model="otherDocumentDescription" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    @error('otherDocumentDescription') <span class="text-red-500 text-xs mt-1">{{ $message }}</span> @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        Upload Document
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex justify-between mt-8">
        <button wire:click="goBack" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
            Previous Step
        </button>
        <button wire:click="completeStep" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Next Step
        </button>
    </div>
</div> 