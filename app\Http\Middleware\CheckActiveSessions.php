<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Auth\LoginHistory;
use App\Services\LoginTrackingService;
use Carbon\Carbon;

class CheckActiveSessions
{
    protected LoginTrackingService $loginTracker;
    
    /**
     * Create a new middleware instance.
     *
     * @param LoginTrackingService $loginTracker
     */
    public function __construct(LoginTrackingService $loginTracker)
    {
        $this->loginTracker = $loginTracker;
    }
    
    /**
     * Handle an incoming request.
     * Validates user session state and enforces multi-login limits.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();
            $currentSessionId = session()->getId();
            $activeRoleId = session('active_role_id');
            $isSuperAdmin = session('is_super_admin', false);
            $hasUnrestrictedAccess = session('unrestricted_access', false);
            
            // Check if this is a transferred session
            $isTransferredSession = session()->has('transferred_from_session') || 
                                   session()->has('is_transferred_session');
            
            \Log::debug('=== CheckActiveSessions middleware ===');
            \Log::debug('User ID: ' . $user->id);
            \Log::debug('Current Session ID: ' . $currentSessionId);
            \Log::debug('Is Transferred Session: ' . ($isTransferredSession ? 'Yes' : 'No'));
            
            // Also check database for transferred session status
            $sessionRecord = LoginHistory::where('session_id', $currentSessionId)
                ->where('user_id', $user->id)
                ->first();
                
            if ($sessionRecord) {
                $isTransferredInDb = !empty($sessionRecord->transferred_from);
                \Log::debug('Is Transferred in DB: ' . ($isTransferredInDb ? 'Yes' : 'No'));
                $isTransferredSession = $isTransferredSession || $isTransferredInDb;
            }
            
            // Check for device fingerprint changes (potential session hijacking)
            // Skip strict fingerprint check for transferred sessions
            if (!$isTransferredSession && $this->loginTracker->checkFingerprintChanged($request, $user->id, $currentSessionId)) {
                // Log the potential hijacking
                $this->logoutCurrentSession($user, $currentSessionId, 'potential_hijacking');
                
                // Redirect to login with warning
                return redirect()->route('login')
                    ->with('status', 'Your session was terminated due to security concerns. Please log in again.');
            }
            
            // Count active sessions for the current user
            $activeSessions = LoginHistory::activeSessions()
                ->where('user_id', $user->id)
                ->count();
            
            \Log::debug('Active Sessions Count: ' . $activeSessions);
            \Log::debug('User Multi-login Limit: ' . $user->multi_login);
            
            // Multi-login limit enforcement - Super Admins with unrestricted access bypass this check
            if (!($isSuperAdmin && $hasUnrestrictedAccess) && $activeSessions > $user->multi_login && $user->multi_login > 0) {
                // Get the current session
                $currentSession = LoginHistory::where('session_id', $currentSessionId)
                    ->where('user_id', $user->id)
                    ->first();
                
                \Log::debug('Current Session Found: ' . ($currentSession ? 'Yes' : 'No'));
                \Log::debug('Current Session Logged Out: ' . ($currentSession && $currentSession->logout_at ? 'Yes' : 'No'));
                
                // If this is a new session that would exceed limits
                if (!$currentSession || $currentSession->logout_at !== null) {
                    \Log::debug('Redirecting to sessions dashboard - multi-login limit reached');
                    return redirect()->route('sessions.dashboard')
                        ->with('status', 'You have reached your maximum session limit. Please end another session to continue.');
                }
            }
            
            // Check for duplicate role sessions if role is set
            // Super Admins with unrestricted access bypass this check
            // Transferred sessions also bypass this check
            if ($activeRoleId && !($isSuperAdmin && $hasUnrestrictedAccess) && !$isTransferredSession) {
                $duplicateRoleSessions = LoginHistory::activeSessions()
                    ->where('user_id', $user->id)
                    ->where('role_id', $activeRoleId)
                    ->where('session_id', '!=', $currentSessionId)
                    ->count();
                
                \Log::debug('Duplicate Role Sessions: ' . $duplicateRoleSessions);
                
                if ($duplicateRoleSessions > 0) {
                    // Log the current session out
                    $this->logoutCurrentSession($user, $currentSessionId, 'duplicate_role_session');
                    
                    // Redirect to login with message
                    return redirect()->route('login')
                        ->with('status', 'You already have an active session with the same role. Multiple sessions with the same role are not allowed.');
                }
            }
            
            // Update the current session's last activity timestamp
            LoginHistory::where('session_id', $currentSessionId)
                ->where('user_id', $user->id)
                ->where('logout_at', null)
                ->update([
                    'updated_at' => Carbon::now(),
                ]);
            
            // Session timeout check
            // Super Admins might have different timeout settings
            $roleLevel = session('active_role_level');
            
            // Determine session timeout based on role hierarchy
            $sessionTimeout = $this->getSessionTimeoutForRole($roleLevel, $isSuperAdmin);
            $lastActivity = session('last_activity');
            
            if ($lastActivity && (time() - $lastActivity) > $sessionTimeout) {
                // Log the timeout
                $this->logoutCurrentSession($user, $currentSessionId, 'timeout');
                
                // Redirect to login with message
                return redirect()->route('login')
                    ->with('status', 'Session expired due to inactivity.');
            }
            
            // Update last activity timestamp
            session(['last_activity' => time()]);
        }

        return $next($request);
    }
    
    /**
     * Log out the current session
     *
     * @param User $user
     * @param string $sessionId
     * @param string $reason
     * @return void
     */
    protected function logoutCurrentSession($user, $sessionId, $reason = 'user_terminated')
    {
        // Update the login history record
        LoginHistory::where('session_id', $sessionId)
            ->where('user_id', $user->id)
            ->where('logout_at', null)
            ->update([
                'logout_at' => Carbon::now(),
                'logout_reason' => $reason,
                'duration_seconds' => Carbon::now()->diffInSeconds(Carbon::parse(session('login_at'))),
            ]);
        
        // Log the user out
        Auth::logout();
        session()->invalidate();
        session()->regenerateToken();
    }
    
    /**
     * Get session timeout based on role hierarchy level
     * 
     * @param int|null $roleLevel
     * @param bool $isSuperAdmin
     * @return int Timeout in seconds
     */
    protected function getSessionTimeoutForRole(?int $roleLevel, bool $isSuperAdmin): int
    {
        // Default session timeout (2 hours)
        $defaultTimeout = config('session.lifetime', 120) * 60;
        
        // Super Admin gets shorter timeout for security (30 minutes)
        if ($isSuperAdmin) {
            return 30 * 60;
        }
        
        // Role-based timeouts
        if ($roleLevel !== null) {
            switch ($roleLevel) {
                case 1: // Super Administrator
                    return 30 * 60; // 30 minutes
                case 2: // Administrator Manager
                    return 60 * 60; // 1 hour
                case 3: // Administrator
                    return 90 * 60; // 1.5 hours
                default:
                    return $defaultTimeout;
            }
        }
        
        return $defaultTimeout;
    }
} 