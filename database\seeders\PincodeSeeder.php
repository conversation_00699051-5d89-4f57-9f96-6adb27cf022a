<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Exception;

class PincodeSeeder extends Seeder
{
    /**
     * Run the database seeds using native PHP functions in a memory-efficient way.
     *
     * @return void
     */
    public function run()
    {
        // Path to the CSV file.
        $csvFilePath = database_path('seeders/data/Pincode data.csv');

        if (!file_exists($csvFilePath)) {
            $this->command->error("The CSV file was not found. Please make sure the file exists at: " . $csvFilePath);
            return;
        }

        // Open the file for reading.
        $fileHandle = fopen($csvFilePath, 'r');

        if ($fileHandle === false) {
            $this->command->error("Could not open the CSV file.");
            return;
        }

        try {
            // Clear the table before inserting new data.
            DB::table('pincodes')->truncate();

            // Read the header row to get column names.
            $header = fgetcsv($fileHandle);
            if ($header === false) {
                 $this->command->error("Could not read the header from the CSV file.");
                 fclose($fileHandle);
                 return;
            }

            $dataChunk = [];
            $chunkSize = 500; // Process 500 records at a time.

            // Loop through the rest of the file line by line.
            while (($row = fgetcsv($fileHandle)) !== false) {
                // Combine the header with the row data to create an associative array.
                $record = array_combine($header, $row);

                // **Data Sanitization Step**
                // Clean each value to prevent encoding errors with the database.
                foreach ($record as $key => &$value) {
                    if (is_string($value)) {
                        // Ensure the string is valid UTF-8 and remove any invalid characters.
                        $value = mb_convert_encoding($value, 'UTF-8', 'UTF-8');
                        // Trim whitespace and other non-visible characters from the start and end.
                        $value = trim($value);
                    }
                }

                // Basic data validation.
                if (
                    !empty($record['Area']) &&
                    !empty($record['Pincode']) && is_numeric($record['Pincode']) &&
                    !empty($record['District']) &&
                    !empty($record['State Name'])
                ) {
                    // Add the valid record to the current chunk.
                    $dataChunk[] = [
                        'area' => $record['Area'],
                        'pincode' => (int) $record['Pincode'],
                        'district' => $record['District'],
                        'state_name' => $record['State Name'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                // If the chunk is full, insert it into the database and clear it.
                if (count($dataChunk) >= $chunkSize) {
                    DB::table('pincodes')->insert($dataChunk);
                    $dataChunk = []; // Reset the chunk.
                    $this->command->info("Inserted a chunk of records...");
                }
            }
            
            // Insert any remaining records in the last chunk.
            if (!empty($dataChunk)) {
                DB::table('pincodes')->insert($dataChunk);
                $this->command->info("Inserted the final chunk of records...");
            }

            $this->command->info('Pincode table seeded successfully!');

        } catch (Exception $e) {
            // Catch and display any other exceptions that might occur during the process.
            $this->command->error("An error occurred during seeding: " . $e->getMessage());
        } finally {
            // Always make sure to close the file handle.
            fclose($fileHandle);
        }
    }
}
