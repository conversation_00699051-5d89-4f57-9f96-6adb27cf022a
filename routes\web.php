<?php

use App\Http\Controllers\Auth\VerifyEmailController;
use Illuminate\Support\Facades\Route;
use App\Livewire\Registration\RegistrationWizard;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use App\Http\Controllers\Auth\MagicLinkController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
})->name('home');

// Redirect the default register route to our entity registration
Route::redirect('/register', '/register/entity');

// Protected routes requiring authentication, active status, and role
Route::middleware(['auth', 'active', 'check.sessions', 'check.roles'])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
    
    // Settings routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/profile', Profile::class)->name('profile');
        Route::get('/password', Password::class)->name('password');
        Route::get('/appearance', Appearance::class)->name('appearance');
    });

    // Admin routes with hierarchy level check
    Route::prefix('admin')->name('admin.')->middleware('min.hierarchy:2')->group(function () {
        Route::prefix('rbac')->name('rbac.')->group(function () {
            // Role Management
            Route::prefix('roles')->name('roles.')->group(function () {
                Route::get('/', \App\Livewire\Admin\Rbac\Roles\RoleIndex::class)->name('index');
                Route::get('/create', \App\Livewire\Admin\Rbac\Roles\RoleCreate::class)->name('create');
                Route::get('/{role}/edit', \App\Livewire\Admin\Rbac\Roles\RoleEdit::class)->name('edit');
            });

            // User Role Assignment
            Route::prefix('users')->name('users.')->group(function () {
                Route::get('/', \App\Livewire\Admin\Rbac\Users\UserRoleIndex::class)->name('index');
                Route::get('/{user}/assign', \App\Livewire\Admin\Rbac\Users\UserRoleAssign::class)->name('assign');
            });
        });
    });
});

// Include auth routes
require __DIR__.'/auth.php';

// Include entity registration routes
require __DIR__.'/entity-registration.php';

// Magic Link routes
Route::post('/sessions/magic-link', [MagicLinkController::class, 'generate'])
    ->middleware(['throttle:3,1']) // 3 attempts per minute
    ->name('sessions.magic-link.generate');
Route::get('/magic-link/{token}', [MagicLinkController::class, 'consume'])->name('magic-link.consume');
Route::post('/magic-link/{id}/cancel', [MagicLinkController::class, 'cancel'])->middleware(['auth'])->name('magic-link.cancel');

Route::get('/sessions/history-dashboard', [\App\Http\Controllers\Auth\SessionDashboardController::class, 'historyDashboard'])->name('sessions.history-dashboard');
