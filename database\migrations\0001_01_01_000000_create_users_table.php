<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop existing tables in reverse order of creation to handle foreign key constraints
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('entity_relationships');
        Schema::dropIfExists('users');
        Schema::dropIfExists('entities');

        // Create the 'entities' table
        Schema::create('entities', function (Blueprint $table) {
            // Primary auto-increment ID
            $table->id()->comment('Auto-incrementing primary key');

            // Business identifier fields
            $table->string('entity_id', 25)->unique()->comment('Unique business identifier for external reference');
            $table->string('entity_name', 100)->comment('Legal name of the entity');
            $table->string('logo')->nullable()->comment('Path to entity logo image');
            $table->string('website')->nullable()->comment('Entity website URL');

            // Entity type (supplier, distributor, dealer)
            $table->enum('entity_type', ['supplier', 'distributor', 'dealer'])
                ->comment('Type of entity: supplier, distributor, or dealer');

            // Provider category flags
            $table->boolean('is_goods_provider')->default(false)
                ->comment('Whether this entity provides goods');
            $table->boolean('is_service_provider')->default(false)
                ->comment('Whether this entity provides services');
            // Status flag
            $table->boolean('is_active')->default(true)->comment('Whether the entity is currently active');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            // Audit fields
            $table->unsignedBigInteger('created_by')->comment('User ID who created this record');
            $table->timestamp('created_at')->useCurrent()->comment('When this record was created');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last modified this record');
            $table->timestamp('updated_at')->nullable()->comment('When this record was last modified');

            // Soft delete fields
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who deleted this record');

            // Restore fields
            $table->timestamp('restored_at')->nullable()->comment('When this record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored this record');

            // Inactivation fields (NEW)
            $table->unsignedBigInteger('inactivated_by')->nullable()->comment('ID of the user who inactivated this user'); // User who performed inactivation
            $table->timestamp('inactivated_at')->nullable()->comment('Timestamp when the user was inactivated'); // Time of inactivation

            // Indexes
            $table->index('entity_name')->comment('Index for faster entity name searches');
            $table->index('is_active')->comment('Index for filtering active/inactive entities');
            $table->index('entity_type')->comment('Index for entity type filtering');
        });

        // Create the 'users' table
        Schema::create('users', function (Blueprint $table) {
            $table->id(); // Primary key for the users table
            $table->string('name')->comment('Full name of the user'); // User's full name
            $table->string('email')->unique()->comment('User email address, must be unique'); // User's unique email
            $table->string('user_id')->unique()->comment('Unique user identifier (custom)'); // Custom unique ID for the user
            $table->string('phone')->nullable()->comment('Phone number of the user'); // User's phone number, optional
            $table->foreignId('entity_id')->nullable()->constrained('entities')->onDelete('cascade')->comment('Foreign key to the entities table'); // Link to an entity
            $table->timestamp('email_verified_at')->nullable()->comment('Timestamp when email was verified'); // Email verification timestamp
            $table->boolean('is_active')->default(false)->comment('Active status of the user account'); // Whether the user account is active
            $table->unsignedInteger('multi_login')->default(1)->comment('Number of allowed simultaneous logins; 0 = unlimited'); // Concurrent login limit
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.'); // Approval requirement flag
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.'); // Approval status
            $table->timestamp('last_login_at')->nullable()->comment('Timestamp of the last successful login');
            $table->string('password')->comment('Hashed password for user authentication'); // User's hashed password
            $table->rememberToken()->comment('Token for "remember me" sessions'); // "Remember me" functionality token

            // Activation fields
            $table->unsignedBigInteger('activated_by')->nullable()->comment('ID of the user who activated this user'); // User who performed activation
            $table->timestamp('activated_at')->nullable()->comment('Timestamp when the user was activated'); // Time of activation

            // Inactivation fields (NEW)
            $table->unsignedBigInteger('inactivated_by')->nullable()->comment('ID of the user who inactivated this user'); // User who performed inactivation
            $table->timestamp('inactivated_at')->nullable()->comment('Timestamp when the user was inactivated'); // Time of inactivation

            // Standard Laravel timestamps
            $table->timestamps(); // `created_at` and `updated_at` timestamps

            // Soft Deletes
            $table->softDeletes(); // `deleted_at` timestamp for soft deletes

            // Audit fields with foreign key constraints
            $table->unsignedBigInteger('created_by')->nullable()->comment('User who created this record'); // User who created the record
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User who last updated this record'); // User who last updated
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft-deleted this record'); // User who soft-deleted
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored this record'); // User who restored from soft delete
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when record was restored from soft delete'); // Time of restoration

            // Foreign key constraints for audit fields
            $table->foreign('activated_by')->references('id')->on('users')->onDelete('set null'); // FK for activated_by
            $table->foreign('inactivated_by')->references('id')->on('users')->onDelete('set null'); // FK for inactivated_by (NEW)
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null'); // FK for created_by
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null'); // FK for updated_by
            $table->foreign('deleted_by')->references('id')->on('users')->onDelete('set null'); // FK for deleted_by
            $table->foreign('restored_by')->references('id')->on('users')->onDelete('set null'); // FK for restored_by
        });

        // Create the 'entity_relationships' table
        Schema::create('entity_relationships', function (Blueprint $table) {
            $table->id(); // Primary key

            // Relationship participants
            $table->unsignedBigInteger('source_entity_id')->comment('Entity initiating the relationship');
            $table->unsignedBigInteger('target_entity_id')->comment('Entity receiving the relationship');

            // Relationship type
            $table->enum('relationship_type', [
                'supplies_to',     // Supplier → Distributor or Distributor → Dealer
                'service_to',      // Service Provider → Distributor or Dealer
                'sells_to',        // Distributor → Dealer or Dealer → Customer
                'reports_to',      // Dealer → Distributor
                'parent_of',       // Parent → Child (ownership/structure)
                'linked_to'        // Generic bidirectional link
            ])->comment('Type of relationship between source and target');

            // Optional description or reason
            $table->string('description')->nullable()->comment('Optional relationship notes or business logic reason');
            $table->date('effective_date')->nullable()->comment('Date when the relationship becomes effective');
            $table->date('expiry_date')->nullable()->comment('Date when the relationship expires');

            // Status
            $table->boolean('is_active')->default(true)->comment('Whether this relationship is currently active');
            // Approval fields
            $table->boolean('is_approval_required')->default(false)->comment('Flag to indicate if an approval is needed for this record.');
            $table->enum('approval_status', ['pending', 'approved', 'rejected', 'expired', 'cancelled', 'on_hold'])->default('approved')->comment('The current status of the approval process.');

            // Audit Fields
            $table->unsignedBigInteger('created_by')->comment('User ID who created this record');
            $table->timestamp('created_at')->useCurrent()->comment('When this record was created');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last updated this record');
            $table->timestamp('updated_at')->nullable()->comment('When this record was last updated');

            // Soft delete fields
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who deleted this relationship');
            $table->timestamp('deleted_at')->nullable()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored this relationship');
            $table->timestamp('restored_at')->nullable()->comment('Restoration timestamp');

            // Constraints
            $table->foreign('source_entity_id')->references('id')->on('entities')->onDelete('cascade');
            $table->foreign('target_entity_id')->references('id')->on('entities')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');

            // Composite unique constraint to prevent duplicate relationships
            $table->unique(
                ['source_entity_id', 'target_entity_id', 'relationship_type'],
                'unique_entity_relationship'
            )->comment('Prevents duplicate relationships between the same entities');

            // Indexes for performance
            $table->index(['source_entity_id', 'target_entity_id'], 'entity_relationship_participants_index');
            $table->index('relationship_type', 'entity_relationship_type_index');
        });

        // Create the 'password_reset_tokens' table
        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary()->comment('The user email associated with the reset token');
            $table->string('token')->comment('The password reset token');
            $table->timestamp('created_at')->nullable()->comment('Timestamp when the token was created');
        });

        // Create the 'sessions' table for managing user sessions
        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary()->comment('The unique session ID');
            $table->foreignId('user_id')->nullable()->index()->comment('The user ID associated with the session');
            $table->string('ip_address', 45)->nullable()->comment('The IP address of the user');
            $table->text('user_agent')->nullable()->comment('The user agent string of the browser');
            $table->longText('payload')->comment('The session payload data');
            $table->integer('last_activity')->index()->comment('Timestamp of the last activity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop tables in the reverse order of their creation to avoid foreign key constraint issues
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('entity_relationships');
        Schema::dropIfExists('users');
        Schema::dropIfExists('entities');
    }
};
