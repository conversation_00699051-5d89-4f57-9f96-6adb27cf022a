<?php

namespace App\Services;

use App\Models\Auth\LoginHistory;
use App\Models\Auth\UserLoginStat;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Agent\Facades\Agent;
use App\Models\User;

class LoginTrackingService
{
    /**
     * Record a successful login attempt.
     *
     * @param Request $request
     * @param int $userId
     * @param string|null $sessionLabel Optional user-defined label for this session
     * @param bool $isCrossEntityAccess Whether this is cross-entity access by Super Admin
     * @param string|null $originalEntityId Original entity ID for cross-entity access
     * @param int|null $originalRoleId Original role ID for cross-entity access
     * @return LoginHistory
     */
    public function recordSuccessfulLogin(
        Request $request, 
        int $userId,
        ?string $sessionLabel = null,
        bool $isCrossEntityAccess = false,
        ?string $originalEntityId = null,
        ?int $originalRoleId = null
    ): LoginHistory
    {
        \Log::debug('=== LoginTrackingService@recordSuccessfulLogin START ===');
        \Log::debug('User ID: ' . $userId);
        \Log::debug('Session Label: ' . ($sessionLabel ?? 'null'));
        \Log::debug('Is Cross Entity Access: ' . ($isCrossEntityAccess ? 'Yes' : 'No'));
        \Log::debug('Original Entity ID: ' . ($originalEntityId ?? 'null'));
        \Log::debug('Original Role ID: ' . ($originalRoleId ?? 'null'));
        
        $sessionId = Session::getId();
        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();
        
        \Log::debug('Session ID: ' . $sessionId);
        \Log::debug('IP Address: ' . $ipAddress);
        \Log::debug('User Agent: ' . $userAgent);
        
        // Generate device fingerprint
        $fingerprint = $this->generateDeviceFingerprint($request);
        \Log::debug('Device Fingerprint: ' . $fingerprint);
        
        // Determine if this appears to be a new device
        $newDevice = !LoginHistory::where('user_id', $userId)
            ->where('device_fingerprint', $fingerprint)
            ->exists();
        \Log::debug('New Device: ' . ($newDevice ? 'Yes' : 'No'));
        
        // Calculate risk score
        $riskScore = $this->calculateRiskScore($request, $userId, $newDevice);
        \Log::debug('Risk Score: ' . $riskScore);
        
        // Create login history record
        $loginData = [
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'device_type' => Agent::deviceType(),
            'device_brand' => Agent::deviceBrand(),
            'device_model' => Agent::deviceModel(),
            'device_fingerprint' => $fingerprint,
            'fingerprint_changed' => false,
            'is_bot' => Agent::isBot(),
            'is_headless' => $this->detectHeadlessBrowser($userAgent),
            'platform' => Agent::platform(),
            'platform_version' => Agent::platformVersion(),
            'browser' => Agent::browser(),
            'browser_version' => Agent::browserVersion(),
            'session_id' => $sessionId,
            'session_label' => $sessionLabel ?: $this->generateSessionLabel(),
            'login_at' => Carbon::now(),
            'login_hour' => Carbon::now()->hour,
            'login_day' => Carbon::now()->format('l'),
            'login_origin' => $request->fullUrl(),
            'login_method' => 'password',
            'guard' => config('auth.defaults.guard'),
            'login_successful' => true,
            'new_device' => $newDevice,
            'risk_score' => $riskScore,
            'is_cross_entity_access' => $isCrossEntityAccess,
            'original_entity_id' => $originalEntityId,
            'original_role_id' => $originalRoleId,
        ];
        
        \Log::debug('Login Data: ' . json_encode($loginData));
        
        // Try to add geolocation data if available
        $geoData = $this->getGeoIpData($ipAddress);
        if ($geoData) {
            \Log::debug('Geo Data: ' . json_encode($geoData));
            $loginData = array_merge($loginData, $geoData);
        }
        
        // Store login time in session for duration calculation
        Session::put('login_at', Carbon::now()->toDateTimeString());
        
        // Create the login record
        $loginHistory = LoginHistory::create($loginData);
        \Log::debug('Created Login History: ' . json_encode($loginHistory->toArray()));
        
        // Update daily stats
        $this->updateUserLoginStats($userId);
        
        // Update user's last login timestamp
        User::where('id', $userId)->update(['last_login_at' => Carbon::now()]);
        
        \Log::debug('=== LoginTrackingService@recordSuccessfulLogin END ===');
        
        return $loginHistory;
    }
    
    /**
     * Record a failed login attempt.
     *
     * @param Request $request
     * @param string|null $email
     * @param string $failureReason
     * @param int|null $userId
     * @return LoginHistory
     */
    public function recordFailedLogin(
        Request $request,
        ?string $email = null,
        string $failureReason = 'Invalid credentials',
        ?int $userId = null
    ): LoginHistory {
        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();
        
        // Generate device fingerprint
        $fingerprint = $this->generateDeviceFingerprint($request);
        
        // Calculate risk score
        $riskScore = $this->calculateRiskScore($request, $userId, false, true);
        
        // Create failed login record
        $loginData = [
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'device_type' => Agent::deviceType(),
            'device_brand' => Agent::deviceBrand(),
            'device_fingerprint' => $fingerprint,
            'platform' => Agent::platform(),
            'platform_version' => Agent::platformVersion(),
            'browser' => Agent::browser(),
            'browser_version' => Agent::browserVersion(),
            'login_at' => Carbon::now(),
            'login_hour' => Carbon::now()->hour,
            'login_day' => Carbon::now()->format('l'),
            'login_origin' => $request->fullUrl(),
            'login_method' => 'password',
            'guard' => config('auth.defaults.guard'),
            'login_successful' => false,
            'failure_reason' => $failureReason . ($email ? " (email: $email)" : ''),
            'is_bot' => Agent::isBot(),
            'risk_score' => $riskScore,
        ];
        
        // Try to add geolocation data if available
        $geoData = $this->getGeoIpData($ipAddress);
        if ($geoData) {
            $loginData = array_merge($loginData, $geoData);
        }
        
        // Create the login record
        $loginHistory = LoginHistory::create($loginData);
        
        // Update stats if we know the user
        if ($userId) {
            $this->updateUserLoginStats($userId);
        }
        
        return $loginHistory;
    }
    
    /**
     * Record a user logout.
     *
     * @param int $userId
     * @param string|null $sessionId
     * @param string $reason
     * @return void
     */
    public function recordLogout(int $userId, ?string $sessionId = null, string $reason = 'user'): void
    {
        \Log::debug('=== LoginTrackingService@recordLogout START ===');
        \Log::debug('User ID: ' . $userId);
        \Log::debug('Session ID: ' . ($sessionId ?? Session::getId()));
        \Log::debug('Reason: ' . $reason);
        
        $sessionId = $sessionId ?? Session::getId();
        $loginAt = Session::get('login_at');
        \Log::debug('Login At from Session: ' . ($loginAt ?? 'null'));
        
        // Find the active session
        $loginRecord = LoginHistory::where('user_id', $userId)
            ->where('session_id', $sessionId)
            ->whereNull('logout_at')
            ->first();
        
        \Log::debug('Login Record Found: ' . ($loginRecord ? 'Yes' : 'No'));
        
        if ($loginRecord) {
            \Log::debug('Login Record Before Update: ' . json_encode($loginRecord->toArray()));
            
            $now = Carbon::now();
            // Always use the database record's login_at for consistent duration calculation
            $loginTime = $loginRecord->login_at;
            
            \Log::debug('Now: ' . $now);
            \Log::debug('Login Time: ' . $loginTime);
            
            // Ensure duration is never negative
            $duration = max(0, $now->timestamp - $loginTime->timestamp);
            \Log::debug('Calculated Duration: ' . $duration);
            
            $loginRecord->update([
                'logout_at' => $now,
                'logout_reason' => $reason,
                'duration_seconds' => $duration,
            ]);
            
            \Log::debug('Login Record After Update: ' . json_encode($loginRecord->fresh()->toArray()));
        } else {
            \Log::debug('No active session found for logout');
        }
        
        \Log::debug('=== LoginTrackingService@recordLogout END ===');
    }
    
    /**
     * Update session label.
     *
     * @param int $userId
     * @param string|null $sessionId
     * @param string $label
     * @return bool
     */
    public function updateSessionLabel(int $userId, ?string $sessionId = null, string $label): bool
    {
        \Log::debug('=== LoginTrackingService@updateSessionLabel START ===');
        \Log::debug('User ID: ' . $userId);
        \Log::debug('Session ID: ' . ($sessionId ?? Session::getId()));
        \Log::debug('New Label: ' . $label);
        
        $sessionId = $sessionId ?? Session::getId();
        
        // Find the active session
        $loginRecord = LoginHistory::where('user_id', $userId)
            ->where('session_id', $sessionId)
            ->whereNull('logout_at')
            ->first();
        
        \Log::debug('Login Record Found: ' . ($loginRecord ? 'Yes' : 'No'));
        
        if ($loginRecord) {
            \Log::debug('Login Record Before Update: ' . json_encode($loginRecord->toArray()));
            
            $loginRecord->update([
                'session_label' => $label,
            ]);
            
            \Log::debug('Login Record After Update: ' . json_encode($loginRecord->fresh()->toArray()));
            
            // Also update the session
            Session::put('session_label', $label);
            \Log::debug('Session Updated with New Label');
            
            \Log::debug('=== LoginTrackingService@updateSessionLabel END ===');
            return true;
        }
        
        \Log::debug('Failed to update session label - no active session found');
        \Log::debug('=== LoginTrackingService@updateSessionLabel END ===');
        return false;
    }
    
    /**
     * Transfer session to another device.
     *
     * @param int $userId
     * @param string $currentSessionId
     * @param string $newSessionId
     * @return bool
     */
    public function transferSession(int $userId, string $currentSessionId, string $newSessionId): bool
    {
        \Log::debug('=== LoginTrackingService@transferSession START ===');
        \Log::debug('User ID: ' . $userId);
        \Log::debug('Current Session ID: ' . $currentSessionId);
        \Log::debug('New Session ID: ' . $newSessionId);
        
        // Find the current session
        $currentSession = LoginHistory::where('user_id', $userId)
            ->where('session_id', $currentSessionId)
            ->whereNull('logout_at')
            ->first();
        
        \Log::debug('Current Session Found: ' . ($currentSession ? 'Yes' : 'No'));
        
        if (!$currentSession) {
            \Log::debug('Failed to transfer session - current session not found');
            \Log::debug('=== LoginTrackingService@transferSession END ===');
            return false;
        }
        
        \Log::debug('Current Session Before Update: ' . json_encode($currentSession->toArray()));
        
        // Calculate duration correctly
        $loginTime = $currentSession->login_at;
        $now = Carbon::now();
        $duration = max(0, $now->timestamp - $loginTime->timestamp);
        
        \Log::debug('Login Time: ' . $loginTime);
        \Log::debug('Now: ' . $now);
        \Log::debug('Duration: ' . $duration);
        
        // Update the current session
        $currentSession->update([
            'transferred_to' => $newSessionId,
            'logout_at' => $now,
            'logout_reason' => 'transferred',
            'duration_seconds' => $duration,
        ]);
        
        \Log::debug('Current Session After Update: ' . json_encode($currentSession->fresh()->toArray()));
        \Log::debug('=== LoginTrackingService@transferSession END ===');
        
        return true;
    }
    
    /**
     * Generate a device fingerprint for the current request.
     *
     * @param Request $request
     * @return string
     */
    protected function generateDeviceFingerprint(Request $request): string
    {
        // Create a consistent fingerprint based on device characteristics
        $components = [
            $request->ip(),
            $request->userAgent(),
            Agent::browser(),
            Agent::browserVersion(),
            Agent::platform(),
            Agent::platformVersion(),
            Agent::deviceType()
        ];
        
        // Filter out null values and join with a separator
        $fingerprint = md5(implode('|', array_filter($components)));
        
        // Store the fingerprint in the session for consistency
        Session::put('device_fingerprint', $fingerprint);
        
        return $fingerprint;
    }

    /**
     * Check if a device fingerprint has changed during a session.
     *
     * @param Request $request
     * @param int $userId
     * @param string|null $sessionId
     * @return bool True if fingerprint has changed (potential session hijacking)
     */
    public function checkFingerprintChanged(Request $request, int $userId, ?string $sessionId = null): bool
    {
        \Log::debug('=== LoginTrackingService@checkFingerprintChanged START ===');
        \Log::debug('User ID: ' . $userId);
        \Log::debug('Session ID: ' . ($sessionId ?? Session::getId()));
        
        $sessionId = $sessionId ?? Session::getId();
        $currentFingerprint = $this->generateDeviceFingerprint($request);
        
        \Log::debug('Current Fingerprint: ' . $currentFingerprint);
        
        // Check if this is a transferred session from session data
        $isTransferredSession = Session::has('transferred_from_session');
        \Log::debug('Is Transferred Session (from session): ' . ($isTransferredSession ? 'Yes' : 'No'));
        
        // Find the active session
        $loginRecord = LoginHistory::where('user_id', $userId)
            ->where('session_id', $sessionId)
            ->whereNull('logout_at')
            ->first();
        
        \Log::debug('Login Record Found: ' . ($loginRecord ? 'Yes' : 'No'));
        
        if (!$loginRecord || !$loginRecord->device_fingerprint) {
            \Log::debug('No fingerprint to compare - session not found or no fingerprint stored');
            \Log::debug('=== LoginTrackingService@checkFingerprintChanged END ===');
            return false;
        }
        
        \Log::debug('Stored Fingerprint: ' . $loginRecord->device_fingerprint);
        
        // Also check if this is a transferred session from database
        $isTransferredFromDb = $loginRecord->transferred_from || 
                              $loginRecord->is_transferred || 
                              LoginHistory::where('transferred_to', $sessionId)->exists();
        
        \Log::debug('Is Transferred Session (from DB): ' . ($isTransferredFromDb ? 'Yes' : 'No'));
        
        // Combined check for transferred session
        $isTransferredSession = $isTransferredSession || $isTransferredFromDb;
        \Log::debug('Is Transferred Session (combined): ' . ($isTransferredSession ? 'Yes' : 'No'));
        
        // If this is a transferred session, update the fingerprint instead of flagging it
        if ($isTransferredSession) {
            // Update the record with the new fingerprint
            $loginRecord->update([
                'device_fingerprint' => $currentFingerprint,
                'fingerprint_changed' => false // Reset this flag if it was set
            ]);
            \Log::debug('Transferred session - updated fingerprint');
            \Log::debug('=== LoginTrackingService@checkFingerprintChanged END ===');
            return false;
        }
        
        // Check if fingerprint has changed
        $changed = $loginRecord->device_fingerprint !== $currentFingerprint;
        \Log::debug('Fingerprint Changed: ' . ($changed ? 'Yes' : 'No'));
        
        // Update the record if changed
        if ($changed) {
            \Log::debug('Updating record to mark fingerprint changed');
            $loginRecord->update([
                'fingerprint_changed' => true,
            ]);
        }
        
        \Log::debug('=== LoginTrackingService@checkFingerprintChanged END ===');
        return $changed;
    }
    
    /**
     * Calculate risk score for a login attempt.
     *
     * @param Request $request
     * @param int|null $userId
     * @param bool $newDevice
     * @param bool $failedAttempt
     * @return int Score from 0-100
     */
    protected function calculateRiskScore(
        Request $request, 
        ?int $userId, 
        bool $newDevice = false,
        bool $failedAttempt = false
    ): int {
        $score = 0;
        $ipAddress = $request->ip();
        
        // New device is risky
        if ($newDevice) {
            $score += 20;
        }
        
        // Failed attempt is risky
        if ($failedAttempt) {
            $score += 25;
        }
        
        // Check if IP is suspicious
        if ($this->isProxyOrVpn($ipAddress)) {
            $score += 30;
        }
        
        // Check if this is an unusual time for this user
        if ($userId && $this->isUnusualLoginTime($userId)) {
            $score += 15;
        }
        
        // Check if this is an unusual location for this user
        if ($userId && $this->isUnusualLocation($userId, $ipAddress)) {
            $score += 20;
        }
        
        // Check if this is a headless browser
        if ($this->detectHeadlessBrowser($request->userAgent())) {
            $score += 25;
        }
        
        // Check if this is a bot
        if (Agent::isBot()) {
            $score += 30;
        }
        
        // Cap the score at 100
        return min($score, 100);
    }
    
    /**
     * Generate a default session label based on device information.
     *
     * @return string
     */
    protected function generateSessionLabel(): string
    {
        $deviceType = ucfirst(Agent::deviceType() ?: 'Unknown');
        $browser = Agent::browser() ?: 'Unknown Browser';
        $platform = Agent::platform() ?: 'Unknown OS';
        
        return "$deviceType - $browser on $platform";
    }
    
    /**
     * Check if this is an unusual login time for the user.
     *
     * @param int $userId
     * @return bool
     */
    protected function isUnusualLoginTime(int $userId): bool
    {
        $currentHour = Carbon::now()->hour;
        
        // Get user's typical login hours
        $typicalHours = LoginHistory::where('user_id', $userId)
            ->where('login_successful', true)
            ->whereNotNull('login_hour')
            ->selectRaw('login_hour, COUNT(*) as count')
            ->groupBy('login_hour')
            ->orderByDesc('count')
            ->limit(5)
            ->pluck('login_hour')
            ->toArray();
        
        // If we don't have enough data, return false
        if (count($typicalHours) < 3) {
            return false;
        }
        
        // Check if current hour is in the typical hours
        return !in_array($currentHour, $typicalHours);
    }
    
    /**
     * Check if this is an unusual location for the user.
     *
     * @param int $userId
     * @param string $ipAddress
     * @return bool
     */
    protected function isUnusualLocation(int $userId, string $ipAddress): bool
    {
        // Get geolocation data for the current IP
        $geoData = $this->getGeoIpData($ipAddress);
        if (!$geoData || !isset($geoData['country_code'])) {
            return false;
        }
        
        // Get user's typical countries
        $typicalCountries = LoginHistory::where('user_id', $userId)
            ->where('login_successful', true)
            ->whereNotNull('country_code')
            ->selectRaw('country_code, COUNT(*) as count')
            ->groupBy('country_code')
            ->orderByDesc('count')
            ->limit(3)
            ->pluck('country_code')
            ->toArray();
        
        // If we don't have enough data, return false
        if (count($typicalCountries) < 2) {
            return false;
        }
        
        // Check if current country is in the typical countries
        return !in_array($geoData['country_code'], $typicalCountries);
    }
    
    /**
     * Check if the IP address is likely a proxy or VPN.
     *
     * @param string $ip
     * @return bool
     */
    protected function isProxyOrVpn(string $ip): bool
    {
        // In a real implementation, you would integrate with a service
        // that can detect proxies and VPNs
        
        // For now, just return false
        return false;
    }
    
    /**
     * Update aggregated user login statistics.
     *
     * @param int $userId
     * @return void
     */
    protected function updateUserLoginStats(int $userId): void
    {
        $today = Carbon::today()->toDateString();
        
        // Get or create today's stats record
        $stats = UserLoginStat::firstOrNew([
            'user_id' => $userId,
            'login_date' => $today,
        ]);
        
        // Count today's logins
        $todaysLogins = LoginHistory::where('user_id', $userId)
            ->whereDate('login_at', $today);
        
        $stats->total_logins = $todaysLogins->count();
        $stats->successful_logins = $todaysLogins->where('login_successful', true)->count();
        $stats->failed_logins = $todaysLogins->where('login_successful', false)->count();
        $stats->suspicious_logins = $todaysLogins->where('suspicious_location', true)->count();
        $stats->new_device_logins = $todaysLogins->where('new_device', true)->count();
        
        // Count unique values
        $stats->unique_ip_count = $todaysLogins->distinct('ip_address')->count('ip_address');
        $stats->unique_device_count = $todaysLogins->distinct()
            ->count(LoginHistory::raw('CONCAT(device_type, browser, platform)'));
        $stats->unique_location_count = $todaysLogins
            ->whereNotNull('country')
            ->distinct()
            ->count(LoginHistory::raw('CONCAT(country, city)'));
        
        // Calculate session duration metrics
        $completedSessions = LoginHistory::where('user_id', $userId)
            ->whereDate('login_at', $today)
            ->whereNotNull('logout_at')
            ->whereNotNull('duration_seconds')
            ->where('duration_seconds', '>', 0);
        
        if ($completedSessions->count() > 0) {
            $stats->avg_session_duration = round($completedSessions->avg('duration_seconds'));
            
            // Median calculation would be more complex, typically requiring raw SQL or a collection
            // This is simplified for this example
            $durations = $completedSessions->pluck('duration_seconds')->toArray();
            sort($durations);
            $count = count($durations);
            $stats->median_session_duration = ($count > 0) 
                ? $durations[intdiv($count, 2)] 
                : null;
        }
        
        $stats->save();
    }
    
    /**
     * Get geolocation data for an IP address.
     * Uses a placeholder implementation - in production you'd integrate a real geolocation service.
     *
     * @param string $ip
     * @return array|null
     */
    protected function getGeoIpData(string $ip): ?array
    {
        // Placeholder implementation
        // In a real implementation, you would integrate with a geolocation service
        // such as MaxMind GeoIP, IP-API, or ipinfo.io
        
        // For local/private IPs, return null or basic data
        if (in_array($ip, ['127.0.0.1', 'localhost', '::1']) || 
            strpos($ip, '192.168.') === 0 || 
            strpos($ip, '10.') === 0) {
            return [
                'country' => 'Local',
                'country_code' => 'LCL',
                'region' => 'Local Network',
                'city' => 'Development',
                'timezone' => config('app.timezone'),
            ];
        }
        
        // For demonstration purposes, return null
        // In production, call your geolocation API here
        return null;
    }
    
    /**
     * Attempt to detect headless browser based on user agent.
     *
     * @param string $userAgent
     * @return bool
     */
    protected function detectHeadlessBrowser(string $userAgent): bool
    {
        $headlessMarkers = [
            'headless',
            'HeadlessChrome',
            'PhantomJS',
            'Playwright',
            'Puppeteer',
            'Selenium',
            'Zombie.js'
        ];
        
        foreach ($headlessMarkers as $marker) {
            if (stripos($userAgent, $marker) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Create a session transfer code.
     *
     * @param string $sessionId
     * @return string The generated transfer code
     */
    public function createSessionTransfer(string $sessionId): string
    {
        \Log::debug('=== LoginTrackingService@createSessionTransfer START ===');
        \Log::debug('Session ID: ' . $sessionId);
        \Log::debug('User ID: ' . auth()->id());
        \Log::debug('Session Data: ' . json_encode(session()->all()));
        
        // Get configuration values
        $codeLength = config('session_transfer.code_length', 8);
        $codeFormat = config('session_transfer.code_format', 'uppercase');
        $expirationTime = config('session_transfer.expiration_time', 600);
        $cachePrefix = config('session_transfer.cache_key_prefix', 'session_transfer:');
        $cacheStore = config('session_transfer.cache_store');
        
        // Generate a unique transfer code based on format
        $transferCode = '';
        switch ($codeFormat) {
            case 'numeric':
                $transferCode = substr(str_pad(mt_rand(0, 999999999), $codeLength, '0', STR_PAD_LEFT), 0, $codeLength);
                break;
            case 'alphanumeric':
                $transferCode = substr(md5(uniqid(mt_rand(), true)), 0, $codeLength);
                break;
            case 'uppercase':
            default:
                $transferCode = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, $codeLength));
                break;
        }
        
        \Log::debug('Generated Transfer Code: ' . $transferCode);
        
        // Get the original login history record to check for cross-entity access
        $loginRecord = LoginHistory::where('session_id', $sessionId)
            ->where('user_id', auth()->id())
            ->whereNull('logout_at')
            ->first();
        
        $isCrossEntityAccess = false;
        $originalEntityId = null;
        $originalRoleId = null;
        
        if ($loginRecord) {
            $isCrossEntityAccess = $loginRecord->is_cross_entity_access;
            $originalEntityId = $loginRecord->original_entity_id;
            $originalRoleId = $loginRecord->original_role_id;
            \Log::debug('Found login record with cross-entity data:');
            \Log::debug('is_cross_entity_access: ' . ($isCrossEntityAccess ? 'true' : 'false'));
            \Log::debug('original_entity_id: ' . ($originalEntityId ?? 'null'));
            \Log::debug('original_role_id: ' . ($originalRoleId ?? 'null'));
        } else {
            \Log::debug('No login record found for session ID: ' . $sessionId);
        }
        
        $cacheData = [
            'session_id' => $sessionId,
            'user_id' => auth()->id(),
            'created_at' => now(),
            'role_id' => session('active_role_id'),
            'entity_id' => session('active_entity_id'),
            'department_id' => session('active_department_id'),
            'attempts' => 0, // Track number of attempts
            'is_cross_entity_access' => $isCrossEntityAccess,
            'original_entity_id' => $originalEntityId,
            'original_role_id' => $originalRoleId,
        ];
        \Log::debug('Cache Data: ' . json_encode($cacheData));
        
        // Store transfer information in cache
        if ($cacheStore) {
            // Use specific cache store if configured
            cache()->store($cacheStore)->put($cachePrefix . $transferCode, $cacheData, $expirationTime);
        } else {
            // Use default cache store
            cache()->put($cachePrefix . $transferCode, $cacheData, $expirationTime);
        }
        
        \Log::debug('=== LoginTrackingService@createSessionTransfer END ===');
        return $transferCode;
    }
} 