<?php

namespace Tests\Feature\Auth;

use App\Livewire\Auth\Login;
use App\Models\Auth\LoginHistory;
use App\Models\Entity\Entity;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\UserRoleAssignment;
use App\Models\User;
use App\Services\LoginTrackingService;
use App\Services\LoginValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Livewire\Livewire;
use Mockery;
use Tests\TestCase;

class SuperAdminTest extends TestCase
{
    use RefreshDatabase;
    
    private LoginValidationService $loginValidator;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->loginValidator = new LoginValidationService();
    }
    
    /** @test */
    public function super_admin_is_correctly_identified()
    {
        $this->assertTrue($this->loginValidator->isSuperAdmin($this->superAdmin));
        $this->assertFalse($this->loginValidator->isSuperAdmin($this->regularUser));
    }
    
    /** @test */
    public function super_admin_can_access_any_entity()
    {
        // Super Admin should be able to access any entity
        $result1 = $this->loginValidator->checkEntityStatus($this->superAdmin, $this->entity1->entity_id);
        $this->assertTrue($result1);
        
        $result2 = $this->loginValidator->checkEntityStatus($this->superAdmin, $this->entity2->entity_id);
        $this->assertTrue($result2);
        
        // Regular user should only be able to access their assigned entity
        $this->regularUser->entity_id = $this->entity1->entity_id;
        $this->regularUser->save();
        
        $result3 = $this->loginValidator->checkEntityStatus($this->regularUser, $this->entity1->entity_id);
        $this->assertTrue($result3);
        
        $result4 = $this->loginValidator->checkEntityStatus($this->regularUser, $this->entity2->entity_id);
        $this->assertIsArray($result4);
        $this->assertEquals('entity_not_found', $result4['error']);
    }
    
    /** @test */
    public function super_admin_can_use_any_role()
    {
        // Super Admin should be able to use any role
        $result1 = $this->loginValidator->checkRoleAssignments($this->superAdmin, null, $this->role1->id);
        $this->assertTrue($result1);
        
        $result2 = $this->loginValidator->checkRoleAssignments($this->superAdmin, null, $this->role2->id);
        $this->assertTrue($result2);
        
        // Regular user should only be able to use their assigned role
        $result3 = $this->loginValidator->checkRoleAssignments($this->regularUser, null, $this->role1->id);
        $this->assertTrue($result3);
        
        $result4 = $this->loginValidator->checkRoleAssignments($this->regularUser, null, $this->role2->id);
        $this->assertIsArray($result4);
    }
    
    /** @test */
    public function super_admin_can_access_cross_entity()
    {
        // Set up session
        Session::shouldReceive('getId')->andReturn('test-session-id');
        Session::shouldReceive('put')->andReturnNull();
        Session::shouldReceive('has')->andReturn(false);
        
        // Super Admin accessing another entity
        $this->superAdmin->entity_id = $this->entity1->entity_id;
        $this->superAdmin->save();
        
        $this->loginValidator->setSessionContext(
            $this->superAdmin, 
            $this->role2->id, 
            $this->entity2->entity_id, 
            'Test Session'
        );
        
        // Regular user should not be able to do cross-entity access
        $this->regularUser->entity_id = $this->entity1->entity_id;
        $this->regularUser->save();
        
        $this->loginValidator->setSessionContext(
            $this->regularUser, 
            $this->role1->id, 
            $this->entity1->entity_id, 
            'Test Session'
        );
    }
    
    /** @test */
    public function super_admin_has_highest_role_level()
    {
        $superAdminLevel = $this->loginValidator->getUserHighestRoleLevel($this->superAdmin);
        $this->assertEquals(LoginValidationService::SUPER_ADMIN_HIERARCHY_LEVEL, $superAdminLevel);
        
        // Create a role with level 2
        $managerRole = SystemRole::create([
            'role_name' => 'Manager',
            'entity_id' => 'ENTITY1',
            'is_active' => true,
            'hierarchy_level' => 2,
            'created_by' => $this->superAdmin->id,
        ]);
        
        // Assign to regular user
        UserRoleAssignment::create([
            'user_id' => $this->regularUser->id,
            'role_id' => $managerRole->id,
            'is_active' => true,
            'approval_status' => 'approved',
            'created_by' => $this->superAdmin->id,
        ]);
        
        $regularUserLevel = $this->loginValidator->getUserHighestRoleLevel($this->regularUser);
        $this->assertEquals(2, $regularUserLevel);
    }

    /**
     * @test
     */
    public function super_admin_can_bypass_entity_selection()
    {
        // Create a Super Admin user
        $superAdmin = User::factory()->create([
            'id' => LoginValidationService::SUPER_ADMIN_USER_ID,
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
        
        // Mock the LoginTrackingService
        $loginTracker = Mockery::mock(LoginTrackingService::class);
        $loginTracker->shouldReceive('recordSuccessfulLogin')
            ->once()
            ->andReturn(new LoginHistory([
                'user_id' => $superAdmin->id,
                'login_successful' => true,
            ]));
            
        // Bind the mock to the container
        $this->app->instance(LoginTrackingService::class, $loginTracker);
        
        // Test the skipAndContinue method
        Livewire::test(Login::class)
            ->set('email', $superAdmin->email)
            ->set('password', 'password')
            ->call('login')
            ->assertSet('showEntitySelector', true) // Entity selector should be shown first
            ->call('skipAndContinue') // Skip entity selection
            ->assertRedirect(route('dashboard')); // Should redirect to dashboard
            
        // Check that the session has unrestricted access flag
        $this->assertTrue(session('is_super_admin'));
        $this->assertTrue(session('unrestricted_access'));
    }
} 