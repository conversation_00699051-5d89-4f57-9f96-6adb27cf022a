<?php

// app/Models/Rbac/UserGuardAccess.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Entity\Entity;
use App\Models\User;
use App\Traits\HasAuditFields;

/**
 * Class UserGuardAccess
 *
 * Controls user access to different authentication guards.
 */
class UserGuardAccess extends Model
{
    use HasAuditFields;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_guard_access';

    protected $fillable = [
        'entity_id',
        'user_id',
        'guard_id',
        'access_from',
        'access_until',
        'is_active',
        'is_approval_required',
        'approval_status',
        'access_notes',
    ];

    protected $casts = [
        'access_from' => 'date',
        'access_until' => 'date',
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
    ];

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // In class UserGuardAccess

    /**
     * Get the access guard type for this access record.
     */
    public function accessGuard(): BelongsTo
    {
        return $this->belongsTo(AccessGuardType::class, 'guard_id');
    }
}
