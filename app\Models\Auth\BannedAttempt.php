<?php

namespace App\Models\Auth;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\LogsDatabaseChanges;

class BannedAttempt extends Model
{
    use SoftDeletes, LogsDatabaseChanges;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'ip_address',
        'user_agent',
        'email_attempted',
        'reason',
        'banned_at',
        'expires_at',
        'banned_until', // Keep for backward compatibility
        'notes',
        'ban_count',
        'banned_by',
        'restored_by',
        'restored_at',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'banned_at' => 'datetime',
        'expires_at' => 'datetime',
        'banned_until' => 'datetime',
        'restored_at' => 'datetime',
        'deleted_at' => 'datetime',
        'ban_count' => 'integer',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Sync expires_at and banned_until for backward compatibility
        static::saving(function ($model) {
            if ($model->expires_at && !$model->banned_until) {
                $model->banned_until = $model->expires_at;
            } elseif ($model->banned_until && !$model->expires_at) {
                $model->expires_at = $model->banned_until;
            }
        });
    }

    /**
     * Check if this ban has expired.
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        if ($this->expires_at === null) {
            return false; // Permanent bans don't expire
        }

        return $this->expires_at->isPast();
    }

    /**
     * Check if this is a permanent ban.
     *
     * @return bool
     */
    public function isPermanent(): bool
    {
        return $this->expires_at === null;
    }

    /**
     * Scope a query to only include active bans.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where(function($query) {
            $query->whereNull('expires_at')
                ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope a query to only include expired bans.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpired($query)
    {
        return $query->whereNotNull('expires_at')
            ->where('expires_at', '<=', now());
    }
}
