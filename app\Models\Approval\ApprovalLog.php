<?php
// app/Models/Approval/ApprovalLog.php
namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use App\Traits\HasAuditFields;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class ApprovalLog
 *
 * Comprehensive audit trail for all approval-related activities.
 * Records every action taken on approval requests for complete traceability.
 *
 * @property int $id
 * @property string $request_id UUID of approval request
 * @property int|null $user_id User who performed the action (null for system)
 * @property string $action Type of action performed
 * @property string|null $details Contextual details about the action
 * @property \Carbon\Carbon $created_at When the action occurred
 * @property \Carbon\Carbon|null $updated_at When the log was last updated
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class ApprovalLog extends Model
{
    use SoftDeletes, HasAuditFields;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'request_id',
        'user_id',
        'action',
        'details',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    /**
     * Available log actions.
     */
    const ACTIONS = [
        'created' => 'Approval request created',
        'submitted' => 'Request submitted for approval',
        'approved' => 'Step approved by user',
        'rejected' => 'Step rejected by user',
        'escalated' => 'Step escalated due to delay',
        'commented' => 'Comment added to request',
        'cancelled' => 'Request cancelled by requester',
        'completed' => 'Request fully approved',
        'expired' => 'Request expired due to timeout',
        'modified' => 'Request data modified',
        'assigned' => 'Approver assigned to step',
        'reminder_sent' => 'Reminder notification sent',
        'workflow_changed' => 'Workflow rules modified',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = ['action_description', 'is_system_action'];

    /**
     * Get human-readable action description.
     *
     * @return string
     */
    public function getActionDescriptionAttribute(): string
    {
        return self::ACTIONS[$this->action] ?? 'Unknown action';
    }

    /**
     * Check if this was a system-generated action.
     *
     * @return bool
     */
    public function getIsSystemActionAttribute(): bool
    {
        return $this->user_id === null;
    }

    /**
     * Get the actor name (user name or "System").
     *
     * @return string
     */
    public function getActorNameAttribute(): string
    {
        return $this->user ? $this->user->name : 'System';
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the approval request this log belongs to.
     *
     * @return BelongsTo
     */
    public function request(): BelongsTo
    {
        return $this->belongsTo(ApprovalRequest::class, 'request_id');
    }

    /**
     * Get the user who performed this action.
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to actions by specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to system actions only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSystemActions($query)
    {
        return $query->whereNull('user_id');
    }

    /**
     * Scope to user actions only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUserActions($query)
    {
        return $query->whereNotNull('user_id');
    }

    /**
     * Scope to specific action type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $action
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to recent activities.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $hours
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>', now()->subHours($hours));
    }

    // ===== STATIC METHODS =====

    /**
     * Log an approval action.
     *
     * @param string $requestId
     * @param string $action
     * @param string|null $details
     * @param int|null $userId
     * @return self
     */
    public static function log(
        string $requestId,
        string $action,
        ?string $details = null,
        ?int $userId = null
    ): self {
        return self::create([
            'request_id' => $requestId,
            'user_id' => $userId ?? auth()->id(),
            'action' => $action,
            'details' => $details,
        ]);
    }

    /**
     * Log a system action.
     *
     * @param string $requestId
     * @param string $action
     * @param string|null $details
     * @return self
     */
    public static function logSystem(
        string $requestId,
        string $action,
        ?string $details = null
    ): self {
        return self::create([
            'request_id' => $requestId,
            'user_id' => null,
            'action' => $action,
            'details' => $details,
        ]);
    }

    /**
     * Get activity timeline for a request.
     *
     * @param string $requestId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTimeline(string $requestId)
    {
        return self::where('request_id', $requestId)
            ->with('user')
            ->orderBy('created_at')
            ->get();
    }

    /**
     * Get user activity statistics.
     *
     * @param int $userId
     * @param \Carbon\Carbon|null $from
     * @param \Carbon\Carbon|null $to
     * @return array
     */
    public static function getUserActivity(
        int $userId,
        ?\Carbon\Carbon $from = null,
        ?\Carbon\Carbon $to = null
    ): array {
        $query = self::byUser($userId);

        if ($from) {
            $query->where('created_at', '>=', $from);
        }

        if ($to) {
            $query->where('created_at', '<=', $to);
        }

        $logs = $query->get();

        return [
            'total_actions' => $logs->count(),
            'approvals' => $logs->where('action', 'approved')->count(),
            'rejections' => $logs->where('action', 'rejected')->count(),
            'comments' => $logs->where('action', 'commented')->count(),
            'most_active_day' => $logs->groupBy(function ($log) {
                return $log->created_at->format('Y-m-d');
            })->sortByDesc(function ($group) {
                return $group->count();
            })->keys()->first(),
        ];
    }
}
