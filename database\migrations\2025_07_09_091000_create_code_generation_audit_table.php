<?php
// database/migrations/2025_07_09_091000_create_code_generation_audit_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration creates the audit trail table for the CodeGenerator service.
     * Every code generation, sequence reset, or administrative action is logged
     * here for compliance, debugging, and analytics purposes.
     *
     * Think of this table as the detailed logbook at our code mint - it records
     * not just what was produced, but who requested it, when, and under what
     * circumstances. This becomes invaluable for:
     *
     * 1. Compliance audits - Prove who generated specific codes
     * 2. Debugging - Understand sequence gaps or unusual patterns
     * 3. Security - Detect potential abuse or unauthorized attempts
     * 4. Analytics - Understand usage patterns and peak times
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('code_generation_audit', function (Blueprint $table) {
            // Primary key for the audit record
            $table->id()->comment('Unique identifier for each audit entry');

            // ============================================================
            // Core Information - What happened?
            // ============================================================

            // The type of code that was generated (e.g., 'supplier', 'purchase_order')
            $table->string('code_type', 50)
                ->comment('The type key from configuration (e.g., supplier, purchase_order)')
                ->index(); // Index for filtering by code type

            // The actual code that was generated
            $table->string('generated_code', 100)
                ->comment('The actual code that was generated (e.g., SUP-MH-0001)')
                ->index(); // Index for searching specific codes

            // The action performed (generate, reset, preview, etc.)
            $table->string('action', 20)
                ->default('generate')
                ->comment('The action performed: generate, reset, preview, failed_attempt')
                ->index(); // Index for filtering by action type

            // ============================================================
            // Who performed the action?
            // ============================================================

            // User who triggered the generation
            $table->unsignedBigInteger('generated_by')
                ->nullable() // Nullable for system-generated codes
                ->comment('User ID who generated the code')
                ->index(); // Index for user activity reports

            // Entity context where generation occurred
            $table->unsignedBigInteger('entity_id')
                ->nullable() // Nullable for super admin actions
                ->comment('Entity ID where the code was generated')
                ->index(); // Index for entity-specific reports

            // ============================================================
            // From where? (Request context)
            // ============================================================

            // IP address for security tracking
            $table->string('ip_address', 45)
                ->nullable() // Nullable for system/console operations
                ->comment('IP address of the request (supports IPv6)')
                ->index(); // Index for security analysis

            // User agent for device tracking
            $table->text('user_agent')
                ->nullable()
                ->comment('Browser/device information from the request');

            // ============================================================
            // Additional Context (Flexible metadata)
            // ============================================================

            // JSON field for extensible metadata
            // This might include: configuration used, validation errors,
            // retry attempts, sequence key, reason for reset, etc.
            $table->json('metadata')
                ->nullable()
                ->comment('Additional context in JSON format');

            // ============================================================
            // When did it happen?
            // ============================================================

            // Single timestamp since we only need creation time for audit logs
            $table->timestamp('created_at')
                ->useCurrent()
                ->comment('When the code was generated')
                ->index(); // Index for time-based queries

            // ============================================================
            // Foreign Key Constraints (Optional)
            // ============================================================

            // Note: We're NOT adding foreign key constraints to users/entities
            // because we want audit records to persist even if users/entities
            // are deleted. This maintains audit integrity.

            // ============================================================
            // Composite Indexes for Common Queries
            // ============================================================

            // For queries like: "Show all purchase orders generated by user X in July"
            $table->index(['generated_by', 'code_type', 'created_at'], 'idx_user_type_time');

            // For queries like: "Show all codes generated by entity Y today"
            $table->index(['entity_id', 'created_at'], 'idx_entity_time');

            // For security queries: "Show all failed attempts from IP Z"
            $table->index(['ip_address', 'action', 'created_at'], 'idx_ip_action_time');
        });

        // ============================================================
        // Create a view for easy reporting (optional but useful)
        // ============================================================

        // This view joins audit data with user and entity names for reports
        // Note: Views are database-specific, so check your database support

        if (config('database.default') === 'mysql' && Schema::hasTable('users')) {
            try {
                // First drop the view if it exists to prevent errors
                DB::statement("DROP VIEW IF EXISTS code_generation_audit_view");
                
                // Check if entities table exists before creating the view
                $entitiesJoin = Schema::hasTable('entities') 
                    ? "LEFT JOIN entities e ON a.entity_id = e.id" 
                    : "";
                
                $entityColumns = Schema::hasTable('entities')
                    ? "e.entity_name, e.entity_type"
                    : "'Unknown' as entity_name, 'Unknown' as entity_type";
                
                DB::statement("
                    CREATE VIEW code_generation_audit_view AS
                    SELECT
                        a.*,
                        u.name as user_name,
                        u.email as user_email,
                        $entityColumns
                    FROM code_generation_audit a
                    LEFT JOIN users u ON a.generated_by = u.id
                    $entitiesJoin
                ");
            } catch (\Exception $e) {
                // Log the error but continue with the migration
                error_log('Failed to create code_generation_audit_view: ' . $e->getMessage());
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * This method ensures clean rollback by dropping the view first
     * (if it exists) and then the table.
     *
     * @return void
     */
    public function down(): void
    {
        // Drop the view first if it exists
        if (config('database.default') === 'mysql') {
            DB::statement("DROP VIEW IF EXISTS code_generation_audit_view");
        }

        // Drop the audit table
        Schema::dropIfExists('code_generation_audit');
    }
};
