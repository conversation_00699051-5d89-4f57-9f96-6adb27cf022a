<?php

// app/Models/Rbac/PermissionGrant.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Entity\Entity;
use App\Traits\HasAuditFields;

/**
 * Class PermissionGrant
 *
 * Grants permissions to roles with optional constraints.
 */
class PermissionGrant extends Model
{
    use HasAuditFields;

    protected $fillable = [
        'entity_id',
        'permission_id',
        'role_id',
        'guard_id',
        'department_id',
        'granted_from',
        'granted_until',
        'is_approval_required',
        'approval_status',
        'is_active',
        'grant_notes',
    ];

    protected $casts = [
        'granted_from' => 'date',
        'granted_until' => 'date',
        'is_approval_required' => 'boolean',
        'is_active' => 'boolean',
    ];

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function permission(): BelongsTo
    {
        return $this->belongsTo(SystemPermission::class, 'permission_id');
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(SystemRole::class, 'role_id');
    }

    // In class PermissionGrant

    /**
     * Get the access guard type associated with the grant.
     */
    public function accessGuard(): BelongsTo
    {
        return $this->belongsTo(AccessGuardType::class, 'guard_id');
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(OrganizationDepartment::class, 'department_id');
    }

    // ===== QUERY SCOPES =====

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        return $query->active()
            ->where(function ($q) {
                $q->whereNull('granted_from')
                    ->orWhere('granted_from', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('granted_until')
                    ->orWhere('granted_until', '>', now());
            });
    }
}
