# SAIMS Project: Features & Models Overview

This document provides a simplified, non-technical summary of all available features and models in the SAIMS project. It is designed for easy understanding by non-developers and stakeholders.

---

## Main Features (All Modules)

### 1. Login & Authentication
- Secure login for all users
- Supports multiple user roles (e.g., admin, manager, staff)
- Passwordless login using magic links
- Email verification required for new users
- Tracks all login sessions and devices
- Allows login from multiple devices (if permitted)
- Prevents duplicate logins for the same role
- Advanced security checks (device, risk, session limits)
- Password reset and management
- Cross-device session transfer
- Monitors for suspicious activity and blocks risky logins

#### Login Module: Step-by-Step Features
- Checks if the user exists and is active
- Ensures the user is approved (if required)
- Verifies the user's email address
- Checks if the user has reached the allowed number of active sessions
- Validates all assigned roles are active and approved
- Ensures no duplicate session for the same role
- Validates department and guard assignments for the user
- Checks if the user's entity (company) is active and approved
- Sets up the session with all user context (role, department, guard, entity)
- Records every login for audit and security
- Redirects user to their dashboard after successful login
- Provides clear error messages for any failed check
- All login checks are centralized for consistency and security

### 2. Registration System
- 9-step registration wizard for new entities (dealers, distributors, suppliers)
- Email verification with OTP (one-time password)
- Entity type selection (dealer or distributor)
- Business information collection (name, contacts, addresses)
- Personal details collection (owner info, password, contact, address)
- Tax information collection (GST, PAN, etc.)
- Upload of business and personal KYC documents
- Additional document uploads (optional)
- Review and submit step before final registration
- Resume registration at any step if interrupted
- All new registrations require admin approval before activation
- Automatic setup of roles, departments, and permissions for new entities
- Sends confirmation and alert emails

### 3. User Profile & Settings
- Manage personal profile information
- Change password and security settings
- Adjust appearance and accessibility preferences

### 4. Approval Workflow System
- Multi-stage approval for various actions (e.g., registration, changes)
- Supports single, sequential, or parallel approval steps
- Role-based approval assignments
- Escalation if approvals are delayed
- Full audit trail of all approval actions

### 5. Role-Based Access Control (RBAC)
- Hierarchical roles (parent-child structure)
- Fine-grained permissions for every action
- Roles and permissions can be time-limited
- Entity-specific roles and permissions
- Supports multiple access types (web, API, mobile)

### 6. Entity Relationship Management
- Organizes companies/entities in a hierarchy (parent-child)
- Tracks business relationships (e.g., who supplies to whom)
- Visualizes and manages entity relationships

### 7. Code Generation System
- Automatically generates unique codes for entities, users, and transactions
- Supports many code types (e.g., supplier, dealer, purchase order)
- Codes can be location-based and follow custom patterns
- Ensures no duplicate codes

### 8. Agent Detection System
- Detects device type (mobile, desktop, tablet, etc.)
- Identifies browser and platform
- Detects bots, proxies, VPNs, and headless browsers
- Helps monitor security and compatibility

### 9. Database Change Logging
- Tracks all changes made to the database
- Records who made each change and when
- Supports audit and compliance needs

### 10. KYC Document Management
- Manages business and personal KYC documents
- Tracks document types, expiration, and verification status
- Supports uploads and secure storage
- Alerts for expiring or missing documents

### 11. Information Management
- Shares addresses, contacts, tax info, and notifications across users and entities
- Supports marking primary contact/address
- Validates and formats all information

### 12. Audit and Compliance
- Tracks all deletions, restores, and status changes
- Allows restoring deleted records
- Manages activation/inactivation of users and entities
- Maintains audit fields for all records

---

## Data Models (Entities)

- **User**: Represents a person who can log in and use the system
- **Entity**: Represents a business unit (supplier, distributor, dealer)
- **Contact**: Stores contact information for users and entities
- **Address**: Stores address information for users and entities
- **KYC**: Stores KYC document details
- **Tax**: Stores tax-related information
- **SystemRole**: Defines user roles
- **SystemPermission**: Defines permissions for actions
- **UserRoleAssignment**: Links users to roles
- **UserDepartmentAssignment**: Links users to departments
- **AccessGuardType**: Defines types of access (web, API, etc.)
- **UserGuardAccess**: Links users to access types
- **EntityRelationship**: Tracks relationships between entities
- **ApprovalWorkflow, ApprovalStep, ApprovalRequest, ApprovalResponse, ApprovalLog, ApprovalEscalation**: Used for approval processes
- **CodeSequence**: Manages code generation
- **LoginHistory**: Tracks user logins
- **BannedAttempt**: Tracks blocked login attempts
- **UserLoginStat**: Stores login analytics

---

## Notes
- Some features are fully implemented in the backend but may not have a user interface yet.
- All features are designed for security, auditability, and scalability.
- The system uses modern design patterns and best practices for maintainability. 