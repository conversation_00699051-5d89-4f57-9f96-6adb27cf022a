@aware([ 'variant' ])

@props([
    'variant' => 'default',
])

@php
// This prevents variants picked up by `@aware()` from other wrapping components like flux::modal from being used here...
$variant = $variant !== 'default' && view()->exists('flux.checkbox.variants.' . $variant)
    ? $variant
    : 'default';
@endphp

@include('flux.checkbox.variants.' . $variant, ['attributes' => $attributes, 'slot' => $slot]) 