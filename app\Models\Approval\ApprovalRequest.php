<?php
// app\Models\Approval\ApprovalRequest.php
namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Traits\HasAuditFields;

/**
 * Class ApprovalRequest
 *
 * Represents a request for approval that follows a specific workflow.
 * Tracks the progress and status of the approval process.
 *
 * @property string $id UUID primary key
 * @property int $workflow_id Which workflow this follows
 * @property string $approvable_type Model class being approved
 * @property string $approvable_id ID of model being approved
 * @property string|null $entity_id Business entity context
 * @property string $action_type Type of action (create/update/delete)
 * @property int $requester_id User who requested approval
 * @property string $status Current status (pending/approved/rejected/cancelled)
 * @property array $request_data Original data for approval
 * @property array|null $approval_data Approval progress tracking
 * @property \Carbon\Carbon|null $submitted_at When formally submitted
 * @property \Carbon\Carbon|null $completed_at When finally resolved
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class ApprovalRequest extends Model
{
    use HasUuids, SoftDeletes, HasAuditFields;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'workflow_id',
        'approvable_type',
        'approvable_id',
        'entity_id',
        'action_type',
        'requester_id',
        'status',
        'request_data',
        'approval_data',
        'submitted_at',
        'completed_at',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'request_data' => 'array',
        'approval_data' => 'array',
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    /**
     * Available statuses.
     */
    const STATUSES = [
        'draft' => 'Not yet submitted',
        'pending' => 'Awaiting approval',
        'approved' => 'Fully approved',
        'rejected' => 'Rejected by approver',
        'cancelled' => 'Cancelled by requester',
        'expired' => 'Expired due to timeout',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = ['is_complete', 'is_pending', 'current_step', 'progress_percentage'];

    /**
     * Initialize approval data when creating.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->approval_data) {
                $model->initializeApprovalData();
            }
        });
    }

    /**
     * Initialize the approval data structure.
     */
    protected function initializeApprovalData(): void
    {
        $steps = $this->workflow->steps()
            ->orderBy('step_number')
            ->get();

        $approvalData = [
            'total_steps' => $steps->count(),
            'completed_steps' => 0,
            'current_step' => 1,
            'steps' => [],
        ];

        foreach ($steps as $step) {
            $approvalData['steps'][$step->step_number] = [
                'role_id' => $step->role_id,
                'status' => 'pending',
                'approver_id' => null,
                'responded_at' => null,
                'is_required' => $step->is_required,
                'is_skipped' => false,
            ];
        }

        $this->approval_data = $approvalData;
    }

    /**
     * Check if request is complete.
     *
     * @return bool
     */
    public function getIsCompleteAttribute(): bool
    {
        return in_array($this->status, ['approved', 'rejected', 'cancelled', 'expired']);
    }

    /**
     * Check if request is pending.
     *
     * @return bool
     */
    public function getIsPendingAttribute(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Get current step number.
     *
     * @return int|null
     */
    public function getCurrentStepAttribute(): ?int
    {
        return $this->approval_data['current_step'] ?? null;
    }

    /**
     * Get progress percentage.
     *
     * @return float
     */
    public function getProgressPercentageAttribute(): float
    {
        $total = $this->approval_data['total_steps'] ?? 1;
        $completed = $this->approval_data['completed_steps'] ?? 0;

        return round(($completed / $total) * 100, 2);
    }

    /**
     * Submit the request for approval.
     *
     * @return bool
     */
    public function submit(): bool
    {
        if ($this->status !== 'draft') {
            return false;
        }

        $this->status = 'pending';
        $this->submitted_at = now();

        return $this->save();
    }

    /**
     * Cancel the request.
     *
     * @param string|null $reason
     * @return bool
     */
    public function cancel(?string $reason = null): bool
    {
        if ($this->is_complete) {
            return false;
        }

        $this->status = 'cancelled';
        $this->completed_at = now();

        if ($reason) {
            $data = $this->approval_data;
            $data['cancellation_reason'] = $reason;
            $this->approval_data = $data;
        }

        return $this->save();
    }

    /**
     * Mark request as approved.
     *
     * @return bool
     */
    public function approve(): bool
    {
        $this->status = 'approved';
        $this->completed_at = now();

        // Apply the approved changes
        $this->applyApprovedChanges();

        return $this->save();
    }

    /**
     * Mark request as rejected.
     *
     * @param string|null $reason
     * @return bool
     */
    public function reject(?string $reason = null): bool
    {
        $this->status = 'rejected';
        $this->completed_at = now();

        if ($reason) {
            $data = $this->approval_data;
            $data['rejection_reason'] = $reason;
            $this->approval_data = $data;
        }

        return $this->save();
    }

    /**
     * Process a step response.
     *
     * @param int $stepNumber
     * @param string $response approved|rejected
     * @param int $approverId
     * @return bool
     */
    public function processStepResponse(int $stepNumber, string $response, int $approverId): bool
    {
        $data = $this->approval_data;

        if (!isset($data['steps'][$stepNumber])) {
            return false;
        }

        // Update step data
        $data['steps'][$stepNumber]['status'] = $response;
        $data['steps'][$stepNumber]['approver_id'] = $approverId;
        $data['steps'][$stepNumber]['responded_at'] = now()->toIso8601String();

        if ($response === 'approved') {
            $data['completed_steps']++;

            // Check if all steps are complete
            if ($this->workflow->isSequential()) {
                if ($data['completed_steps'] >= $data['total_steps']) {
                    $this->approve();
                } else {
                    $data['current_step'] = $stepNumber + 1;
                }
            }
        } else {
            // Check rejection limit
            $rejections = collect($data['steps'])
                ->where('status', 'rejected')
                ->count();

            if ($rejections >= $this->workflow->max_rejections) {
                $this->reject('Maximum rejections reached');
            }
        }

        $this->approval_data = $data;
        return $this->save();
    }

    /**
     * Apply approved changes to the approvable model.
     */
    protected function applyApprovedChanges(): void
    {
        if ($this->action_type === 'create' && $this->approvable) {
            // Activate the created entity/user
            $this->approvable->update([
                'is_active' => true,
                'approval_status' => 'approved',
                'is_approval_required' => false,
            ]);
        }
    }

    /**
     * Get pending approvers for current step.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getPendingApprovers()
    {
        if (!$this->is_pending) {
            return collect();
        }

        $currentStep = $this->workflow->steps()
            ->where('step_number', $this->current_step)
            ->first();

        if (!$currentStep) {
            return collect();
        }

        // Get users with the required role
        return User::whereHas('roles', function ($query) use ($currentStep) {
            $query->where('role_id', $currentStep->role_id);
        })->get();
    }

    /**
     * Check if user can approve current step.
     *
     * @param User $user
     * @return bool
     */
    public function canUserApprove(User $user): bool
    {
        if (!$this->is_pending) {
            return false;
        }

        // Check self-approval
        if (!$this->workflow->allow_self_approval && $user->id === $this->requester_id) {
            return false;
        }

        // Check if user has required role for current step
        $currentStep = $this->workflow->steps()
            ->where('step_number', $this->current_step)
            ->first();

        if (!$currentStep) {
            return false;
        }

        // This would check user roles once RBAC is implemented
        // return $user->hasRole($currentStep->role_id);
        return true;
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the workflow this request follows.
     *
     * @return BelongsTo
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'workflow_id');
    }

    /**
     * Get the model being approved.
     *
     * @return MorphTo
     */
    public function approvable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who requested approval.
     *
     * @return BelongsTo
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_id');
    }

    /**
     * Get all responses for this request.
     *
     * @return HasMany
     */
    public function responses(): HasMany
    {
        return $this->hasMany(ApprovalResponse::class, 'request_id')
            ->orderBy('step_number');
    }

    /**
     * Get approved responses.
     *
     * @return HasMany
     */
    public function approvals(): HasMany
    {
        return $this->responses()->where('response_type', 'approved');
    }

    /**
     * Get rejected responses.
     *
     * @return HasMany
     */
    public function rejections(): HasMany
    {
        return $this->responses()->where('response_type', 'rejected');
    }

    /**
     * Get escalations for this request.
     *
     * @return HasMany
     */
    public function escalations(): HasMany
    {
        return $this->hasMany(ApprovalEscalation::class, 'request_id');
    }

    /**
     * Get logs for this request.
     *
     * @return HasMany
     */
    public function logs(): HasMany
    {
        return $this->hasMany(ApprovalLog::class, 'request_id')
            ->orderBy('created_at');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to pending requests.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to completed requests.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->whereIn('status', ['approved', 'rejected', 'cancelled', 'expired']);
    }

    /**
     * Scope to approved requests.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to requests by requester.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRequester($query, int $userId)
    {
        return $query->where('requester_id', $userId);
    }

    /**
     * Scope to requests for entity.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $entityId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEntity($query, string $entityId)
    {
        return $query->where('entity_id', $entityId);
    }

    /**
     * Scope to requests awaiting user approval.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAwaitingUserApproval($query, int $userId)
    {
        return $query->pending()
            ->whereHas('workflow.steps', function ($q) use ($userId) {
                // This would check user roles once implemented
                $q->where('step_number', $this->current_step);
            });
    }
}
