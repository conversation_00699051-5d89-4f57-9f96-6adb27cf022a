# Login and Session Database Change Logging System

## Overview

The system automatically logs all changes (create, update, delete, restore) to the following tables:

- `users` (authentication fields)
- `sessions`
- `login_histories`
- `user_login_stats`
- `banned_attempts`
- `password_reset_tokens`

All changes are logged to a dedicated log file: `storage/logs/db-changes.log`

## Implementation

The logging system consists of several components:

1. **Custom Log Channel**: A dedicated log channel named `logdb` in `config/logging.php`
2. **DatabaseChangeLogger Service**: Handles formatting and writing log entries
3. **LogsDatabaseChanges Trait**: Added to models for automatic logging
4. **Model Events**: Triggered on create, update, delete, and restore operations

### Log Format

Each log entry is formatted as a JSON object with the following structure:

```json
{
  "timestamp": "2023-07-15 14:30:45.123456",
  "user_id": 1,
  "ip_address": "***********",
  "action": "updated",
  "table": "login_histories",
  "model": "App\\Models\\Auth\\LoginHistory",
  "id": 123,
  "changes": {
    "browser": {
      "from": null,
      "to": "Chrome"
    },
    "platform": {
      "from": null,
      "to": "Windows"
    }
  }
}
```

For created records, the complete attributes are logged. For updates, only the changed fields are logged with their old and new values.

## Viewing Logs

You can view the log file using standard commands:

```bash
# View the entire log
cat storage/logs/db-changes.log

# Monitor log in real-time
tail -f storage/logs/db-changes.log

# Filter for specific actions
grep '"action":"updated"' storage/logs/db-changes.log
```

## Security Considerations

The log includes sensitive information such as:
- IP addresses
- User IDs
- Session information
- Security risk indicators

Ensure that the log file has appropriate access restrictions and is included in backup policies.

## Performance Impact

The logging system adds minimal overhead to database operations:
- Small memory increase for tracking changed attributes
- Single log write per database operation
- Logs are rotated daily to prevent excessive file sizes

For high-traffic systems, consider monitoring the size and performance impact of the logging system. 