@echo off
echo Creating backup of session management module...
echo -----------------------------------
echo.

rem Create timestamp for the backup folder
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%c-%%a-%%b)
for /f "tokens=1-2 delims=: " %%a in ('time /t') do (set mytime=%%a%%b)
set timestamp=%mydate%_%mytime%

rem Create main backup directory with timestamp
echo Creating main backup directory with timestamp: %timestamp%
mkdir "backup\session_management_%timestamp%" 2>nul
set BACKUP_DIR=backup\session_management_%timestamp%
echo.

rem Create directory structure
echo Creating directory structure...
mkdir "%BACKUP_DIR%\app\Http\Controllers\Auth" 2>nul
mkdir "%BACKUP_DIR%\app\Http\Middleware" 2>nul
mkdir "%BACKUP_DIR%\app\Livewire\Auth" 2>nul
mkdir "%BACKUP_DIR%\app\Models\Auth" 2>nul
mkdir "%BACKUP_DIR%\app\Services" 2>nul
mkdir "%BACKUP_DIR%\resources\views\auth" 2>nul
mkdir "%BACKUP_DIR%\resources\views\livewire\auth" 2>nul
mkdir "%BACKUP_DIR%\resources\views\components" 2>nul
mkdir "%BACKUP_DIR%\routes" 2>nul
mkdir "%BACKUP_DIR%\database\migrations" 2>nul
mkdir "%BACKUP_DIR%\tests\Feature\Auth" 2>nul
mkdir "%BACKUP_DIR%\tests\Unit" 2>nul
echo Directory structure created successfully.
echo.

rem Backup Controllers
echo Backing up Controllers...
if exist app\Http\Controllers\Auth\SessionDashboardController.php copy app\Http\Controllers\Auth\SessionDashboardController.php "%BACKUP_DIR%\app\Http\Controllers\Auth\" /Y
if exist app\Http\Controllers\Auth\SessionController.php copy app\Http\Controllers\Auth\SessionController.php "%BACKUP_DIR%\app\Http\Controllers\Auth\" /Y
if exist app\Http\Controllers\DebugController.php copy app\Http\Controllers\DebugController.php "%BACKUP_DIR%\app\Http\Controllers\" /Y
echo.

rem Backup Middleware
echo Backing up Middleware...
if exist app\Http\Middleware\CheckActiveSessions.php copy app\Http\Middleware\CheckActiveSessions.php "%BACKUP_DIR%\app\Http\Middleware\" /Y
if exist app\Http\Middleware\UpdateLastActivity.php copy app\Http\Middleware\UpdateLastActivity.php "%BACKUP_DIR%\app\Http\Middleware\" /Y
echo.

rem Backup Livewire Components
echo Backing up Livewire Components...
if exist app\Livewire\Auth\SessionManagement.php copy app\Livewire\Auth\SessionManagement.php "%BACKUP_DIR%\app\Livewire\Auth\" /Y
if exist app\Livewire\Auth\SessionTransfer.php copy app\Livewire\Auth\SessionTransfer.php "%BACKUP_DIR%\app\Livewire\Auth\" /Y
echo.

rem Backup Models
echo Backing up Models...
if exist app\Models\Auth\LoginHistory.php copy app\Models\Auth\LoginHistory.php "%BACKUP_DIR%\app\Models\Auth\" /Y
if exist app\Models\Auth\UserLoginStat.php copy app\Models\Auth\UserLoginStat.php "%BACKUP_DIR%\app\Models\Auth\" /Y
echo.

rem Backup Services
echo Backing up Services...
if exist app\Services\LoginTrackingService.php copy app\Services\LoginTrackingService.php "%BACKUP_DIR%\app\Services\" /Y
if exist app\Services\LoginValidationService.php copy app\Services\LoginValidationService.php "%BACKUP_DIR%\app\Services\" /Y
echo.

rem Backup Views
echo Backing up Views...
if exist resources\views\auth\session-dashboard.blade.php copy resources\views\auth\session-dashboard.blade.php "%BACKUP_DIR%\resources\views\auth\" /Y
if exist resources\views\livewire\auth\session-management.blade.php copy resources\views\livewire\auth\session-management.blade.php "%BACKUP_DIR%\resources\views\livewire\auth\" /Y
if exist resources\views\livewire\auth\session-transfer.blade.php copy resources\views\livewire\auth\session-transfer.blade.php "%BACKUP_DIR%\resources\views\livewire\auth\" /Y
if exist resources\views\components\session-timer.blade.php copy resources\views\components\session-timer.blade.php "%BACKUP_DIR%\resources\views\components\" /Y
echo.

rem Backup Routes
echo Backing up Routes...
if exist routes\auth.php copy routes\auth.php "%BACKUP_DIR%\routes\" /Y
if exist routes\web.php copy routes\web.php "%BACKUP_DIR%\routes\" /Y
echo.

rem Backup Migrations
echo Backing up Migrations...
if exist database\migrations\2025_06_03_134438_create_login_activities_table.php copy database\migrations\2025_06_03_134438_create_login_activities_table.php "%BACKUP_DIR%\database\migrations\" /Y
if exist database\migrations\2025_07_15_add_rbac_fields_to_login_histories.php copy database\migrations\2025_07_15_add_rbac_fields_to_login_histories.php "%BACKUP_DIR%\database\migrations\" /Y
if exist database\migrations\2025_07_16_add_session_management_fields_to_login_histories.php copy database\migrations\2025_07_16_add_session_management_fields_to_login_histories.php "%BACKUP_DIR%\database\migrations\" /Y
if exist database\migrations\2025_07_23_202133_add_is_transferred_to_login_histories.php copy database\migrations\2025_07_23_202133_add_is_transferred_to_login_histories.php "%BACKUP_DIR%\database\migrations\" /Y
if exist database\migrations\2025_07_23_205516_create_session_module_export.php copy database\migrations\2025_07_23_205516_create_session_module_export.php "%BACKUP_DIR%\database\migrations\" /Y
echo.

rem Backup Tests
echo Backing up Tests...
if exist tests\Feature\Auth\SessionManagementTest.php copy tests\Feature\Auth\SessionManagementTest.php "%BACKUP_DIR%\tests\Feature\Auth\" /Y
if exist tests\Unit\CheckActiveSessionsTest.php copy tests\Unit\CheckActiveSessionsTest.php "%BACKUP_DIR%\tests\Unit\" /Y
if exist tests\Unit\LoginValidationServiceTest.php copy tests\Unit\LoginValidationServiceTest.php "%BACKUP_DIR%\tests\Unit\" /Y
echo.

rem Count files copied
set /a total_files=0
for /r "%BACKUP_DIR%" %%F in (*) do set /a total_files+=1

echo -----------------------------------
echo Backup completed successfully!
echo Total files backed up: %total_files%
echo All session management module files have been copied to %BACKUP_DIR%
echo -----------------------------------
echo.

pause 