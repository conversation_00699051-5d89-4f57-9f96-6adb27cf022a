@props(['level'])

@php
$levelConfig = [
    1 => ['label' => 'Super Admin', 'color' => 'red', 'icon' => 'shield-exclamation'],
    2 => ['label' => 'Admin Manager', 'color' => 'orange', 'icon' => 'shield-check'],
    3 => ['label' => 'Administrator', 'color' => 'blue', 'icon' => 'user-circle'],
];

$config = $levelConfig[$level] ?? ['label' => "Level {$level}", 'color' => 'gray', 'icon' => 'user'];
@endphp

<div class="flex items-center gap-2">
    <flux:badge variant="solid" color="{{ $config['color'] }}">
        <flux:icon name="{{ $config['icon'] }}" class="h-3 w-3" />
        {{ __('Level :level', ['level' => $level]) }}
    </flux:badge>
    <flux:text class="text-xs text-zinc-500">{{ $config['label'] }}</flux:text>
</div>
