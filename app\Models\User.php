<?php
// app/Models/User.php - UPDATED VERSION
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use App\Traits\HasAuditFields;
use App\Traits\HasActivation; // ✅ NOW USING THE TRAIT
use App\Traits\LogsDatabaseChanges;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use App\Models\Entity\Entity;
use App\Models\Information\Contact;
use App\Models\Information\Address;
use App\Models\Information\Kyc;
use App\Models\Information\Tax;
use App\Models\SiteSetting\Notification;
use App\Models\Auth\LoginHistory;
use App\Models\Auth\UserLoginStat;

/**
 * Class User
 *
 * [Same docblock as before...]
 */
class User extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes, HasAuditFields, HasActivation, LogsDatabaseChanges; // ✅ ADDED LogsDatabaseChanges

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'email',
        'user_id',
        'phone',
        'entity_id',
        'email_verified_at',
        'is_active',
        'multi_login',
        'is_approval_required',
        'approval_status',
        'last_login_at',
        'password',
        'activated_by',
        'activated_at',
        'inactivated_by',
        'inactivated_at',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
        'last_login_at' => 'datetime',
        'activated_at' => 'datetime',
        'inactivated_at' => 'datetime',
        'restored_at' => 'datetime',
        'password' => 'hashed',
        'multi_login' => 'integer',
    ];

    
    /**
     * Get the user's initials from their name.
     * Used for avatar placeholders.
     *
     * @return string
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->map(fn(string $name) => Str::of($name)->substr(0, 1))
            ->implode('');
    }
    /**
     * Get the user's initials from their name.
     * Used for avatar placeholders.
     *
     * @return string
     */
    public function getInitialsAttribute(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    // ✅ REMOVED: isActive() method - now using trait version as base
    // ✅ ADDED: Business-specific methods that build on trait functionality

    /**
     * Check if the user is operationally active.
     * User must be active AND approved to be considered operational.
     *
     * @return bool
     */
    public function isOperational(): bool
    {
        return $this->isActive() && $this->approval_status === 'approved';
    }

    /**
     * Check if user can login based on email verification and operational status.
     *
     * @return bool
     */
    public function canLogin(): bool
    {
        return $this->isOperational() && $this->email_verified_at !== null;
    }

    // ===== ENTITY RELATIONSHIPS =====

    /**
     * Get the entity this user belongs to.
     *
     * @return BelongsTo
     */
    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class);
    }

    // ===== AUTHENTICATION & SECURITY RELATIONSHIPS =====

    /**
     * Get all login history records for this user.
     *
     * @return HasMany
     */
    public function loginHistories(): HasMany
    {
        return $this->hasMany(LoginHistory::class)->orderBy('login_at', 'desc');
    }

    /**
     * Get aggregated login statistics for this user.
     *
     * @return HasMany
     */
    public function loginStats(): HasMany
    {
        return $this->hasMany(UserLoginStat::class);
    }

    // ===== RBAC RELATIONSHIPS =====

    /**
     * Get all role assignments for this user.
     *
     * @return HasMany
     */
    public function roleAssignments(): HasMany
    {
        return $this->hasMany(\App\Models\Rbac\UserRoleAssignment::class);
    }

    // ===== POLYMORPHIC RELATIONSHIPS =====

    /**
     * Get all addresses associated with this user.
     * Users can have multiple addresses (home, office, etc.)
     *
     * @return MorphToMany
     */
    public function addresses(): MorphToMany
    {
        return $this->morphToMany(Address::class, 'addressable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all contacts associated with this user.
     *
     * @return MorphToMany
     */
    public function contacts(): MorphToMany
    {
        return $this->morphToMany(Contact::class, 'contactable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all KYC documents associated with this user.
     *
     * @return MorphToMany
     */
    public function kycs(): MorphToMany
    {
        return $this->morphToMany(Kyc::class, 'kycable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all tax information associated with this user.
     *
     * @return MorphToMany
     */
    public function taxes(): MorphToMany
    {
        return $this->morphToMany(Tax::class, 'taxable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get notification preferences for this user.
     *
     * @return MorphToMany
     */
    public function notifications(): MorphToMany
    {
        return $this->morphToMany(Notification::class, 'notifiable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    // ===== AUDIT RELATIONSHIPS =====
    // ✅ REMOVED: activatedBy() and inactivatedBy() - now provided by HasActivation trait

    /**
     * Get the user who created this record.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this record.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who soft deleted this record.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored this record.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    // ===== HELPER METHODS =====

    /**
     * Get the primary address for this user.
     *
     * @return Address|null
     */
    public function getPrimaryAddress(): ?Address
    {
        return $this->addresses()->wherePivot('is_primary', true)->first();
    }

    /**
     * Get the primary contact for this user.
     *
     * @return Contact|null
     */
    public function getPrimaryContact(): ?Contact
    {
        return $this->contacts()->wherePivot('is_primary', true)->first();
    }

    /**
     * Get the primary notification preference for this user.
     *
     * @return Notification|null
     */
    public function getPrimaryNotification(): ?Notification
    {
        return $this->notifications()->wherePivot('is_primary', true)->first();
    }

    // ===== QUERY SCOPES =====
    // ✅ REMOVED: scopeActive() - now provided by HasActivation trait

    /**
     * Scope to only approved users.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope to only verified users.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * Scope to users pending approval.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePendingApproval($query)
    {
        return $query->where('is_approval_required', true)
            ->where('approval_status', 'pending');
    }

    /**
     * Scope to users that are operationally active.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOperational($query)
    {
        return $query->active()->approved();
    }

    /**
     * Scope to users that can login.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCanLogin($query)
    {
        return $query->operational()->verified();
    }
}
