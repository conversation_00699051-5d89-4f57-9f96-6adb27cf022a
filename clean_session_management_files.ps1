param (
    [string]$backupDirectory = "backup/session_management_*"
)

# Find the most recent session management backup directory
$targetDir = Get-ChildItem -Path $backupDirectory -Directory | Sort-Object -Property LastWriteTime -Descending | Select-Object -First 1

if (-not $targetDir) {
    Write-Host "No session management backup directory found matching pattern: $backupDirectory" -ForegroundColor Red
    exit 1
}

Write-Host "Processing files in directory: $($targetDir.FullName)" -ForegroundColor Cyan

# Get all PHP files in the backup directory (recursively)
$phpFiles = Get-ChildItem -Path $targetDir.FullName -Filter "*.php" -Recurse

$totalFiles = $phpFiles.Count
$processedFiles = 0

foreach ($file in $phpFiles) {
    Write-Host "Processing file: $($file.FullName)" -ForegroundColor Yellow
    
    # Read file content
    $content = Get-Content -Path $file.FullName -Raw
    
    # Remove multi-line comments (/* ... */)
    $content = [regex]::Replace($content, '/\*[\s\S]*?\*/', '')
    
    # Remove single-line comments (// ...)
    $content = [regex]::Replace($content, '//.*', '')
    
    # Remove doc-block comments (/** ... */)
    $content = [regex]::Replace($content, '/\*\*[\s\S]*?\*/', '')
    
    # Remove blank lines
    $content = [regex]::Replace($content, '^\s*\r?\n', '', [System.Text.RegularExpressions.RegexOptions]::Multiline)
    
    # Save the cleaned content back to the file
    $content | Set-Content -Path $file.FullName -NoNewline
    
    $processedFiles++
    Write-Progress -Activity "Cleaning PHP Files" -Status "$processedFiles of $totalFiles files processed" -PercentComplete (($processedFiles / $totalFiles) * 100)
}

Write-Host "Completed! Cleaned $processedFiles PHP files in $($targetDir.Name)" -ForegroundColor Green

# Also clean any Blade template files
$bladeFiles = Get-ChildItem -Path $targetDir.FullName -Filter "*.blade.php" -Recurse

if ($bladeFiles.Count -gt 0) {
    Write-Host "Processing $($bladeFiles.Count) Blade template files..." -ForegroundColor Cyan
    
    foreach ($file in $bladeFiles) {
        Write-Host "Processing Blade file: $($file.FullName)" -ForegroundColor Yellow
        
        # Read file content
        $content = Get-Content -Path $file.FullName -Raw
        
        # Remove HTML comments (<!-- ... -->)
        $content = [regex]::Replace($content, '<!--[\s\S]*?-->', '')
        
        # Remove PHP comments in Blade files
        $content = [regex]::Replace($content, '{{--[\s\S]*?--}}', '')
        
        # Remove blank lines
        $content = [regex]::Replace($content, '^\s*\r?\n', '', [System.Text.RegularExpressions.RegexOptions]::Multiline)
        
        # Save the cleaned content back to the file
        $content | Set-Content -Path $file.FullName -NoNewline
    }
    
    Write-Host "Completed! Cleaned $($bladeFiles.Count) Blade files in $($targetDir.Name)" -ForegroundColor Green
}

Write-Host "All files have been cleaned successfully!" -ForegroundColor Green 