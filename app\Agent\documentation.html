<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Custom Agent - Documentation</title>
    <style>
        :root {
            --primary: #4f46e5;
            --secondary: #f9fafb;
            --text: #1f2937;
            --light-text: #6b7280;
            --border: #e5e7eb;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            border-bottom: 1px solid var(--border);
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        h1 {
            color: var(--primary);
            margin-bottom: 10px;
        }

        h2 {
            color: var(--primary);
            margin-top: 40px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border);
        }

        h3 {
            margin-top: 30px;
            color: var(--text);
        }

        code {
            background-color: var(--secondary);
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.9em;
        }

        pre {
            background-color: var(--secondary);
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            border-left: 3px solid var(--primary);
        }

        .note {
            background-color: #e0f2fe;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 3px solid #0ea5e9;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        th,
        td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }

        th {
            background-color: var(--secondary);
            font-weight: 600;
        }

        .method {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .method-name {
            font-weight: bold;
            color: var(--primary);
        }

        .method-return {
            color: var(--light-text);
            font-style: italic;
        }
    </style>
</head>

<body>
    <header>
        <h1>Laravel Custom Agent</h1>
        <p>A comprehensive, dependency-free user agent detection package for Laravel that provides extensive device,
            browser, and platform detection capabilities with advanced security features.</p>
    </header>

    <section id="installation">
        <h2>Installation</h2>

        <h3>Manual Installation</h3>
        <ol>
            <li>Copy the following files to your Laravel project:
                <pre><code>app/Agent/Agent.php
app/Agent/Facades/Agent.php
app/Agent/AgentServiceProvider.php
config/agent.php</code></pre>
            </li>
            <li>Register the service provider in <code>config/app.php</code> or <code>bootstrap/providers.php</code>
                (Laravel 11+):
                <pre><code>return [
    // Other providers...
    App\Agent\AgentServiceProvider::class,
];</code></pre>
            </li>
            <li>Add the facade alias to <code>config/app.php</code> (optional for Laravel 11+):
                <pre><code>'aliases' => [
    // Other aliases...
    'Agent' => App\Agent\Facades\Agent::class,
],</code></pre>
            </li>
        </ol>

        <h3>Optional Configuration</h3>
        <p>Publish the config file:</p>
        <pre><code>php artisan vendor:publish --tag=agent-config</code></pre>
    </section>

    <section id="features">
        <h2>Features</h2>

        <h3>Basic Detection</h3>
        <ul>
            <li>Device type detection (mobile, tablet, desktop, smartwatch, TV)</li>
            <li>Browser detection with extensive pattern matching</li>
            <li>Operating system/platform detection</li>
            <li>Bot/crawler detection with customizable patterns</li>
            <li>Device brand and model identification</li>
        </ul>

        <h3>Advanced Features</h3>
        <ul>
            <li>Version detection (browser and OS)</li>
            <li>Language preference detection</li>
            <li>Security checks (headless browsers, proxies, TOR, VPN)</li>
            <li>Performance optimized with caching</li>
            <li>JSON output for API responses</li>
            <li>Enhanced Blade directives for responsive design</li>
            <li>Windows 11 detection</li>
            <li>Detailed device brand & model detection</li>
        </ul>
    </section>

    <section id="usage">
        <h2>Usage Examples</h2>

        <h3>Basic Usage</h3>
        <div class="method">
            <p class="method-name">Device Type Checking</p>
            <pre><code>use Agent;

if (Agent::isMobile()) {
    // Mobile-specific logic
}

if (Agent::isTablet()) {
    // Tablet-specific logic
}

if (Agent::isDesktop()) {
    // Desktop-specific logic
}

// New device type methods
if (Agent::isSmartwatch()) {
    // Smartwatch-specific logic
}

if (Agent::isTV()) {
    // TV/Smart TV-specific logic
}</code></pre>
        </div>

        <div class="method">
            <p class="method-name">Browser Detection</p>
            <pre><code>$browser = Agent::browser(); // 'Chrome', 'Firefox', 'Edge', etc.
$browserVersion = Agent::browserVersion(); // '120.0.0'</code></pre>
        </div>

        <div class="method">
            <p class="method-name">Platform Detection</p>
            <pre><code>$platform = Agent::platform(); // 'Windows', 'Mac', 'iOS', etc.
$platformVersion = Agent::platformVersion(); // '10', '11', '10.15.7'</code></pre>
        </div>

        <h3>Blade Directives</h3>
        <pre><code>// Classic ifAgent directive
@ifAgent('Mobile')
    &lt;!-- Mobile-specific content --&gt;
@endifAgent

// New shorthand directives
@mobile
    &lt;!-- Mobile-specific content --&gt;
@endmobile

@tablet
    &lt;!-- Tablet-specific content --&gt;
@endtablet

@desktop
    &lt;!-- Desktop-specific content --&gt;
@enddesktop

@bot
    &lt;!-- Bot warning or message --&gt;
@endbot</code></pre>

        <h3>Enhanced Security Features</h3>
        <pre><code>// Bot detection
if (Agent::isBot()) {
    abort(403, 'Bots not allowed');
}

// Headless browser detection
if (Agent::isHeadlessChrome()) {
    // Potential automated traffic
}

// NEW: TOR network detection
if (Agent::isTor()) {
    // User is accessing via TOR network
}

// NEW: VPN detection (basic heuristics)
if (Agent::isVPN()) {
    // User may be using a VPN
}</code></pre>

        <h3>Device Brand and Model Detection</h3>
        <pre><code>// Get device brand
$brand = Agent::deviceBrand(); // 'Apple', 'Samsung', 'Google', etc.

// Get device model
$model = Agent::deviceModel(); // 'iPhone 13', 'Galaxy S21', etc.

// Brand-specific checks
if (Agent::isApple()) {
    // Apple device specific features
}

if (Agent::isSamsung()) {
    // Samsung device specific features
}</code></pre>

        <h3>Testing and Debugging</h3>
        <pre><code>// Override user agent for testing
$agent = app('agent')->setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)');

// Get detailed information as array
$details = Agent::toArray();

// Get JSON representation (with options)
$json = Agent::toJson(JSON_PRETTY_PRINT);</code></pre>
    </section>

    <section id="methods">
        <h2>Available Methods</h2>

        <h3>Device Type Detection</h3>
        <table>
            <tr>
                <th>Method</th>
                <th>Return Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>isMobile()</td>
                <td>bool</td>
                <td>Check if the device is a mobile phone</td>
            </tr>
            <tr>
                <td>isTablet()</td>
                <td>bool</td>
                <td>Check if the device is a tablet</td>
            </tr>
            <tr>
                <td>isDesktop()</td>
                <td>bool</td>
                <td>Check if the device is a desktop computer</td>
            </tr>
            <tr>
                <td>isSmartwatch()</td>
                <td>bool</td>
                <td>Check if the device is a smartwatch</td>
            </tr>
            <tr>
                <td>isTV()</td>
                <td>bool</td>
                <td>Check if the device is a smart TV</td>
            </tr>
            <tr>
                <td>deviceType()</td>
                <td>string|null</td>
                <td>Get the device type as a string</td>
            </tr>
        </table>

        <h3>Browser Detection</h3>
        <table>
            <tr>
                <th>Method</th>
                <th>Return Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>browser()</td>
                <td>string|null</td>
                <td>Get the browser name</td>
            </tr>
            <tr>
                <td>browserVersion()</td>
                <td>string|null</td>
                <td>Get the browser version</td>
            </tr>
            <tr>
                <td>isHeadlessChrome()</td>
                <td>bool</td>
                <td>Check if the browser is running in headless mode</td>
            </tr>
        </table>

        <h3>Platform Detection</h3>
        <table>
            <tr>
                <th>Method</th>
                <th>Return Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>platform()</td>
                <td>string|null</td>
                <td>Get the platform/OS name</td>
            </tr>
            <tr>
                <td>platformVersion()</td>
                <td>string|null</td>
                <td>Get the platform/OS version</td>
            </tr>
            <tr>
                <td>isWindows()</td>
                <td>bool</td>
                <td>Check if platform is Windows</td>
            </tr>
            <tr>
                <td>isMac()</td>
                <td>bool</td>
                <td>Check if platform is macOS</td>
            </tr>
            <tr>
                <td>isLinux()</td>
                <td>bool</td>
                <td>Check if platform is Linux</td>
            </tr>
            <tr>
                <td>isAndroid()</td>
                <td>bool</td>
                <td>Check if platform is Android</td>
            </tr>
            <tr>
                <td>isIOS()</td>
                <td>bool</td>
                <td>Check if platform is iOS</td>
            </tr>
        </table>

        <h3>Device Brand & Model</h3>
        <table>
            <tr>
                <th>Method</th>
                <th>Return Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>deviceBrand()</td>
                <td>string|null</td>
                <td>Get the device brand name</td>
            </tr>
            <tr>
                <td>deviceModel()</td>
                <td>string|null</td>
                <td>Get the device model name</td>
            </tr>
            <tr>
                <td>isApple()</td>
                <td>bool</td>
                <td>Check if device is made by Apple</td>
            </tr>
            <tr>
                <td>isSamsung()</td>
                <td>bool</td>
                <td>Check if device is made by Samsung</td>
            </tr>
            <tr>
                <td>isGoogle()</td>
                <td>bool</td>
                <td>Check if device is made by Google</td>
            </tr>
            <tr>
                <td>isHuawei()</td>
                <td>bool</td>
                <td>Check if device is made by Huawei</td>
            </tr>
            <tr>
                <td>isXiaomi()</td>
                <td>bool</td>
                <td>Check if device is made by Xiaomi</td>
            </tr>
            <tr>
                <td>isOnePlus()</td>
                <td>bool</td>
                <td>Check if device is made by OnePlus</td>
            </tr>
            <tr>
                <td>isSony()</td>
                <td>bool</td>
                <td>Check if device is made by Sony</td>
            </tr>
        </table>

        <h3>Security Features</h3>
        <table>
            <tr>
                <th>Method</th>
                <th>Return Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>isBot()</td>
                <td>bool</td>
                <td>Check if the user agent belongs to a bot/crawler</td>
            </tr>
            <tr>
                <td>isProxy()</td>
                <td>bool</td>
                <td>Check if request is coming through a proxy</td>
            </tr>
            <tr>
                <td>isTor()</td>
                <td>bool</td>
                <td>Check if request is coming from a TOR exit node</td>
            </tr>
            <tr>
                <td>isVPN()</td>
                <td>bool</td>
                <td>Basic detection of VPN connections</td>
            </tr>
        </table>

        <h3>Utility Methods</h3>
        <table>
            <tr>
                <th>Method</th>
                <th>Return Type</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>languages()</td>
                <td>array</td>
                <td>Get the preferred languages from Accept-Language header</td>
            </tr>
            <tr>
                <td>toArray()</td>
                <td>array</td>
                <td>Get all detection results as an array</td>
            </tr>
            <tr>
                <td>toJson($options = 0)</td>
                <td>string</td>
                <td>Get all detection results as JSON</td>
            </tr>
            <tr>
                <td>analyze()</td>
                <td>Agent</td>
                <td>Pre-compute all detections</td>
            </tr>
            <tr>
                <td>setUserAgent($userAgent)</td>
                <td>Agent</td>
                <td>Set a custom user agent string (for testing)</td>
            </tr>
        </table>
    </section>

    <section id="configuration">
        <h2>Configuration Options</h2>

        <p>The <code>config/agent.php</code> file allows you to customize various aspects of the Agent class:</p>

        <h3>Caching</h3>
        <pre><code>'cache' => env('AGENT_CACHE', true),</code></pre>
        <p>Enable or disable caching of detection results. Caching is recommended for production environments.</p>

        <h3>Browser Detection Patterns</h3>
        <pre><code>'browsers' => [
    'Brave'     => 'Brave',
    'Chrome'    => 'Chrome|CriOS',
    'Edge'      => 'Edg|Edge|EdgA|EdgIOS',
    // ...
],</code></pre>
        <p>Add or modify browser detection patterns.</p>

        <h3>Platform Detection Patterns</h3>
        <pre><code>'platforms' => [
    'Android'     => 'Android',
    'iOS'         => 'iPhone|iPad|iPod',
    'Windows'     => 'Windows NT|WinNT|Win32',
    // ...
],</code></pre>
        <p>Add or modify platform detection patterns.</p>

        <h3>Device Brand Patterns</h3>
        <pre><code>'device_brands' => [
    'Apple'    => 'iPhone|iPad|iPod|Macintosh',
    'Google'   => 'Pixel|Nexus',
    'Samsung'  => 'SM-|SAMSUNG|Galaxy',
    // ...
],</code></pre>
        <p>Add or modify device brand detection patterns.</p>

        <h3>Bot Identifiers</h3>
        <pre><code>'bots' => [
    'Googlebot',
    'Bingbot',
    'Yandexbot',
    // ...
],</code></pre>
        <p>Add or modify bot detection patterns.</p>
    </section>

    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid var(--border); color: var(--light-text);">
        <p>© 2024 Laravel Agent - Last Updated:
            <?php echo date('F Y'); ?>
        </p>
    </footer>
</body>

</html>