<?php
// app/Models/Rbac/SystemRole.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Entity\Entity;
use App\Models\User;
use App\Traits\HasAuditFields;

/**
 * Class SystemRole
 *
 * Represents system roles with hierarchical structure.
 * Roles define what users can do in the system.
 *
 * @property int $id
 * @property int|null $entity_id Associated entity identifier
 * @property string $role_name Human-readable role name
 * @property string|null $description Detailed role description
 * @property int|null $parent_role_id Hierarchical parent role
 * @property int $hierarchy_level Level in role hierarchy
 * @property string $guard_type Type of access guard
 * @property \Carbon\Carbon|null $active_from Date when role becomes active
 * @property \Carbon\Carbon|null $active_until Date when role expires
 * @property bool $is_active Soft delete flag
 * @property bool $is_approval_required Flag indicating if approval is needed
 * @property string $approval_status Current approval status
 * @property string|null $notes Administrative notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class SystemRole extends Model
{
    use HasAuditFields;

    protected $fillable = [
        'entity_id',
        'role_name',
        'description',
        'parent_role_id',
        'hierarchy_level',
        'guard_type',
        'active_from',
        'active_until',
        'is_active',
        'is_approval_required',
        'approval_status',
        'notes',
    ];

    protected $casts = [
        'active_from' => 'date',
        'active_until' => 'date',
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
        'hierarchy_level' => 'integer',
    ];

    protected $appends = ['is_hierarchical', 'is_expired'];

    /**
     * Check if role has parent/child hierarchy.
     *
     * @return bool
     */
    public function getIsHierarchicalAttribute(): bool
    {
        return $this->parent_role_id !== null || $this->children()->exists();
    }

    /**
     * Check if role is expired.
     *
     * @return bool
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->active_until && $this->active_until->isPast();
    }

    /**
     * Check if role is currently active and not expired.
     *
     * @return bool
     */
    public function isOperational(): bool
    {
        if (!$this->is_active || $this->approval_status !== 'approved') {
            return false;
        }

        if ($this->active_from && $this->active_from->isFuture()) {
            return false;
        }

        if ($this->active_until && $this->active_until->isPast()) {
            return false;
        }

        return true;
    }

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function parentRole(): BelongsTo
    {
        return $this->belongsTo(SystemRole::class, 'parent_role_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(SystemRole::class, 'parent_role_id');
    }

    public function userAssignments(): HasMany
    {
        return $this->hasMany(UserRoleAssignment::class, 'role_id');
    }

    public function permissionGrants(): HasMany
    {
        return $this->hasMany(PermissionGrant::class, 'role_id');
    }

    // ===== QUERY SCOPES =====

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOperational($query)
    {
        return $query->active()
            ->where('approval_status', 'approved')
            ->where(function ($q) {
                $q->whereNull('active_from')
                    ->orWhere('active_from', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('active_until')
                    ->orWhere('active_until', '>', now());
            });
    }

    public function scopeForEntity($query, string $entityId)
    {
        return $query->where('entity_id', $entityId);
    }

    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_role_id');
    }
}
