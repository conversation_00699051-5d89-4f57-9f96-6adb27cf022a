<?php
// app/Models/Information/Address.php
namespace App\Models\Information;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use App\Models\Entity\Entity;
use App\Models\User;

/**
 * Class Address
 *
 * Represents a physical address that can be associated with both entities and users.
 * Uses polymorphic many-to-many relationships via the addressables pivot table.
 *
 * Addresses support full geographic information including GPS coordinates for mapping
 * and distance calculations. The system allows multiple addresses per entity/user with
 * primary designation support. Advanced features include address validation, formatting,
 * and geographic search capabilities.
 *
 * Key Features:
 * - Polymorphic many-to-many relationships with entities and users
 * - GPS coordinates with distance calculations
 * - Address formatting and validation
 * - Geographic search and radius queries
 * - Primary address designation
 * - Comprehensive audit trail
 * - Address standardization
 *
 * @property int $id
 * @property string $address_line1 Street address or main address line
 * @property string|null $address_line2 Additional address info (apartment, suite, etc.)
 * @property string $city City name
 * @property string $state State or province
 * @property string $postal_code ZIP or postal code
 * @property bool $is_primary Whether this is the primary address
 * @property float|null $latitude GPS latitude coordinate (decimal degrees)
 * @property float|null $longitude GPS longitude coordinate (decimal degrees)
 * @property bool $is_active Status: true=active, false=inactive
 * @property int $created_by User ID who created this record
 * @property int|null $updated_by User ID who last updated this record
 * @property int|null $deleted_by User ID who soft deleted this record
 * @property int|null $restored_by User ID who restored this record
 * @property \Carbon\Carbon|null $restored_at When the record was restored
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 *
 * // Computed Attributes
 * @property string $full_address Complete formatted address
 * @property bool $has_coordinates Whether GPS coordinates are available
 * @property string $display_name Short address for display
 * @property string $map_link Google Maps link
 * @property array $coordinate_array Lat/lng as array
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder inCity(string $city)
 * @method static \Illuminate\Database\Eloquent\Builder inState(string $state)
 * @method static \Illuminate\Database\Eloquent\Builder withCoordinates()
 * @method static \Illuminate\Database\Eloquent\Builder withinRadius(float $lat, float $lng, float $radius)
 * @method static \Illuminate\Database\Eloquent\Builder search(string $search)
 */
class Address extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'addresses';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'address_line1',
        'address_line2',
        'city',
        'state',
        'postal_code',
        'is_primary',
        'latitude',
        'longitude',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'is_active' => 'boolean',
        'is_primary' => 'boolean',
        'restored_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array<string>
     */
    protected $hidden = [
        'deleted_by',
        'restored_by',
        'restored_at'
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'full_address',
        'has_coordinates',
        'display_name',
        'coordinate_array'
    ];

    /**
     * Indian state codes for validation.
     *
     * @var array<string, string>
     */
    const INDIAN_STATES = [
        'AN' => 'Andaman and Nicobar Islands',
        'AP' => 'Andhra Pradesh',
        'AR' => 'Arunachal Pradesh',
        'AS' => 'Assam',
        'BR' => 'Bihar',
        'CG' => 'Chhattisgarh',
        'CH' => 'Chandigarh',
        'DH' => 'Dadra and Nagar Haveli',
        'DD' => 'Daman and Diu',
        'DL' => 'Delhi',
        'GA' => 'Goa',
        'GJ' => 'Gujarat',
        'HR' => 'Haryana',
        'HP' => 'Himachal Pradesh',
        'JK' => 'Jammu and Kashmir',
        'JH' => 'Jharkhand',
        'KA' => 'Karnataka',
        'KL' => 'Kerala',
        'LA' => 'Ladakh',
        'LD' => 'Lakshadweep',
        'MP' => 'Madhya Pradesh',
        'MH' => 'Maharashtra',
        'MN' => 'Manipur',
        'ML' => 'Meghalaya',
        'MZ' => 'Mizoram',
        'NL' => 'Nagaland',
        'OR' => 'Odisha',
        'PY' => 'Puducherry',
        'PB' => 'Punjab',
        'RJ' => 'Rajasthan',
        'SK' => 'Sikkim',
        'TN' => 'Tamil Nadu',
        'TS' => 'Telangana',
        'TR' => 'Tripura',
        'UP' => 'Uttar Pradesh',
        'UK' => 'Uttarakhand',
        'WB' => 'West Bengal'
    ];

    // ===== ACCESSORS =====

    /**
     * Get the full address as a formatted string.
     *
     * @return string
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address_line1,
            $this->address_line2,
            $this->city,
            $this->state,
            $this->postal_code
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get short display name for the address.
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->address_line2) {
            return "{$this->address_line1}, {$this->address_line2}";
        }
        
        return "{$this->address_line1}, {$this->city}";
    }

    /**
     * Check if address has GPS coordinates.
     *
     * @return bool
     */
    public function getHasCoordinatesAttribute(): bool
    {
        return $this->hasCoordinates();
    }

    /**
     * Get coordinates as array for JavaScript/mapping.
     *
     * @return array|null
     */
    public function getCoordinateArrayAttribute(): ?array
    {
        if (!$this->has_coordinates) {
            return null;
        }

        return [
            'lat' => (float) $this->latitude,
            'lng' => (float) $this->longitude
        ];
    }

    /**
     * Get Google Maps link for the address.
     *
     * @return string|null
     */
    public function getMapLinkAttribute(): ?string
    {
        if ($this->has_coordinates) {
            return "https://maps.google.com/?q={$this->latitude},{$this->longitude}";
        }

        $address = urlencode($this->full_address);
        return "https://maps.google.com/?q={$address}";
    }

    /**
     * Get formatted postal code.
     *
     * @return string
     */
    public function getFormattedPostalCodeAttribute(): string
    {
        // Format Indian postal codes (6 digits with space after 3rd digit)
        if (preg_match('/^\d{6}$/', $this->postal_code)) {
            return substr($this->postal_code, 0, 3) . ' ' . substr($this->postal_code, 3);
        }

        return $this->postal_code;
    }

    // ===== HELPER METHODS =====

    /**
     * Check if address has GPS coordinates.
     *
     * @return bool
     */
    public function hasCoordinates(): bool
    {
        return $this->latitude !== null && $this->longitude !== null;
    }

    /**
     * Calculate distance to another address in kilometers.
     * Uses Haversine formula for great-circle distance.
     *
     * @param Address $other
     * @return float|null Distance in kilometers, or null if coordinates missing
     */
    public function distanceTo(Address $other): ?float
    {
        if (!$this->hasCoordinates() || !$other->hasCoordinates()) {
            return null;
        }

        $earthRadius = 6371; // Earth's radius in kilometers

        $latFrom = deg2rad($this->latitude);
        $lonFrom = deg2rad($this->longitude);
        $latTo = deg2rad($other->latitude);
        $lonTo = deg2rad($other->longitude);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $angle = 2 * asin(sqrt(
            pow(sin($latDelta / 2), 2) +
                cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)
        ));

        return round($angle * $earthRadius, 2);
    }

    /**
     * Calculate distance to coordinates in kilometers.
     *
     * @param float $latitude
     * @param float $longitude
     * @return float|null
     */
    public function distanceToCoordinates(float $latitude, float $longitude): ?float
    {
        if (!$this->hasCoordinates()) {
            return null;
        }

        $earthRadius = 6371;

        $latFrom = deg2rad($this->latitude);
        $lonFrom = deg2rad($this->longitude);
        $latTo = deg2rad($latitude);
        $lonTo = deg2rad($longitude);

        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;

        $angle = 2 * asin(sqrt(
            pow(sin($latDelta / 2), 2) +
                cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)
        ));

        return round($angle * $earthRadius, 2);
    }

    /**
     * Check if address is in a specific country (basic check by postal code pattern).
     *
     * @param string $country
     * @return bool
     */
    public function isInCountry(string $country): bool
    {
        return match (strtoupper($country)) {
            'INDIA', 'IN' => preg_match('/^\d{6}$/', $this->postal_code),
            'USA', 'US' => preg_match('/^\d{5}(-\d{4})?$/', $this->postal_code),
            'UK', 'GB' => preg_match('/^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$/', $this->postal_code),
            default => true // Unknown country, assume valid
        };
    }

    /**
     * Validate Indian postal code format.
     *
     * @return bool
     */
    public function hasValidIndianPostalCode(): bool
    {
        return preg_match('/^\d{6}$/', $this->postal_code);
    }

    /**
     * Get address completeness score (0-100).
     *
     * @return int
     */
    public function getCompletenessScore(): int
    {
        $score = 0;
        $totalFields = 7;

        if (!empty($this->address_line1)) $score++;
        if (!empty($this->city)) $score++;
        if (!empty($this->state)) $score++;
        if (!empty($this->postal_code)) $score++;
        if ($this->hasCoordinates()) $score += 2; // GPS worth double
        if (!empty($this->address_line2)) $score++; // Bonus for line 2

        return round(($score / $totalFields) * 100);
    }

    /**
     * Standardize address format.
     *
     * @return self
     */
    public function standardize(): self
    {
        // Capitalize city and state
        $this->city = ucwords(strtolower($this->city));
        $this->state = ucwords(strtolower($this->state));
        
        // Clean postal code
        $this->postal_code = preg_replace('/[^A-Za-z0-9]/', '', $this->postal_code);
        
        // Trim all text fields
        $this->address_line1 = trim($this->address_line1);
        $this->address_line2 = trim($this->address_line2);

        return $this;
    }

    // ===== POLYMORPHIC RELATIONSHIPS =====

    /**
     * Get all entities that have this address.
     *
     * @return MorphToMany
     */
    public function entities(): MorphToMany
    {
        return $this->morphedByMany(Entity::class, 'addressable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all users that have this address.
     *
     * @return MorphToMany
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'addressable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all models (entities and users) that have this address.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllAddressables()
    {
        return collect()
            ->merge($this->entities)
            ->merge($this->users);
    }

    /**
     * Check if this address is primary for any model.
     *
     * @return bool
     */
    public function isPrimaryForAny(): bool
    {
        $entityPrimary = $this->entities()->wherePivot('is_primary', true)->exists();
        $userPrimary = $this->users()->wherePivot('is_primary', true)->exists();

        return $entityPrimary || $userPrimary;
    }

    /**
     * Get count of entities using this address.
     *
     * @return int
     */
    public function getEntityCount(): int
    {
        return $this->entities()->count();
    }

    /**
     * Get count of users using this address.
     *
     * @return int
     */
    public function getUserCount(): int
    {
        return $this->users()->count();
    }

    /**
     * Get total usage count.
     *
     * @return int
     */
    public function getTotalUsageCount(): int
    {
        return $this->getEntityCount() + $this->getUserCount();
    }

    // ===== AUDIT RELATIONSHIPS =====

    /**
     * Get the user who created this address.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this address.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who soft deleted this address.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored this address.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to only active addresses.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to addresses in a specific city.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $city
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInCity($query, string $city)
    {
        return $query->where('city', 'like', "%{$city}%");
    }

    /**
     * Scope to addresses in a specific state.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $state
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInState($query, string $state)
    {
        return $query->where('state', 'like', "%{$state}%");
    }

    /**
     * Scope to addresses with specific postal code.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $postalCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithPostalCode($query, string $postalCode)
    {
        return $query->where('postal_code', $postalCode);
    }

    /**
     * Scope to addresses with GPS coordinates.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithCoordinates($query)
    {
        return $query->whereNotNull('latitude')
            ->whereNotNull('longitude');
    }

    /**
     * Scope to addresses without GPS coordinates.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithoutCoordinates($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('latitude')->orWhereNull('longitude');
        });
    }

    /**
     * Scope to addresses within a radius of given coordinates.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param float $latitude
     * @param float $longitude
     * @param float $radius In kilometers
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithinRadius($query, float $latitude, float $longitude, float $radius)
    {
        $haversine = "(6371 * acos(cos(radians($latitude))
            * cos(radians(latitude))
            * cos(radians(longitude) - radians($longitude))
            + sin(radians($latitude))
            * sin(radians(latitude))))";

        return $query->select('*')
            ->selectRaw("$haversine AS distance")
            ->whereRaw("$haversine < ?", [$radius])
            ->orderBy('distance');
    }

    /**
     * Scope to search addresses by any field.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('address_line1', 'like', "%{$search}%")
                ->orWhere('address_line2', 'like', "%{$search}%")
                ->orWhere('city', 'like', "%{$search}%")
                ->orWhere('state', 'like', "%{$search}%")
                ->orWhere('postal_code', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to addresses with high completeness score.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $minScore Minimum completeness score (0-100)
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeComplete($query, int $minScore = 80)
    {
        return $query->whereNotNull('address_line1')
            ->whereNotNull('city')
            ->whereNotNull('state')
            ->whereNotNull('postal_code');
    }

    /**
     * Scope to Indian addresses.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIndian($query)
    {
        return $query->whereRaw('postal_code REGEXP "^[0-9]{6}$"');
    }

    // ===== STATIC METHODS =====

    /**
     * Find address by exact match.
     *
     * @param string $addressLine1
     * @param string $city
     * @param string $state
     * @param string|null $postalCode
     * @return self|null
     */
    public static function findExactMatch(
        string $addressLine1,
        string $city,
        string $state,
        ?string $postalCode = null
    ): ?self {
        $query = self::where('address_line1', $addressLine1)
            ->where('city', $city)
            ->where('state', $state);

        if ($postalCode) {
            $query->where('postal_code', $postalCode);
        }

        return $query->first();
    }

    /**
     * Find similar addresses.
     *
     * @param string $addressLine1
     * @param string $city
     * @param string $state
     * @param float $threshold Similarity threshold (0-1)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findSimilar(
        string $addressLine1,
        string $city,
        string $state,
        float $threshold = 0.8
    ) {
        return self::where('city', 'like', "%{$city}%")
            ->where('state', 'like', "%{$state}%")
            ->get()
            ->filter(function ($address) use ($addressLine1, $threshold) {
                $similarity = similar_text(
                    strtolower($address->address_line1),
                    strtolower($addressLine1),
                    $percent
                );
                return ($percent / 100) >= $threshold;
            });
    }

    /**
     * Get addresses within radius of coordinates.
     *
     * @param float $latitude
     * @param float $longitude
     * @param float $radius
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getWithinRadius(
        float $latitude,
        float $longitude,
        float $radius,
        int $limit = 100
    ) {
        return self::withCoordinates()
            ->withinRadius($latitude, $longitude, $radius)
            ->limit($limit)
            ->get();
    }

    /**
     * Get popular cities from addresses.
     *
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public static function getPopularCities(int $limit = 20)
    {
        return Cache::remember('popular_cities', 3600, function () use ($limit) {
            return self::select('city')
                ->selectRaw('COUNT(*) as address_count')
                ->active()
                ->groupBy('city')
                ->orderByDesc('address_count')
                ->limit($limit)
                ->pluck('address_count', 'city');
        });
    }

    /**
     * Get popular states from addresses.
     *
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public static function getPopularStates(int $limit = 10)
    {
        return Cache::remember('popular_states', 3600, function () use ($limit) {
            return self::select('state')
                ->selectRaw('COUNT(*) as address_count')
                ->active()
                ->groupBy('state')
                ->orderByDesc('address_count')
                ->limit($limit)
                ->pluck('address_count', 'state');
        });
    }

    /**
     * Validate postal code format for different countries.
     *
     * @param string $postalCode
     * @param string $country
     * @return bool
     */
    public static function isValidPostalCode(string $postalCode, string $country = 'IN'): bool
    {
        return match (strtoupper($country)) {
            'IN', 'INDIA' => preg_match('/^\d{6}$/', $postalCode),
            'US', 'USA' => preg_match('/^\d{5}(-\d{4})?$/', $postalCode),
            'UK', 'GB' => preg_match('/^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][A-Z]{2}$/', $postalCode),
            'CA', 'CANADA' => preg_match('/^[A-Z]\d[A-Z] \d[A-Z]\d$/', $postalCode),
            default => !empty($postalCode)
        };
    }

    /**
     * Get Indian states for dropdown.
     *
     * @return array
     */
    public static function getIndianStates(): array
    {
        return self::INDIAN_STATES;
    }

    /**
     * Geocode address using a service (placeholder for actual implementation).
     *
     * @param string $address
     * @return array|null Returns ['lat' => float, 'lng' => float] or null
     */
    public static function geocodeAddress(string $address): ?array
    {
        // Placeholder for geocoding service integration
        // You would integrate with Google Maps API, OpenStreetMap, etc.
        
        // Example implementation would be:
        // $response = Http::get('geocoding-service-url', ['address' => $address]);
        // Parse response and return coordinates
        
        return null;
    }

    // ===== MODEL EVENTS =====

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        // Auto-standardize address on creating/updating
        static::creating(function ($address) {
            $address->standardize();
        });

        static::updating(function ($address) {
            $address->standardize();
        });

        // Clear cache when addresses change
        static::saved(function ($address) {
            Cache::forget('popular_cities');
            Cache::forget('popular_states');
        });

        static::deleted(function ($address) {
            Cache::forget('popular_cities');
            Cache::forget('popular_states');
        });
    }
}