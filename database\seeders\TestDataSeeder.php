<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Entity\Entity;
use App\Models\User;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\UserRoleAssignment;
use App\Models\Information\Address;
use App\Models\Information\Contact;
use App\Models\Entity\EntityRelationship;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds to create test data.
     */
    public function run(): void
    {
        // Create first distributor entity
        $distributor1 = Entity::create([
            'entity_id' => 'DIST001',
            'entity_name' => 'Sample Distributor 1',
            'entity_type' => 'distributor',
            'is_goods_provider' => true,
            'is_service_provider' => false,
            'is_active' => true,
            'created_by' => 1, // System user ID
        ]);

        // Create a second distributor entity
        $distributor2 = Entity::create([
            'entity_id' => 'DIST002',
            'entity_name' => 'Sample Distributor 2',
            'entity_type' => 'distributor',
            'is_goods_provider' => true,
            'is_service_provider' => true,
            'is_active' => true,
            'created_by' => 1, // System user ID
        ]);

        // Create address for distributor 1
        $address1 = Address::create([
            'address_line1' => '123 Main St',
            'address_line2' => 'Suite 100',
            'city' => 'New York',
            'state' => 'NY',
            'postal_code' => '10001',
            'is_active' => true,
            'created_by' => 1,
        ]);

        // Link address to distributor 1
        DB::table('addressables')->insert([
            'address_id' => $address1->id,
            'addressable_type' => 'App\\Models\\Entity\\Entity',
            'addressable_id' => $distributor1->id,
            'is_primary' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create address for distributor 2
        $address2 = Address::create([
            'address_line1' => '456 Market St',
            'address_line2' => 'Floor 2',
            'city' => 'San Francisco',
            'state' => 'CA',
            'postal_code' => '94105',
            'is_active' => true,
            'created_by' => 1,
        ]);

        // Link address to distributor 2
        DB::table('addressables')->insert([
            'address_id' => $address2->id,
            'addressable_type' => 'App\\Models\\Entity\\Entity',
            'addressable_id' => $distributor2->id,
            'is_primary' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create Administrator Manager user for distributor 1
        $adminManager1 = User::create([
            'name' => 'Admin Manager',
            'email' => '<EMAIL>',
            'user_id' => 'USRAM001',
            'phone' => '************',
            'entity_id' => $distributor1->id,
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'is_active' => true,
            'created_by' => 1,
        ]);

        // Create Administrator user for distributor 1
        $admin1 = User::create([
            'name' => 'Regular Admin',
            'email' => '<EMAIL>',
            'user_id' => 'USRA001',
            'phone' => '************',
            'entity_id' => $distributor1->id,
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'is_active' => true,
            'created_by' => 1,
        ]);

        // Create Administrator user for distributor 2
        $admin2 = User::create([
            'name' => 'Second Admin',
            'email' => '<EMAIL>',
            'user_id' => 'USRA002',
            'phone' => '************',
            'entity_id' => $distributor2->id,
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
            'is_active' => true,
            'created_by' => 1,
        ]);

        // Get or create the necessary roles
        $adminManagerRole = SystemRole::firstOrCreate(
            ['role_name' => 'Administrator Manager'],
            [
                'entity_id' => null, // System-wide role
                'description' => 'Top-level entity role with full access within its own entity',
                'hierarchy_level' => 2,
                'is_active' => true,
            ]
        );

        $adminRole = SystemRole::firstOrCreate(
            ['role_name' => 'Administrator'],
            [
                'entity_id' => null, // System-wide role
                'description' => 'Entity admin with access to all operational modules',
                'hierarchy_level' => 3,
                'is_active' => true,
            ]
        );

        // Assign roles to users
        DB::table('user_role_assignments')->insert([
            'entity_id' => $distributor1->id,
            'user_id' => $adminManager1->id,
            'role_id' => $adminManagerRole->id,
            'is_active' => true,
            'is_approval_required' => false,
            'approval_status' => 'approved',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::table('user_role_assignments')->insert([
            'entity_id' => $distributor1->id,
            'user_id' => $admin1->id,
            'role_id' => $adminRole->id,
            'is_active' => true,
            'is_approval_required' => false,
            'approval_status' => 'approved',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::table('user_role_assignments')->insert([
            'entity_id' => $distributor2->id,
            'user_id' => $admin2->id,
            'role_id' => $adminRole->id,
            'is_active' => true,
            'is_approval_required' => false,
            'approval_status' => 'approved',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create entity relationship between distributors
        DB::table('entity_relationships')->insert([
            'source_entity_id' => $distributor1->id,
            'target_entity_id' => $distributor2->id,
            'relationship_type' => 'linked_to',
            'description' => 'Partner distributors',
            'is_active' => true,
            'is_approval_required' => false,
            'approval_status' => 'approved',
            'created_by' => 1, // Required field
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create contact for distributor 1
        $contact1 = Contact::create([
            'name' => 'Distributor 1 Contact',
            'email' => '<EMAIL>',
            'phone' => '************',
            'is_active' => true,
            'created_by' => 1,
        ]);

        // Link contact to distributor 1
        DB::table('contactables')->insert([
            'contact_id' => $contact1->id,
            'contactable_type' => 'App\\Models\\Entity\\Entity',
            'contactable_id' => $distributor1->id,
            'is_primary' => true,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $this->command->info('Test distributor entities and users created successfully!');
    }
} 