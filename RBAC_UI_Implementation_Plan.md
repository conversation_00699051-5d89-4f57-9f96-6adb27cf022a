# RBAC Administrative UI Implementation Plan

**Project:** SAIMS Role-Based Access Control UI  
**Framework:** Laravel 12 + Livewire 3 + Flux UI  
**Analysis Date:** 2025-07-24  
**Created By:** Augment Agent

---

## Executive Summary

This plan outlines the complete implementation of missing RBAC Administrative UI interfaces using existing Laravel 12 resources, Livewire 3 components, and Flux UI elements. **No additional packages or custom CSS/JavaScript required.**

### Implementation Approach:
- ✅ **Use existing Flux UI components** for consistent design
- ✅ **Follow established Livewire patterns** from Settings components
- ✅ **Leverage existing layout structures** and navigation patterns
- ✅ **Utilize current middleware and validation** systems
- ✅ **Maintain Laravel 12 architecture** compliance

---

## 1. File and Folder Structure

### 1.1 New Directory Structure
```
app/Livewire/Admin/
├── Rbac/
│   ├── Roles/
│   │   ├── RoleIndex.php
│   │   ├── RoleCreate.php
│   │   ├── RoleEdit.php
│   │   └── RoleDelete.php
│   ├── Users/
│   │   ├── UserRoleIndex.php
│   │   ├── UserRoleAssign.php
│   │   └── UserRoleHistory.php
│   ├── Permissions/
│   │   ├── PermissionIndex.php
│   │   ├── PermissionCreate.php
│   │   ├── PermissionEdit.php
│   │   └── PermissionGrant.php
│   ├── Departments/
│   │   ├── DepartmentIndex.php
│   │   ├── DepartmentCreate.php
│   │   └── DepartmentEdit.php
│   └── Audit/
│       ├── AccessAudit.php
│       ├── RoleUsage.php
│       └── PermissionAudit.php

resources/views/livewire/admin/
├── rbac/
│   ├── roles/
│   │   ├── role-index.blade.php
│   │   ├── role-create.blade.php
│   │   ├── role-edit.blade.php
│   │   └── role-delete.blade.php
│   ├── users/
│   │   ├── user-role-index.blade.php
│   │   ├── user-role-assign.blade.php
│   │   └── user-role-history.blade.php
│   ├── permissions/
│   │   ├── permission-index.blade.php
│   │   ├── permission-create.blade.php
│   │   ├── permission-edit.blade.php
│   │   └── permission-grant.blade.php
│   ├── departments/
│   │   ├── department-index.blade.php
│   │   ├── department-create.blade.php
│   │   └── department-edit.blade.php
│   └── audit/
│       ├── access-audit.blade.php
│       ├── role-usage.blade.php
│       └── permission-audit.blade.php

resources/views/components/admin/
├── layout.blade.php
├── navigation.blade.php
├── rbac-card.blade.php
├── role-badge.blade.php
├── permission-badge.blade.php
└── hierarchy-indicator.blade.php
```

### 1.2 Route Structure
```php
// routes/web.php - Add to existing protected routes
Route::middleware(['auth', 'active', 'check.sessions', 'check.roles'])->group(function () {
    // Admin routes with hierarchy level check
    Route::prefix('admin')->name('admin.')->middleware('min.hierarchy:2')->group(function () {
        Route::prefix('rbac')->name('rbac.')->group(function () {
            // Role Management
            Route::prefix('roles')->name('roles.')->group(function () {
                Route::get('/', RoleIndex::class)->name('index');
                Route::get('/create', RoleCreate::class)->name('create');
                Route::get('/{role}/edit', RoleEdit::class)->name('edit');
            });
            
            // User Role Management
            Route::prefix('users')->name('users.')->group(function () {
                Route::get('/', UserRoleIndex::class)->name('index');
                Route::get('/{user}/assign', UserRoleAssign::class)->name('assign');
                Route::get('/{user}/history', UserRoleHistory::class)->name('history');
            });
            
            // Permission Management
            Route::prefix('permissions')->name('permissions.')->group(function () {
                Route::get('/', PermissionIndex::class)->name('index');
                Route::get('/create', PermissionCreate::class)->name('create');
                Route::get('/{permission}/edit', PermissionEdit::class)->name('edit');
                Route::get('/{role}/grant', PermissionGrant::class)->name('grant');
            });
            
            // Department Management
            Route::prefix('departments')->name('departments.')->group(function () {
                Route::get('/', DepartmentIndex::class)->name('index');
                Route::get('/create', DepartmentCreate::class)->name('create');
                Route::get('/{department}/edit', DepartmentEdit::class)->name('edit');
            });
            
            // Audit & Reports
            Route::prefix('audit')->name('audit.')->group(function () {
                Route::get('/access', AccessAudit::class)->name('access');
                Route::get('/roles', RoleUsage::class)->name('roles');
                Route::get('/permissions', PermissionAudit::class)->name('permissions');
            });
        });
    });
});
```

---

## 2. Component Architecture Design

### 2.1 Base Admin Layout Component
**File:** `resources/views/components/admin/layout.blade.php`

```php
@props(['heading', 'subheading', 'breadcrumbs' => []])

<div class="h-full w-full p-6">
    <!-- Breadcrumb Navigation -->
    <x-breadcrumb :items="$breadcrumbs" />

    <!-- Admin Grid Layout -->
    <div class="grid gap-6 lg:grid-cols-5">
        <!-- Left Navigation (Wider for admin) -->
        <div class="lg:col-span-1">
            <x-admin.navigation />
        </div>

        <!-- Right Content Area -->
        <div class="lg:col-span-4">
            <flux:card>
                <flux:card.header>
                    <flux:heading size="lg">{{ $heading }}</flux:heading>
                    <flux:subheading>{{ $subheading }}</flux:subheading>
                </flux:card.header>

                <flux:card.body>
                    {{ $slot }}
                </flux:card.body>
            </flux:card>
        </div>
    </div>
</div>
```

### 2.2 Admin Navigation Component
**File:** `resources/views/components/admin/navigation.blade.php`

```php
<nav class="space-y-1" aria-label="{{ __('Admin Navigation') }}">
    <!-- Role Management Section -->
    <div class="pb-2">
        <flux:text class="px-3 text-xs font-semibold text-zinc-500 uppercase tracking-wider">
            {{ __('Role Management') }}
        </flux:text>
    </div>
    
    <a href="{{ route('admin.rbac.roles.index') }}" 
       class="nav-link {{ request()->routeIs('admin.rbac.roles.*') ? 'active' : '' }}">
        <flux:icon name="shield-check" class="h-4 w-4" />
        {{ __('Roles') }}
    </a>
    
    <a href="{{ route('admin.rbac.users.index') }}" 
       class="nav-link {{ request()->routeIs('admin.rbac.users.*') ? 'active' : '' }}">
        <flux:icon name="users" class="h-4 w-4" />
        {{ __('User Assignments') }}
    </a>
    
    <!-- Permission Management Section -->
    <div class="pt-4 pb-2">
        <flux:text class="px-3 text-xs font-semibold text-zinc-500 uppercase tracking-wider">
            {{ __('Permissions') }}
        </flux:text>
    </div>
    
    <a href="{{ route('admin.rbac.permissions.index') }}" 
       class="nav-link {{ request()->routeIs('admin.rbac.permissions.*') ? 'active' : '' }}">
        <flux:icon name="key" class="h-4 w-4" />
        {{ __('Permissions') }}
    </a>
    
    <a href="{{ route('admin.rbac.departments.index') }}" 
       class="nav-link {{ request()->routeIs('admin.rbac.departments.*') ? 'active' : '' }}">
        <flux:icon name="building-office" class="h-4 w-4" />
        {{ __('Departments') }}
    </a>
    
    <!-- Audit Section -->
    <div class="pt-4 pb-2">
        <flux:text class="px-3 text-xs font-semibold text-zinc-500 uppercase tracking-wider">
            {{ __('Audit & Reports') }}
        </flux:text>
    </div>
    
    <a href="{{ route('admin.rbac.audit.access') }}" 
       class="nav-link {{ request()->routeIs('admin.rbac.audit.*') ? 'active' : '' }}">
        <flux:icon name="document-magnifying-glass" class="h-4 w-4" />
        {{ __('Access Audit') }}
    </a>
</nav>

<style>
.nav-link {
    @apply flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors text-zinc-600 hover:bg-zinc-50 hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-100;
}
.nav-link.active {
    @apply bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-zinc-100;
}
</style>
```

---

## 3. Priority Implementation Phases

### Phase 1: Core Role Management (Week 1-2)
**Priority:** HIGH - Essential for basic RBAC administration

#### 3.1 Role Index Component
**File:** `app/Livewire/Admin/Rbac/Roles/RoleIndex.php`

```php
<?php

namespace App\Livewire\Admin\Rbac\Roles;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Rbac\SystemRole;
use Illuminate\Support\Facades\Auth;

class RoleIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $filterHierarchy = '';
    public $filterStatus = '';
    
    protected $queryString = [
        'search' => ['except' => ''],
        'filterHierarchy' => ['except' => ''],
        'filterStatus' => ['except' => '']
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function getRolesProperty()
    {
        $query = SystemRole::with(['entity', 'parentRole', 'userAssignments'])
            ->where('entity_id', Auth::user()->entity_id);

        if ($this->search) {
            $query->where(function($q) {
                $q->where('role_name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->filterHierarchy) {
            $query->where('hierarchy_level', $this->filterHierarchy);
        }

        if ($this->filterStatus) {
            if ($this->filterStatus === 'active') {
                $query->operational();
            } else {
                $query->where('is_active', false);
            }
        }

        return $query->orderBy('hierarchy_level')
                    ->orderBy('role_name')
                    ->paginate(15);
    }

    public function render()
    {
        return view('livewire.admin.rbac.roles.role-index', [
            'roles' => $this->roles
        ])->layout('components.admin.layout', [
            'heading' => __('Role Management'),
            'subheading' => __('Manage system roles and permissions'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Roles'), 'url' => route('admin.rbac.roles.index')]
            ]
        ]);
    }
}
```

#### 3.2 Role Index View
**File:** `resources/views/livewire/admin/rbac/roles/role-index.blade.php`

```php
<div class="space-y-6">
    <!-- Header Actions -->
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
            <!-- Search -->
            <flux:input 
                wire:model.live.debounce.300ms="search"
                placeholder="{{ __('Search roles...') }}"
                class="w-64">
                <x-slot name="iconTrailing">
                    <flux:icon name="magnifying-glass" class="h-4 w-4" />
                </x-slot>
            </flux:input>

            <!-- Filters -->
            <flux:select wire:model.live="filterHierarchy" placeholder="{{ __('All Levels') }}">
                <flux:option value="1">{{ __('Level 1 - Super Admin') }}</flux:option>
                <flux:option value="2">{{ __('Level 2 - Admin Manager') }}</flux:option>
                <flux:option value="3">{{ __('Level 3 - Administrator') }}</flux:option>
            </flux:select>

            <flux:select wire:model.live="filterStatus" placeholder="{{ __('All Status') }}">
                <flux:option value="active">{{ __('Active') }}</flux:option>
                <flux:option value="inactive">{{ __('Inactive') }}</flux:option>
            </flux:select>
        </div>

        <!-- Create Button -->
        <flux:button 
            variant="primary" 
            href="{{ route('admin.rbac.roles.create') }}"
            wire:navigate>
            <flux:icon name="plus" class="h-4 w-4" />
            {{ __('Create Role') }}
        </flux:button>
    </div>

    <!-- Roles Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-700">
        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
            <thead class="bg-zinc-50 dark:bg-zinc-800">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Role') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Hierarchy') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Users') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Status') }}
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Actions') }}
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-900 divide-y divide-zinc-200 dark:divide-zinc-700">
                @forelse($roles as $role)
                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-800">
                    <td class="px-6 py-4">
                        <div>
                            <flux:text class="font-medium">{{ $role->role_name }}</flux:text>
                            @if($role->description)
                            <flux:text class="text-sm text-zinc-500">{{ $role->description }}</flux:text>
                            @endif
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <x-admin.hierarchy-indicator :level="$role->hierarchy_level" />
                    </td>
                    <td class="px-6 py-4">
                        <flux:badge variant="outline">
                            {{ $role->userAssignments->count() }} {{ __('users') }}
                        </flux:badge>
                    </td>
                    <td class="px-6 py-4">
                        @if($role->isOperational())
                            <flux:badge variant="success">{{ __('Active') }}</flux:badge>
                        @else
                            <flux:badge variant="danger">{{ __('Inactive') }}</flux:badge>
                        @endif
                    </td>
                    <td class="px-6 py-4 text-right">
                        <div class="flex items-center justify-end gap-2">
                            <flux:button 
                                variant="ghost" 
                                size="sm"
                                href="{{ route('admin.rbac.roles.edit', $role) }}"
                                wire:navigate>
                                {{ __('Edit') }}
                            </flux:button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="5" class="px-6 py-12 text-center">
                        <flux:text class="text-zinc-500">{{ __('No roles found.') }}</flux:text>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $roles->links() }}
    </div>
</div>
```

---

## 4. Required Middleware Enhancement

### 4.1 Hierarchy Level Middleware
**File:** `app/Http/Middleware/CheckMinimumHierarchyLevel.php`

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckMinimumHierarchyLevel
{
    public function handle(Request $request, Closure $next, int $minLevel): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $userHierarchyLevel = session('active_role_level');
        $isSuperAdmin = session('is_super_admin', false);

        // Super Admin bypasses all hierarchy checks
        if ($isSuperAdmin) {
            return $next($request);
        }

        // Check if user meets minimum hierarchy level (lower number = higher privilege)
        if (!$userHierarchyLevel || $userHierarchyLevel > $minLevel) {
            abort(403, 'Insufficient privileges for this action.');
        }

        return $next($request);
    }
}
```

### 4.2 Register Middleware
**File:** `bootstrap/app.php` - Add to middleware aliases:

```php
$middleware->alias([
    'active' => EnsureUserIsActive::class,
    'check.sessions' => CheckActiveSessions::class,
    'check.roles' => CheckUserHasAnyRole::class,
    'update.activity' => UpdateLastActivity::class,
    'min.hierarchy' => CheckMinimumHierarchyLevel::class, // Add this line
]);
```

---

## 5. Implementation Timeline

### Week 1: Foundation & Role Management
- ✅ Create base admin layout and navigation components
- ✅ Implement hierarchy level middleware
- ✅ Build Role Index, Create, Edit components
- ✅ Create role management views
- ✅ Add admin routes and navigation

### Week 2: User Role Assignment
- ✅ Build User Role Index component
- ✅ Create User Role Assignment interface
- ✅ Implement role assignment history
- ✅ Add bulk assignment capabilities

### Week 3: Permission Management
- ✅ Build Permission Index and Create components
- ✅ Implement Permission Grant interface
- ✅ Create permission categorization
- ✅ Add permission audit trails

### Week 4: Departments & Audit
- ✅ Build Department management components
- ✅ Implement Access Audit interface
- ✅ Create Role Usage analytics
- ✅ Add comprehensive reporting

---

## 6. Key Implementation Notes

### 6.1 Existing Resource Utilization
- **Flux UI Components:** All UI elements use existing Flux components
- **Livewire Patterns:** Follow established patterns from Settings components
- **Layout Structure:** Extend existing settings layout pattern
- **Middleware Integration:** Use existing RBAC middleware system
- **Validation:** Leverage existing model validation rules

### 6.2 No Additional Dependencies
- **No new packages** required
- **No custom CSS** needed (use Tailwind classes)
- **No custom JavaScript** required
- **Use existing icons** from Flux icon library
- **Leverage existing components** and patterns

### 6.3 Laravel 12 Compliance
- **Livewire 3** component structure
- **Route model binding** for clean URLs
- **Middleware aliases** in bootstrap/app.php
- **Component-based architecture**
- **Wire:navigate** for SPA-like experience

---

## 7. Detailed Component Specifications

### 7.1 Role Create Component
**File:** `app/Livewire/Admin/Rbac/Roles/RoleCreate.php`

```php
<?php

namespace App\Livewire\Admin\Rbac\Roles;

use Livewire\Component;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\OrganizationDepartment;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;

class RoleCreate extends Component
{
    #[Validate('required|string|max:255')]
    public $role_name = '';

    #[Validate('nullable|string|max:1000')]
    public $description = '';

    #[Validate('nullable|exists:system_roles,id')]
    public $parent_role_id = null;

    #[Validate('required|integer|min:3|max:10')]
    public $hierarchy_level = 3;

    #[Validate('required|in:web,api,mobile')]
    public $guard_type = 'web';

    #[Validate('nullable|date')]
    public $active_from = null;

    #[Validate('nullable|date|after:active_from')]
    public $active_until = null;

    public function mount()
    {
        // Set default active_from to today
        $this->active_from = now()->toDateString();
    }

    public function getAvailableParentRolesProperty()
    {
        $currentUserLevel = session('active_role_level', 999);

        return SystemRole::where('entity_id', Auth::user()->entity_id)
            ->where('hierarchy_level', '<', $this->hierarchy_level)
            ->where('hierarchy_level', '>', $currentUserLevel)
            ->operational()
            ->orderBy('hierarchy_level')
            ->get();
    }

    public function save()
    {
        $this->validate();

        // Additional business logic validation
        $this->validateHierarchyLevel();

        SystemRole::create([
            'entity_id' => Auth::user()->entity_id,
            'role_name' => $this->role_name,
            'description' => $this->description,
            'parent_role_id' => $this->parent_role_id,
            'hierarchy_level' => $this->hierarchy_level,
            'guard_type' => $this->guard_type,
            'active_from' => $this->active_from,
            'active_until' => $this->active_until,
            'is_active' => false, // Requires approval
            'approval_status' => 'pending',
            'created_by' => Auth::id(),
        ]);

        session()->flash('success', __('Role created successfully and is pending approval.'));

        return $this->redirect(route('admin.rbac.roles.index'), navigate: true);
    }

    private function validateHierarchyLevel()
    {
        $currentUserLevel = session('active_role_level', 999);

        if ($this->hierarchy_level <= $currentUserLevel) {
            $this->addError('hierarchy_level',
                __('You can only create roles with lower privilege levels than your own.'));
        }
    }

    public function render()
    {
        return view('livewire.admin.rbac.roles.role-create', [
            'availableParentRoles' => $this->availableParentRoles
        ])->layout('components.admin.layout', [
            'heading' => __('Create New Role'),
            'subheading' => __('Define a new system role with specific permissions'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Roles'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Create'), 'url' => route('admin.rbac.roles.create')]
            ]
        ]);
    }
}
```

### 7.2 Role Create View
**File:** `resources/views/livewire/admin/rbac/roles/role-create.blade.php`

```php
<form wire:submit="save" class="space-y-6">
    <!-- Basic Information -->
    <flux:fieldset>
        <flux:legend>{{ __('Basic Information') }}</flux:legend>

        <div class="grid gap-6 md:grid-cols-2">
            <flux:field>
                <flux:label>{{ __('Role Name') }}</flux:label>
                <flux:input wire:model="role_name" placeholder="{{ __('Enter role name') }}" />
                <flux:error name="role_name" />
            </flux:field>

            <flux:field>
                <flux:label>{{ __('Hierarchy Level') }}</flux:label>
                <flux:select wire:model.live="hierarchy_level">
                    @for($i = 3; $i <= 10; $i++)
                        <flux:option value="{{ $i }}">
                            {{ __('Level :level', ['level' => $i]) }}
                            @if($i === 3) ({{ __('Administrator') }}) @endif
                        </flux:option>
                    @endfor
                </flux:select>
                <flux:description>{{ __('Lower numbers indicate higher privileges') }}</flux:description>
                <flux:error name="hierarchy_level" />
            </flux:field>
        </div>

        <flux:field>
            <flux:label>{{ __('Description') }}</flux:label>
            <flux:textarea
                wire:model="description"
                placeholder="{{ __('Describe the role responsibilities and scope') }}"
                rows="3" />
            <flux:error name="description" />
        </flux:field>
    </flux:fieldset>

    <!-- Role Hierarchy -->
    <flux:fieldset>
        <flux:legend>{{ __('Role Hierarchy') }}</flux:legend>

        <flux:field>
            <flux:label>{{ __('Parent Role') }}</flux:label>
            <flux:select wire:model="parent_role_id" placeholder="{{ __('Select parent role (optional)') }}">
                @foreach($availableParentRoles as $parentRole)
                    <flux:option value="{{ $parentRole->id }}">
                        {{ $parentRole->role_name }} ({{ __('Level :level', ['level' => $parentRole->hierarchy_level]) }})
                    </flux:option>
                @endforeach
            </flux:select>
            <flux:description>{{ __('Parent role defines inheritance relationships') }}</flux:description>
            <flux:error name="parent_role_id" />
        </flux:field>
    </flux:fieldset>

    <!-- Access Configuration -->
    <flux:fieldset>
        <flux:legend>{{ __('Access Configuration') }}</flux:legend>

        <div class="grid gap-6 md:grid-cols-3">
            <flux:field>
                <flux:label>{{ __('Guard Type') }}</flux:label>
                <flux:select wire:model="guard_type">
                    <flux:option value="web">{{ __('Web Interface') }}</flux:option>
                    <flux:option value="api">{{ __('API Access') }}</flux:option>
                    <flux:option value="mobile">{{ __('Mobile App') }}</flux:option>
                </flux:select>
                <flux:error name="guard_type" />
            </flux:field>

            <flux:field>
                <flux:label>{{ __('Active From') }}</flux:label>
                <flux:input type="date" wire:model="active_from" />
                <flux:error name="active_from" />
            </flux:field>

            <flux:field>
                <flux:label>{{ __('Active Until') }}</flux:label>
                <flux:input type="date" wire:model="active_until" />
                <flux:description>{{ __('Leave empty for no expiration') }}</flux:description>
                <flux:error name="active_until" />
            </flux:field>
        </div>
    </flux:fieldset>

    <!-- Form Actions -->
    <div class="flex items-center justify-between pt-6 border-t border-zinc-200 dark:border-zinc-700">
        <flux:button
            variant="ghost"
            href="{{ route('admin.rbac.roles.index') }}"
            wire:navigate>
            {{ __('Cancel') }}
        </flux:button>

        <flux:button type="submit" variant="primary">
            {{ __('Create Role') }}
        </flux:button>
    </div>
</form>
```

### 7.3 User Role Assignment Component
**File:** `app/Livewire/Admin/Rbac/Users/<USER>

```php
<?php

namespace App\Livewire\Admin\Rbac\Users;

use Livewire\Component;
use App\Models\User;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\UserRoleAssignment;
use App\Models\Rbac\OrganizationDepartment;
use App\Models\Rbac\UserDepartmentAssignment;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;

class UserRoleAssign extends Component
{
    public User $user;

    #[Validate('required|exists:system_roles,id')]
    public $role_id = null;

    #[Validate('nullable|exists:organization_departments,id')]
    public $department_id = null;

    #[Validate('nullable|date')]
    public $assigned_from = null;

    #[Validate('nullable|date|after:assigned_from')]
    public $assigned_until = null;

    #[Validate('nullable|string|max:500')]
    public $assignment_notes = '';

    public function mount(User $user)
    {
        $this->user = $user;
        $this->assigned_from = now()->toDateString();
    }

    public function getAvailableRolesProperty()
    {
        $currentUserLevel = session('active_role_level', 999);

        return SystemRole::where('entity_id', Auth::user()->entity_id)
            ->where('hierarchy_level', '>', $currentUserLevel)
            ->operational()
            ->whereNotIn('id', $this->user->roleAssignments()->pluck('role_id'))
            ->orderBy('hierarchy_level')
            ->get();
    }

    public function getAvailableDepartmentsProperty()
    {
        return OrganizationDepartment::where('entity_id', Auth::user()->entity_id)
            ->operational()
            ->orderBy('dept_name')
            ->get();
    }

    public function getCurrentAssignmentsProperty()
    {
        return $this->user->roleAssignments()
            ->with(['role', 'department'])
            ->current()
            ->get();
    }

    public function assignRole()
    {
        $this->validate();

        // Check if assignment already exists
        $existingAssignment = UserRoleAssignment::where([
            'user_id' => $this->user->id,
            'role_id' => $this->role_id,
            'entity_id' => Auth::user()->entity_id
        ])->first();

        if ($existingAssignment) {
            $this->addError('role_id', __('User already has this role assigned.'));
            return;
        }

        UserRoleAssignment::create([
            'entity_id' => Auth::user()->entity_id,
            'user_id' => $this->user->id,
            'role_id' => $this->role_id,
            'assigned_from' => $this->assigned_from,
            'assigned_until' => $this->assigned_until,
            'assignment_notes' => $this->assignment_notes,
            'is_active' => false, // Requires approval
            'approval_status' => 'pending',
            'created_by' => Auth::id(),
        ]);

        // Assign to department if selected
        if ($this->department_id) {
            UserDepartmentAssignment::create([
                'entity_id' => Auth::user()->entity_id,
                'user_id' => $this->user->id,
                'department_id' => $this->department_id,
                'assigned_from' => $this->assigned_from,
                'assigned_until' => $this->assigned_until,
                'is_active' => false,
                'approval_status' => 'pending',
                'created_by' => Auth::id(),
            ]);
        }

        session()->flash('success', __('Role assignment created and is pending approval.'));

        // Reset form
        $this->reset(['role_id', 'department_id', 'assigned_until', 'assignment_notes']);
        $this->assigned_from = now()->toDateString();
    }

    public function removeAssignment($assignmentId)
    {
        $assignment = UserRoleAssignment::where('id', $assignmentId)
            ->where('entity_id', Auth::user()->entity_id)
            ->first();

        if ($assignment) {
            $assignment->update([
                'is_active' => false,
                'updated_by' => Auth::id(),
            ]);

            session()->flash('success', __('Role assignment removed successfully.'));
        }
    }

    public function render()
    {
        return view('livewire.admin.rbac.users.user-role-assign', [
            'availableRoles' => $this->availableRoles,
            'availableDepartments' => $this->availableDepartments,
            'currentAssignments' => $this->currentAssignments,
        ])->layout('components.admin.layout', [
            'heading' => __('Assign Roles to :name', ['name' => $this->user->name]),
            'subheading' => __('Manage user role assignments and permissions'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.users.index')],
                ['label' => __('Users'), 'url' => route('admin.rbac.users.index')],
                ['label' => $this->user->name, 'url' => route('admin.rbac.users.assign', $this->user)]
            ]
        ]);
    }
}
```

---

## 8. Custom Blade Components

### 8.1 Hierarchy Indicator Component
**File:** `resources/views/components/admin/hierarchy-indicator.blade.php`

```php
@props(['level'])

@php
$levelConfig = [
    1 => ['label' => 'Super Admin', 'color' => 'red', 'icon' => 'shield-exclamation'],
    2 => ['label' => 'Admin Manager', 'color' => 'orange', 'icon' => 'shield-check'],
    3 => ['label' => 'Administrator', 'color' => 'blue', 'icon' => 'user-circle'],
];

$config = $levelConfig[$level] ?? ['label' => "Level {$level}", 'color' => 'gray', 'icon' => 'user'];
@endphp

<div class="flex items-center gap-2">
    <flux:badge variant="{{ $config['color'] }}">
        <flux:icon name="{{ $config['icon'] }}" class="h-3 w-3" />
        {{ __('Level :level', ['level' => $level]) }}
    </flux:badge>
    <flux:text class="text-xs text-zinc-500">{{ $config['label'] }}</flux:text>
</div>
```

### 8.2 Role Badge Component
**File:** `resources/views/components/admin/role-badge.blade.php`

```php
@props(['role', 'showLevel' => true])

<div class="flex items-center gap-2">
    <flux:badge variant="outline">
        {{ $role->role_name }}
    </flux:badge>

    @if($showLevel)
        <x-admin.hierarchy-indicator :level="$role->hierarchy_level" />
    @endif

    @if(!$role->isOperational())
        <flux:badge variant="danger" size="sm">{{ __('Inactive') }}</flux:badge>
    @endif
</div>
```

This plan provides a complete roadmap for implementing all missing RBAC administrative interfaces while maintaining consistency with existing codebase patterns and utilizing only existing resources.
