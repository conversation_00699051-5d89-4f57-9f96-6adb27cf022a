{{-- 5. Search Component --}}
{{-- resources/views/components/search-modal.blade.php --}}
<div x-data="{ open: false }" x-on:keydown.cmd.k.prevent="open = true" x-on:keydown.ctrl.k.prevent="open = true">
    <!-- Search Trigger -->
    <button
        type="button"
        class="flex h-10 w-10 items-center justify-center rounded-lg text-zinc-600 hover:bg-zinc-100 hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-100 transition-colors"
        x-on:click="open = true">
        <flux:icon.magnifying-glass class="h-5 w-5" />
    </button>

    <!-- Search Modal -->
    <div x-show="open"
        x-on:keydown.escape.prevent.stop="open = false"
        class="fixed inset-0 z-50 overflow-y-auto"
        style="display: none;">
        <div class="flex min-h-screen items-start justify-center px-4 pt-16">
            <div x-show="open"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                class="fixed inset-0 transition-opacity bg-zinc-500 bg-opacity-75"
                x-on:click="open = false"></div>

            <div x-show="open"
                x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="w-full max-w-lg transform transition-all bg-white dark:bg-zinc-900 rounded-lg shadow-xl">

                <div class="p-4">
                    <div class="relative">
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            @include('flux.icon.magnifying-glass', ['class' => 'h-5 w-5'])
                        </div>
                        <input
                            type="search"
                            placeholder="{{ __('Search...') }}"
                            class="block w-full rounded-md border-0 py-1.5 pl-10 text-zinc-900 ring-1 ring-inset ring-zinc-300 placeholder:text-zinc-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-zinc-800 dark:text-white dark:ring-zinc-700 dark:placeholder:text-zinc-500 dark:focus:ring-indigo-500"
                            wire:model.live.debounce.300ms="search"
                            autofocus
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>