<?php
// app/Models/Information/Kyc.php
namespace App\Models\Information;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use App\Models\Entity\Entity;
use App\Models\User;

/**
 * Class Kyc
 *
 * Represents Know Your Customer (KYC) documentation that can be associated with 
 * both entities and users. Uses polymorphic many-to-many relationships via the 
 * kycables pivot table.
 *
 * KYC documents are essential for compliance and verification purposes. Each document
 * has a type (Passport, Driver License, etc.), unique number, and optional file upload.
 * The system supports multiple KYC documents per entity/user with primary designation.
 *
 * @property int $id
 * @property string $document_type Type of document (e.g., Passport, Driver License)
 * @property string $document_number Unique number of the document
 * @property string|null $document_file Path to uploaded document copy
 * @property \Carbon\Carbon|null $expiry_date Date when the document expires
 * @property string $verification_status Status of document verification (pending, verified, rejected, expired)
 * @property bool $is_active Status: true=active, false=inactive
 * @property int $created_by User ID who created this record
 * @property int|null $updated_by User ID who last updated this record
 * @property int|null $deleted_by User ID who soft deleted this record
 * @property int|null $restored_by User ID who restored this record
 * @property \Carbon\Carbon|null $restored_at When the record was restored
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder withFiles()
 * @method static \Illuminate\Database\Eloquent\Builder search(string $search)
 */
class Kyc extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'kycs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'document_type',
        'document_number',
        'document_file',
        'expiry_date',
        'verification_status',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'expiry_date' => 'date',
        'restored_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'display_name',
        'has_file',
        'file_url',
        'document_type_label'
    ];

    /**
     * Document type constants with labels.
     *
     * @var array<string, string>
     */
    const DOCUMENT_TYPES = [
        'passport' => 'Passport',
        'drivers_license' => 'Driver\'s License',
        'national_id' => 'National ID Card',
        'voter_id' => 'Voter ID Card',
        'pan_card' => 'PAN Card',
        'aadhaar' => 'Aadhaar Card',
        'gst_certificate' => 'GST Certificate',
        'trade_license' => 'Trade License',
        'company_registration' => 'Company Registration Certificate',
        'partnership_deed' => 'Partnership Deed',
        'moa_aoa' => 'Memorandum & Articles of Association',
        'bank_statement' => 'Bank Statement',
        'utility_bill' => 'Utility Bill',
        'other' => 'Other Document'
    ];

    /**
     * Documents that are typically for individuals.
     *
     * @var array<string>
     */
    const INDIVIDUAL_DOCUMENTS = [
        'passport',
        'drivers_license',
        'national_id',
        'voter_id',
        'pan_card',
        'aadhaar'
    ];

    /**
     * Documents that are typically for businesses/entities.
     *
     * @var array<string>
     */
    const BUSINESS_DOCUMENTS = [
        'gst_certificate',
        'trade_license',
        'company_registration',
        'partnership_deed',
        'moa_aoa'
    ];

    // ===== ACCESSORS =====

    /**
     * Get human-readable document type.
     *
     * @return string
     */
    public function getDocumentTypeLabelAttribute(): string
    {
        return self::DOCUMENT_TYPES[$this->document_type] ?? 'Unknown Document';
    }

    /**
     * Get display name for the KYC document.
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->document_type_label} - {$this->document_number}";
    }

    /**
     * Check if document has an uploaded file.
     *
     * @return bool
     */
    public function getHasFileAttribute(): bool
    {
        return !empty($this->document_file) && $this->fileExists();
    }

    /**
     * Get the URL to access the document file.
     *
     * @return string|null
     */
    public function getFileUrlAttribute(): ?string
    {
        if (!$this->has_file) {
            return null;
        }

        return Storage::url($this->document_file);
    }

    // ===== HELPER METHODS =====

    /**
     * Check if the document file exists in storage.
     *
     * @return bool
     */
    public function fileExists(): bool
    {
        if (empty($this->document_file)) {
            return false;
        }

        return Storage::exists($this->document_file);
    }

    /**
     * Get the file size in human readable format.
     *
     * @return string|null
     */
    public function getFileSize(): ?string
    {
        if (!$this->has_file) {
            return null;
        }

        $size = Storage::size($this->document_file);
        
        if ($size < 1024) {
            return $size . ' B';
        } elseif ($size < 1048576) {
            return round($size / 1024, 2) . ' KB';
        } else {
            return round($size / 1048576, 2) . ' MB';
        }
    }

    /**
     * Get the file extension.
     *
     * @return string|null
     */
    public function getFileExtension(): ?string
    {
        if (empty($this->document_file)) {
            return null;
        }

        return pathinfo($this->document_file, PATHINFO_EXTENSION);
    }

    /**
     * Check if this is an individual document type.
     *
     * @return bool
     */
    public function isIndividualDocument(): bool
    {
        return in_array($this->document_type, self::INDIVIDUAL_DOCUMENTS);
    }

    /**
     * Check if this is a business document type.
     *
     * @return bool
     */
    public function isBusinessDocument(): bool
    {
        return in_array($this->document_type, self::BUSINESS_DOCUMENTS);
    }

    /**
     * Check if document number format is valid for the document type.
     *
     * @return bool
     */
    public function hasValidFormat(): bool
    {
        return match ($this->document_type) {
            'pan_card' => preg_match('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $this->document_number),
            'aadhaar' => preg_match('/^[0-9]{12}$/', $this->document_number),
            'gst_certificate' => preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $this->document_number),
            default => !empty($this->document_number)
        };
    }

    // ===== POLYMORPHIC RELATIONSHIPS =====

    /**
     * Get all entities that have this KYC document.
     *
     * @return MorphToMany
     */
    public function entities(): MorphToMany
    {
        return $this->morphedByMany(Entity::class, 'kycable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get all users that have this KYC document.
     *
     * @return MorphToMany
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'kycable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get all models (entities and users) that have this KYC document.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllKycables()
    {
        return collect()
            ->merge($this->entities)
            ->merge($this->users);
    }

    /**
     * Check if this KYC document is primary for any model.
     *
     * @return bool
     */
    public function isPrimaryForAny(): bool
    {
        $entityPrimary = $this->entities()->wherePivot('is_primary', true)->exists();
        $userPrimary = $this->users()->wherePivot('is_primary', true)->exists();

        return $entityPrimary || $userPrimary;
    }

    // ===== AUDIT RELATIONSHIPS =====

    /**
     * Get the user who created this KYC document.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this KYC document.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who soft deleted this KYC document.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored this KYC document.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to only active KYC documents.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to KYC documents by type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Scope to KYC documents with uploaded files.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithFiles($query)
    {
        return $query->whereNotNull('document_file');
    }

    /**
     * Scope to individual document types.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIndividualDocuments($query)
    {
        return $query->whereIn('document_type', self::INDIVIDUAL_DOCUMENTS);
    }

    /**
     * Scope to business document types.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBusinessDocuments($query)
    {
        return $query->whereIn('document_type', self::BUSINESS_DOCUMENTS);
    }

    /**
     * Scope to search KYC documents by document type, number, or file name.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('document_type', 'like', "%{$search}%")
                ->orWhere('document_number', 'like', "%{$search}%")
                ->orWhere('document_file', 'like', "%{$search}%");
        });
    }

    // ===== STATIC METHODS =====

    /**
     * Find KYC document by document number.
     *
     * @param string $documentNumber
     * @param string|null $documentType
     * @return self|null
     */
    public static function findByDocumentNumber(string $documentNumber, ?string $documentType = null): ?self
    {
        $query = self::where('document_number', $documentNumber);

        if ($documentType) {
            $query->where('document_type', $documentType);
        }

        return $query->first();
    }

    /**
     * Check if document number already exists.
     *
     * @param string $documentNumber
     * @param string|null $documentType
     * @param int|null $excludeId
     * @return bool
     */
    public static function documentNumberExists(string $documentNumber, ?string $documentType = null, ?int $excludeId = null): bool
    {
        $query = self::where('document_number', $documentNumber);

        if ($documentType) {
            $query->where('document_type', $documentType);
        }

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get available document types for dropdown.
     *
     * @param bool $forBusiness Whether to return business document types
     * @return array
     */
    public static function getDocumentTypes(bool $forBusiness = false): array
    {
        if ($forBusiness) {
            return array_intersect_key(
                self::DOCUMENT_TYPES,
                array_flip(array_merge(self::BUSINESS_DOCUMENTS, ['other']))
            );
        }

        return self::DOCUMENT_TYPES;
    }

    // ===== FILE HANDLING =====

    /**
     * Delete the associated file from storage.
     *
     * @return bool
     */
    public function deleteFile(): bool
    {
        if (!$this->has_file) {
            return true;
        }

        $deleted = Storage::delete($this->document_file);
        
        if ($deleted) {
            $this->update(['document_file' => null]);
        }

        return $deleted;
    }

    /**
     * Override the delete method to also delete files.
     *
     * @return bool|null
     */
    public function delete()
    {
        // Delete file when soft deleting
        $this->deleteFile();
        
        return parent::delete();
    }

    /**
     * Override the forceDelete method to also delete files.
     *
     * @return bool|null
     */
    public function forceDelete()
    {
        // Delete file when force deleting
        $this->deleteFile();
        
        return parent::forceDelete();
    }
}