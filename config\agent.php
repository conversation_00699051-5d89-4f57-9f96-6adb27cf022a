<?php
// config\agent.php
return [

    // Toggle user-agent caching
    'cache' => env('AGENT_CACHE', true),

    // Browser detection patterns
    'browsers' => [
        'Brave'     => 'Brave',
        'Chrome'    => 'Chrome|CriOS',
        'Edge'      => 'Edg|Edge|EdgA|EdgIOS',
        'Firefox'   => 'Firefox|FxiOS',
        'IE'        => 'MSIE|Trident',
        'Opera'     => 'Opera|OPR',
        'Safari'    => 'Safari|^((?!Chrome|Android).)*$',
        'Samsung'   => 'SamsungBrowser',
        'UCBrowser' => 'UCBrowser|UCWEB',
        'Vivaldi'   => 'Vivaldi',
        'WeChat'    => 'MicroMessenger',
    ],

    // Platform detection patterns
    'platforms' => [
        'Android'     => 'Android',
        'BlackBerry'  => 'BlackBerry|BB10|RIM Tablet OS',
        'ChromeOS'    => 'CrOS',
        'iOS'         => 'iPhone|iPad|iPod',
        'Linux'       => 'Linux|X11|Ubuntu|Debian',
        'Mac'         => 'Macintosh|Mac OS X|MacPPC|MacIntel|Mac_PowerPC',
        'Unix'        => 'Unix',
        'Windows'     => 'Windows NT|WinNT|Win32',
    ],

    // Device brand patterns
    'device_brands' => [
        'Apple'    => 'iPhone|iPad|iPod|Macintosh',
        'Google'   => 'Pixel|Nexus',
        'Huawei'   => 'HUAWEI|Honor',
        'OnePlus'  => 'OnePlus',
        'Samsung'  => 'SM-|SAMSUNG|Galaxy',
        'Sony'     => 'Xperia',
        'Xiaomi'   => 'Redmi|Mi|POCO',
    ],

    // Known bot identifiers
    'bots' => [
        'Baiduspider',
        'Bingbot',
        'DuckDuckBot',
        'Exabot',
        'Googlebot',
        'LinkedInBot',
        'Sogou',
        'Slurp',
        'TelegramBot',
        'Twitterbot',
        'WhatsApp',
        'YandexBot',
        'facebot',
        'ia_archiver',
    ],
];
