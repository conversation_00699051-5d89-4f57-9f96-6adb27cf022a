<?php

namespace Tests\Unit;

use App\Models\Entity\Entity;
use App\Models\User;
use App\Services\LoginValidationService;
use PHPUnit\Framework\TestCase;

class LoginValidationServiceTest extends TestCase
{
    protected LoginValidationService $service;
    protected User $superAdmin;
    protected User $regularUser;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new LoginValidationService();
        
        // Create mock users
        $this->superAdmin = $this->createMock(User::class);
        $this->superAdmin->method('__get')->with('id')->willReturn(LoginValidationService::SUPER_ADMIN_USER_ID);
        
        $this->regularUser = $this->createMock(User::class);
        $this->regularUser->method('__get')->with('id')->willReturn(2);
    }
    
    public function testIsSuperAdmin()
    {
        $this->assertTrue($this->service->isSuperAdmin($this->superAdmin));
        $this->assertFalse($this->service->isSuperAdmin($this->regularUser));
    }
} 