<div>
    <div class="max-w-2xl mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Personal Details</h3>
        
        <form wire:submit.prevent="saveAndContinue">
            {{-- Owner Account Details Section --}}
            <div class="bg-white rounded-lg border border-gray-300 p-5 mb-6">
                <h4 class="text-lg font-medium mb-4">Account Details</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="ownerName" class="block text-sm font-medium text-gray-700 mb-1">Owner Name</label>
                        <input 
                            wire:model="ownerName" 
                            id="ownerName"
                            type="text"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Full name"
                        >
                        @error('ownerName') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div>
                        <label for="ownerPhone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input 
                            wire:model="ownerPhone" 
                            id="ownerPhone"
                            type="tel"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="9876543210"
                            maxlength="10"
                        >
                        @error('ownerPhone') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="ownerEmail" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <input 
                        wire:model="ownerEmail" 
                        id="ownerEmail"
                        type="email"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-100"
                        readonly
                    >
                    <p class="mt-1 text-xs text-gray-500">Email address from verification cannot be changed</p>
                    @error('ownerEmail') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <input 
                            wire:model="password" 
                            id="password"
                            type="password"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('password') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div>
                        <label for="passwordConfirmation" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                        <input 
                            wire:model="passwordConfirmation" 
                            id="passwordConfirmation"
                            type="password"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('passwordConfirmation') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
                
                <div class="mt-2">
                    <p class="text-xs text-gray-600">
                        Password must be at least 8 characters and include uppercase, lowercase, number, and special character.
                    </p>
                </div>
            </div>
            
            {{-- Personal Contact Details Section --}}
            <div class="bg-white rounded-lg border border-gray-300 p-5 mb-6">
                <h4 class="text-lg font-medium mb-4">Personal Contact Details</h4>
                <p class="text-sm text-gray-600 mb-4">Pre-filled from business contact. You can update if needed.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="personalContactName" class="block text-sm font-medium text-gray-700 mb-1">Contact Name</label>
                        <input 
                            wire:model="personalContactName" 
                            id="personalContactName"
                            type="text"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('personalContactName') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div>
                        <label for="personalContactEmail" class="block text-sm font-medium text-gray-700 mb-1">Email (Optional)</label>
                        <input 
                            wire:model="personalContactEmail" 
                            id="personalContactEmail"
                            type="email"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('personalContactEmail') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div>
                        <label for="personalContactPhone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input 
                            wire:model="personalContactPhone" 
                            id="personalContactPhone"
                            type="tel"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            maxlength="10"
                        >
                        @error('personalContactPhone') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                        <label for="personalContactTitle" class="block text-sm font-medium text-gray-700 mb-1">Title (Optional)</label>
                        <input 
                            wire:model="personalContactTitle" 
                            id="personalContactTitle"
                            type="text"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Mr., Ms., Dr., etc."
                            maxlength="20"
                        >
                        @error('personalContactTitle') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div>
                        <label for="personalContactPosition" class="block text-sm font-medium text-gray-700 mb-1">Position (Optional)</label>
                        <input 
                            wire:model="personalContactPosition" 
                            id="personalContactPosition"
                            type="text"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Job title or role"
                            maxlength="100"
                        >
                        @error('personalContactPosition') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
            </div>
            
            {{-- Personal Address Section --}}
            <div class="bg-white rounded-lg border border-gray-300 p-5 mb-8">
                <h4 class="text-lg font-medium mb-4">Personal Address</h4>
                <p class="text-sm text-gray-600 mb-4">Pre-filled from business address. You can update if needed.</p>
                
                <div class="space-y-4">
                    <div>
                        <label for="personalAddressLine1" class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                        <input 
                            wire:model="personalAddressLine1" 
                            id="personalAddressLine1"
                            type="text"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('personalAddressLine1') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div>
                        <label for="personalAddressLine2" class="block text-sm font-medium text-gray-700 mb-1">Address Line 2 (Optional)</label>
                        <input 
                            wire:model="personalAddressLine2" 
                            id="personalAddressLine2"
                            type="text"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('personalAddressLine2') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="personalState" class="block text-sm font-medium text-gray-700 mb-1">State</label>
                            <select 
                                wire:model.live="personalState" 
                                id="personalState"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                            >
                                <option value="">Select State</option>
                                @foreach($states as $state)
                                    <option value="{{ $state['code'] }}">{{ $state['name'] }}</option>
                                @endforeach
                            </select>
                            @error('personalState') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        
                        <div>
                            <label for="personalCity" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                            <select 
                                wire:model="personalCity" 
                                id="personalCity"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                                {{ empty($personalState) ? 'disabled' : '' }}
                            >
                                <option value="">Select City</option>
                                @foreach($cities as $city)
                                    <option value="{{ $city }}">{{ $city }}</option>
                                @endforeach
                            </select>
                            @error('personalCity') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        
                        <div>
                            <label for="personalPostalCode" class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                            <input 
                                wire:model="personalPostalCode" 
                                id="personalPostalCode"
                                type="text"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                maxlength="6"
                            >
                            @error('personalPostalCode') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                    </div>
                </div>
            </div>
            
            {{-- Navigation --}}
            <div class="flex justify-between">
                <button 
                    type="button"
                    wire:click="stepBack" 
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    Previous
                </button>
                
                <button 
                    type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    Continue
                    <svg class="w-5 h-5 ml-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div> 