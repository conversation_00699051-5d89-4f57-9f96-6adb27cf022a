<?php

namespace App\Traits;

use App\Services\DatabaseChangeLogger;

trait LogsDatabaseChanges
{
    /**
     * Property to store original attributes before save.
     * This property is protected to prevent <PERSON><PERSON> from trying to save it to the database.
     */
    protected $originalAttributesBeforeSave = [];

    /**
     * Boot the trait.
     *
     * @return void
     */
    public static function bootLogsDatabaseChanges()
    {
        // Get or create the logger instance
        $logger = app(DatabaseChangeLogger::class);
        
        // Log when a model is created
        static::created(function ($model) use ($logger) {
            $logger->logChanges($model, 'created', null, $model->attributesToArray());
        });
        
        // Log when a model is updating - capture original attributes
        static::updating(function ($model) use ($logger) {
            // Store original attributes before they change
            $model->originalAttributesBeforeSave = $model->getOriginal();
            return true;
        });
        
        // Log when a model is updated
        static::updated(function ($model) use ($logger) {
            $before = $model->originalAttributesBeforeSave ?? [];
            $after = $model->attributesToArray();
            $logger->logChanges($model, 'updated', $before, $after);
            
            // Clear the stored attributes to prevent memory leaks
            $model->originalAttributesBeforeSave = [];
        });
        
        // Log when a model is deleted
        static::deleted(function ($model) use ($logger) {
            $logger->logChanges($model, 'deleted', $model->attributesToArray(), null);
        });
        
        // Log when a model is restored (from soft delete)
        if (method_exists(static::class, 'restored')) {
            static::restored(function ($model) use ($logger) {
                $logger->logChanges($model, 'restored', null, $model->attributesToArray());
            });
        }
    }
} 