{{-- resources/views/livewire/settings/profile.blade.php - Enhanced Version --}}
<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Profile')" :subheading="__('Update your name and email address')">
        <form wire:submit="updateProfileInformation" class="my-6 w-full space-y-6">
            <!-- Loading overlay for this form -->
            <div wire:loading.flex wire:target="updateProfileInformation"
                class="absolute inset-0 z-10 bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm rounded-lg">
                <div class="flex h-full items-center justify-center">
                    <x-loading-spinner type="spinner" size="md" text="{{ __('Saving changes...') }}" />
                </div>
            </div>

            <flux:input
                wire:model="name"
                :label="__('Name')"
                type="text"
                required
                autofocus
                autocomplete="name"
                wire:loading.attr="disabled"
                wire:target="updateProfileInformation" />

            <div>
                <flux:input
                    wire:model="email"
                    :label="__('Email')"
                    type="email"
                    required
                    autocomplete="email"
                    wire:loading.attr="disabled"
                    wire:target="updateProfileInformation" />

                @if (auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail &&! auth()->user()->hasVerifiedEmail())
                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-yellow-900/20 dark:border-yellow-800">
                    <flux:text class="text-yellow-800 dark:text-yellow-200">
                        {{ __('Your email address is unverified.') }}

                        <button type="button"
                            wire:click.prevent="resendVerificationNotification"
                            wire:loading.attr="disabled"
                            wire:target="resendVerificationNotification"
                            class="font-medium text-yellow-600 hover:text-yellow-500 dark:text-yellow-400 dark:hover:text-yellow-300 underline cursor-pointer disabled:opacity-50">
                            <span wire:loading.remove wire:target="resendVerificationNotification">
                                {{ __('Click here to re-send the verification email.') }}
                            </span>
                            <span wire:loading wire:target="resendVerificationNotification" class="flex items-center gap-2">
                                <x-loading-spinner type="inline" />
                                {{ __('Sending...') }}
                            </span>
                        </button>
                    </flux:text>

                    @if (session('status') === 'verification-link-sent')
                    <flux:text class="mt-2 font-medium text-green-600 dark:text-green-400">
                        {{ __('A new verification link has been sent to your email address.') }}
                    </flux:text>
                    @endif
                </div>
                @endif
            </div>

            <div class="flex items-center gap-4">
                <div class="flex items-center justify-end">
                    <flux:button
                        variant="primary"
                        type="submit"
                        class="w-full relative"
                        wire:loading.attr="disabled"
                        wire:target="updateProfileInformation">
                        <span wire:loading.remove wire:target="updateProfileInformation">
                            {{ __('Save') }}
                        </span>
                        <span wire:loading wire:target="updateProfileInformation" class="flex items-center gap-2">
                            <x-loading-spinner type="inline" />
                            {{ __('Saving...') }}
                        </span>
                    </flux:button>
                </div>

                <x-action-message class="me-3" on="profile-updated">
                    <div class="flex items-center gap-2 text-green-600 dark:text-green-400">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {{ __('Saved.') }}
                    </div>
                </x-action-message>
            </div>
        </form>

        <livewire:settings.delete-user-form />
    </x-settings.layout>
</section>