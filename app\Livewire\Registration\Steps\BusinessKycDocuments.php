<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Auth\RegistrationAttempt;
use App\Models\Information\Kyc;
use Illuminate\Support\Facades\Storage;

class BusinessKycDocuments extends Component
{
    use WithFileUploads;
    
    public $attempt;
    
    public $documents = [];
    public $requiredDocumentTypes = [
        'gst_certificate' => 'GST Certificate',
        'company_registration' => 'Company Registration Certificate'
    ];
    
    public function initDocuments()
    {
        // Required document types
        $requiredTypes = [
            'gst_certificate' => 'GST Certificate',
            'trade_license' => 'Trade License',
            'company_registration' => 'Company Registration Certificate'
        ];
        
        // Initialize documents array
        $documents = [];
        foreach ($requiredTypes as $type => $label) {
            $documents[] = [
                'document_type' => $type,
                'document_number' => '',
                'document_file' => null,
                'document_file_path' => '',
                'filename' => '',
                'expiry_date' => null,
                'verification_status' => 'pending'
            ];
        }
        
        return $documents;
    }
    
    public function loadSavedDocuments()
    {
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        
        if (isset($stagesData['business_kyc_documents'])) {
            $savedDocs = $stagesData['business_kyc_documents'];
            
            foreach ($this->documents as $index => $doc) {
                if (isset($savedDocs[$index])) {
                    $this->documents[$index]['document_number'] = $savedDocs[$index]['document_number'] ?? '';
                    $this->documents[$index]['document_file_path'] = $savedDocs[$index]['document_file_path'] ?? '';
                    $this->documents[$index]['expiry_date'] = $savedDocs[$index]['expiry_date'] ?? null;
                    $this->documents[$index]['verification_status'] = $savedDocs[$index]['verification_status'] ?? 'pending';
                }
            }
        }
    }
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        if (!$this->attempt) {
            return redirect()->route('register');
        }
        
        // Load saved data if exists
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        
        if (isset($stagesData['business_kyc_documents'])) {
            $this->documents = $stagesData['business_kyc_documents'];
        } else {
            // Initialize documents array with required document types
            foreach ($this->requiredDocumentTypes as $type => $label) {
                $this->documents[] = [
                    'document_type' => $type,
                    'document_number' => '',
                    'document_file' => null,
                    'document_file_path' => null,
                    'filename' => null,
                    'expiry_date' => null,
                    'verification_status' => 'pending'
                ];
            }
        }
    }
    
    public function updatedDocuments($value, $key)
    {
        // If file uploaded, store the file information
        if (preg_match('/^(\d+)\.document_file$/', $key, $matches)) {
            $index = $matches[1];
            $file = $this->documents[$index]['document_file'];
            
            if ($file) {
                // Set the filename for display
                $this->documents[$index]['filename'] = $file->getClientOriginalName();
            }
        }
    }
    
    public function removeFile($index)
    {
        // If there's a stored file path, we might need to clean that up later
        $this->documents[$index]['document_file'] = null;
        $this->documents[$index]['filename'] = null;
    }
    
    protected function rules()
    {
        $rules = [];
        
        foreach ($this->documents as $index => $doc) {
            $rules["documents.{$index}.document_type"] = 'required|string';
            $rules["documents.{$index}.document_number"] = 'required|string|min:3|max:30';
            $rules["documents.{$index}.expiry_date"] = 'nullable|date|after:today';
            
            // If no file path exists and no file uploaded, require file
            if (empty($doc['document_file_path']) && empty($doc['document_file'])) {
                $rules["documents.{$index}.document_file"] = 'required|file|mimes:pdf,jpg,jpeg|max:5120'; // 5MB max
            }
            // If file is uploaded, validate it
            elseif (!empty($doc['document_file'])) {
                $rules["documents.{$index}.document_file"] = 'file|mimes:pdf,jpg,jpeg|max:5120'; // 5MB max
            }
        }
        
        return $rules;
    }
    
    protected $messages = [
        'documents.*.document_type.required' => 'Document type is required',
        'documents.*.document_number.required' => 'Document number/identifier is required',
        'documents.*.expiry_date.date' => 'Expiry date must be a valid date',
        'documents.*.expiry_date.after' => 'Expiry date must be in the future',
        'documents.*.document_file.required' => 'Document file is required',
        'documents.*.document_file.file' => 'Must be a valid file',
        'documents.*.document_file.mimes' => 'File must be PDF, JPG or JPEG format',
        'documents.*.document_file.max' => 'File size cannot exceed 5MB',
    ];
    
    public function saveAndContinue()
    {
        // Validate
        $this->validate([
            'documents.*.document_number' => 'required|string',
            'documents.*.expiry_date' => 'nullable|date'
        ]);
        
        // Store files and prepare data for saving
        $docsToSave = [];
        foreach ($this->documents as $index => $doc) {
            // Handle file upload if present
            if (!empty($doc['document_file'])) {
                // Store the file and get the path
                $filePath = $doc['document_file']->store('kyc_documents', 'public');
                $this->documents[$index]['document_file_path'] = $filePath;
                // Clear the upload object as we've stored the file
                $this->documents[$index]['document_file'] = null;
            }
            
            $docsToSave[] = [
                'document_type' => $doc['document_type'],
                'document_number' => $doc['document_number'],
                'document_file_path' => $doc['document_file_path'],
                'expiry_date' => $doc['expiry_date'],
                'verification_status' => $doc['verification_status'] ?? 'pending'
            ];
        }
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['business_kyc_documents'] = $docsToSave;
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('business_kyc_documents', $stagesCompleted)) {
            $stagesCompleted[] = 'business_kyc_documents';
        }
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'additional_documents'
        ]);
        
        $this->dispatch('stepCompleted', 'business_kyc_documents');
    }
    
    public function stepBack()
    {
        $this->dispatch('stepBack');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.business-kyc-documents');
    }
} 