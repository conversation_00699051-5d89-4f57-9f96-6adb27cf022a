<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\UserRoleAssignment;

class SuperAdminSeeder extends Seeder
{
    public function run(): void
    {
        DB::beginTransaction();
        try {
            // 1. Create or update the Super Administrator role (global)
            $role = SystemRole::updateOrCreate(
                [
                    'role_name' => 'Super Administrator',
                    'hierarchy_level' => 1,
                    'entity_id' => null,
                ],
                [
                    'description' => 'System-wide role with unrestricted access',
                    'is_active' => true,
                    'approval_status' => 'approved',
                    'guard_type' => 'web',
                    'active_from' => now(),
                ]
            );

            // 2. Create or update the Super Administrator user (global)
            $user = User::updateOrCreate(
                [
                    'email' => '<EMAIL>',
                ],
                [
                    'name' => 'Avinash Ban',
                    'user_id' => 'SUPERADMIN',
                    'is_active' => true,
                    'email_verified_at' => now(),
                    'password' => Hash::make('<EMAIL>'),
                    'approval_status' => 'approved',
                    'activated_by' => 1, // self, since this is the first user
                    'activated_at' => now(),
                    'created_by' => 1,
                    'updated_by' => 1,
                    'multi_login' => 0,
                    'phone' => '7507980750',
                ]
            );

            // 3. Assign the Super Administrator role to the user (global)
            UserRoleAssignment::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'role_id' => $role->id,
                    'entity_id' => null,
                ],
                [
                    'is_active' => true,
                    'approval_status' => 'approved',
                    'assigned_from' => now(),
                ]
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
} 