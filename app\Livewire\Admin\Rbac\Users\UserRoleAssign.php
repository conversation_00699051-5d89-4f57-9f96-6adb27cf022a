<?php

namespace App\Livewire\Admin\Rbac\Users;

use Livewire\Component;
use App\Models\User;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\UserRoleAssignment;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;
use Livewire\Attributes\Layout;

#[Layout('components.admin.layout')]
class UserRoleAssign extends Component
{
    public User $user;
    
    #[Validate('required|exists:system_roles,id')]
    public $role_id = '';
    
    #[Validate('nullable|date')]
    public $assigned_from = null;
    
    #[Validate('nullable|date|after:assigned_from')]
    public $assigned_until = null;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->assigned_from = now()->toDateString();
    }

    public function getAvailableRolesProperty()
    {
        $currentUserLevel = session('active_role_level', 999);
        
        return SystemRole::where('entity_id', Auth::user()->entity_id)
            ->where('hierarchy_level', '>', $currentUserLevel) // Can only assign lower privilege roles
            ->operational()
            ->orderBy('hierarchy_level')
            ->get();
    }

    public function getCurrentAssignmentsProperty()
    {
        return UserRoleAssignment::with('role')
            ->where('user_id', $this->user->id)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->get();
    }

    public function assignRole()
    {
        $this->validate();

        // Check if user already has this role
        $existingAssignment = UserRoleAssignment::where('user_id', $this->user->id)
            ->where('role_id', $this->role_id)
            ->current()
            ->first();

        if ($existingAssignment) {
            $this->addError('role_id', __('User already has this role assigned.'));
            return;
        }

        UserRoleAssignment::create([
            'user_id' => $this->user->id,
            'role_id' => $this->role_id,
            'assigned_from' => $this->assigned_from,
            'assigned_until' => $this->assigned_until,
            'is_active' => false, // Requires approval
            'approval_status' => 'pending',
            'assigned_by' => Auth::id(),
        ]);

        session()->flash('success', __('Role assigned successfully and is pending approval.'));
        
        // Reset form
        $this->reset(['role_id', 'assigned_until']);
        $this->assigned_from = now()->toDateString();
    }

    public function revokeAssignment($assignmentId)
    {
        $assignment = UserRoleAssignment::findOrFail($assignmentId);
        
        // Check authorization
        if ($assignment->user->entity_id !== Auth::user()->entity_id) {
            abort(403, 'Unauthorized action.');
        }

        $assignment->update([
            'assigned_until' => now(),
            'is_active' => false,
            'revoked_by' => Auth::id(),
            'revoked_at' => now(),
        ]);

        session()->flash('success', __('Role assignment revoked successfully.'));
    }

    public function render()
    {
        return view('livewire.admin.rbac.users.user-role-assign', [
            'availableRoles' => $this->availableRoles,
            'currentAssignments' => $this->currentAssignments,
            'user' => $this->user
        ])->with([
            'heading' => __('Manage Roles: :name', ['name' => $this->user->name]),
            'subheading' => __('Assign and manage user role assignments'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.users.index')],
                ['label' => __('User Assignments'), 'url' => route('admin.rbac.users.index')],
                ['label' => __('Assign Roles'), 'url' => route('admin.rbac.users.assign', $this->user)]
            ]
        ]);
    }
}
