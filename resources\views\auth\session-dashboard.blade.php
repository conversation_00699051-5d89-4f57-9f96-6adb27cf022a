<x-layouts.app :title="__('Session Management')">
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Session Management') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- History Dashboard Link -->
            <div class="mb-6">
                <a href="{{ route('sessions.history-dashboard') }}" class="inline-flex items-center px-4 py-2 bg-gray-700 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-800 focus:bg-gray-800 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                    {{ __('View History Dashboard') }}
                </a>
            </div>

            <!-- Session Status -->
            @if (session('status'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('status') }}</span>
                </div>
            @endif

            @if (session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            @if (session('transfer_code'))
                <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <span class="font-bold">Transfer Code:</span>
                            <span class="block sm:inline text-lg font-mono bg-blue-50 p-1 rounded">{{ session('transfer_code') }}</span>
                            <div class="mt-2 text-sm">
                                <p>Use this code on another device to transfer your session.</p>
                                <p class="mt-1"><span class="font-bold">Transfer URL:</span> 
                                    <span class="font-mono">{{ route('sessions.transfer') }}</span>
                                </p>
                                <p class="mt-3">
                                    <a href="{{ route('sessions.transfer') }}" class="inline-flex items-center px-3 py-1 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        Open Transfer Page
                                    </a>
                                </p>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <div class="bg-white p-2 rounded">
                                <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data={{ urlencode(route('sessions.transfer') . '?code=' . session('transfer_code')) }}" 
                                     alt="QR Code for Session Transfer" 
                                     class="w-32 h-32" />
                            </div>
                            <p class="text-xs text-center mt-1">Scan with your device</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Session Statistics -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Session Statistics') }}</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ __('Active Sessions') }}</div>
                            <div class="text-2xl font-bold">{{ $stats['total_active'] }}</div>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ __('Recent Sessions (30 days)') }}</div>
                            <div class="text-2xl font-bold">{{ $stats['total_recent'] }}</div>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ __('Suspicious Sessions') }}</div>
                            <div class="text-2xl font-bold">{{ $stats['total_suspicious'] }}</div>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ __('Average Session Duration') }}</div>
                            <div class="text-2xl font-bold">{{ $stats['avg_session_duration'] ? gmdate('H:i:s', $stats['avg_session_duration']) : 'N/A' }}</div>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ __('Most Used Device') }}</div>
                            <div class="text-2xl font-bold">{{ $stats['most_used_device'] ?? 'N/A' }}</div>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="text-sm text-gray-500 dark:text-gray-400">{{ __('Most Used Browser') }}</div>
                            <div class="text-2xl font-bold">{{ $stats['most_used_browser'] ?? 'N/A' }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Session Transfer -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Session Transfer') }}</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Create Transfer -->
                        <div>
                            <h4 class="text-md font-medium mb-2">{{ __('Transfer Current Session') }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                {{ __('Generate a code to transfer your current session to another device.') }}
                            </p>
                            <form action="{{ route('sessions.create-transfer') }}" method="POST">
                                @csrf
                                <input type="hidden" name="session_id" value="{{ $currentSessionId }}">
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150" @if(session('is_transferred_session')) disabled style="opacity:0.5;cursor:not-allowed;" @endif>
                                    {{ __('Generate Transfer Code') }}
                                </button>
                            </form>
                            @if(session('transfer_code'))
                                <div class="mt-2 flex items-center" x-data="{ copied: false }">
                                    <span class="font-mono text-lg bg-blue-50 p-1 rounded">{{ session('transfer_code') }}</span>
                                    <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" @click="navigator.clipboard.writeText('{{ session('transfer_code') }}'); copied = true; setTimeout(() => copied = false, 1500)">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16h8M8 12h8m-7 8h6a2 2 0 002-2V6a2 2 0 00-2-2H8a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                                    </button>
                                    <span class="ml-2 text-green-600 text-xs" x-show="copied" x-transition>{{ __('Copied!') }}</span>
                                </div>
                            @endif
                        </div>
                        <!-- Magic Link Feature -->
                        <div>
                            <h4 class="text-md font-medium mb-2">{{ __('Magic Link Login') }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                {{ __('Generate a one-time magic login link to your email. The link will expire after the specified time or after first use.') }}
                            </p>
                            <form action="{{ route('sessions.magic-link.generate') }}" method="POST" x-data="{ show: false }" @submit="show = true">
                                @csrf
                                <div class="mb-2">
                                    <label for="validity_minutes" class="block text-xs font-medium text-gray-700 dark:text-gray-300">{{ __('Validity (minutes)') }}</label>
                                    <input type="number" min="1" max="1440" name="validity_minutes" id="validity_minutes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 sm:text-sm" required>
                                </div>
                                <div class="mb-2">
                                    <label for="email" class="block text-xs font-medium text-gray-700 dark:text-gray-300">{{ __('Email Address') }}</label>
                                    <input type="email" name="email" id="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 sm:text-sm" required>
                                </div>
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    {{ __('Generate Magic Link') }}
                                </button>
                            </form>
                            @if(session('magic_link_url'))
                                <div class="mt-2 flex items-center" x-data="{ copied: false }">
                                    <input type="text" readonly class="font-mono text-xs bg-green-50 p-1 rounded w-full" value="{{ session('magic_link_url') }}">
                                    <button type="button" class="ml-2 text-green-600 hover:text-green-800" @click="navigator.clipboard.writeText('{{ session('magic_link_url') }}'); copied = true; setTimeout(() => copied = false, 1500)">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16h8M8 12h8m-7 8h6a2 2 0 002-2V6a2 2 0 00-2-2H8a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                                    </button>
                                    <span class="ml-2 text-green-600 text-xs" x-show="copied" x-transition>{{ __('Copied!') }}</span>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">{{ __('This link will expire at') }} {{ session('magic_link_expires_at') }}</div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Sessions -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Active Sessions') }}</h3>
                    
                    @if($activeSessions->isEmpty())
                        <p class="text-gray-500 dark:text-gray-400">{{ __('No active sessions found.') }}</p>
                    @else
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Device') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Location') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Started') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Label') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Status') }}</th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    @foreach($activeSessions as $session)
                                        <tr class="{{ $session->session_id === $currentSessionId ? 'bg-blue-50 dark:bg-blue-900/20' : '' }}">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div class="font-medium">{{ $session->device_type ?? 'Unknown' }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->browser }} on {{ $session->platform }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div>{{ $session->getLocationString() }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->ip_address }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div>{{ $session->login_at->format('M d, Y') }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->login_at->format('H:i') }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <form action="{{ route('sessions.update-label') }}" method="POST" class="flex items-center space-x-2">
                                                    @csrf
                                                    <input type="hidden" name="session_id" value="{{ $session->session_id }}">
                                                    <input type="text" name="session_label" value="{{ $session->session_label }}" class="rounded-md shadow-sm border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 block w-full sm:text-sm">
                                                    <button type="submit" class="inline-flex items-center px-2 py-1 bg-gray-200 dark:bg-gray-700 border border-transparent rounded-md font-semibold text-xs uppercase tracking-widest hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                                        {{ __('Save') }}
                                                    </button>
                                                </form>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                @if($session->session_id === $currentSessionId)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                        {{ __('Current') }}
                                                    </span>
                                                @endif
                                                
                                                @if($session->is_cross_entity_access)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                        {{ __('Cross-Entity') }}
                                                    </span>
                                                @endif
                                                
                                                @if($session->fingerprint_changed)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                        {{ __('Suspicious') }}
                                                    </span>
                                                @endif
                                                
                                                @if($session->transferred_from)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                        {{ __('Transferred From') }}
                                                    </span>
                                                @endif
                                                @if($session->transferred_to)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                        {{ __('Transferred To') }}
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <form action="{{ route('sessions.terminate') }}" method="POST" class="inline-block">
                                                    @csrf
                                                    <input type="hidden" name="session_id" value="{{ $session->session_id }}">
                                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                                        {{ __('Terminate') }}
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Sessions -->
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Recent Sessions') }}</h3>
                    
                    @if($recentSessions->isEmpty())
                        <p class="text-gray-500 dark:text-gray-400">{{ __('No recent sessions found.') }}</p>
                    @else
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Device') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Location') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Started') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Duration') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Ended') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    @foreach($recentSessions as $session)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div class="font-medium">{{ $session->device_type ?? 'Unknown' }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->browser }} on {{ $session->platform }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->session_label }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div>{{ $session->getLocationString() }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->ip_address }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div>{{ $session->login_at->format('M d, Y') }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->login_at->format('H:i') }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                {{ $session->session_duration_formatted ?? 'N/A' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div>{{ $session->logout_at ? $session->logout_at->format('M d, Y') : 'N/A' }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->logout_at ? $session->logout_at->format('H:i') : '' }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->logout_reason }}</div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Suspicious Sessions -->
            @if($suspiciousSessions->isNotEmpty())
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-gray-900 dark:text-gray-100">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">{{ __('Suspicious Sessions') }}</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Device') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Location') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Date/Time') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Risk Score') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ __('Issues') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    @foreach($suspiciousSessions as $session)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div class="font-medium">{{ $session->device_type ?? 'Unknown' }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->browser }} on {{ $session->platform }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div>{{ $session->getLocationString() }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->ip_address }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div>{{ $session->login_at->format('M d, Y') }}</div>
                                                <div class="text-gray-500 dark:text-gray-400">{{ $session->login_at->format('H:i') }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <div class="flex items-center">
                                                    <div class="w-16 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                                        <div class="bg-red-600 h-2.5 rounded-full" style="width: {{ $session->risk_score ?? 0 }}%"></div>
                                                    </div>
                                                    <span>{{ $session->risk_score ?? 0 }}</span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                @if($session->fingerprint_changed)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                        {{ __('Fingerprint Changed') }}
                                                    </span>
                                                @endif
                                                
                                                @if($session->suspicious_location)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                        {{ __('Unusual Location') }}
                                                    </span>
                                                @endif
                                                
                                                @if($session->unusual_timing)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                                        {{ __('Unusual Time') }}
                                                    </span>
                                                @endif
                                                
                                                @if($session->is_proxy)
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                        {{ __('Proxy/VPN') }}
                                                    </span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-layouts.app> 