@props(['role', 'showLevel' => true])

<div class="flex items-center gap-2">
    <flux:badge variant="outline">
        {{ $role->role_name }}
    </flux:badge>
    
    @if($showLevel)
        <x-admin.hierarchy-indicator :level="$role->hierarchy_level" />
    @endif
    
    @if(!$role->isOperational())
        <flux:badge variant="solid" color="danger" size="sm">{{ __('Inactive') }}</flux:badge>
    @endif
</div>
