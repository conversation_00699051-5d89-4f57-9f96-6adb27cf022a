<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Auth\BannedAttempt;

class LoginRateLimiter
{
    /**
     * Handle an incoming request.
     * Enhanced rate limiting for login attempts with IP tracking.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $key = 'login:' . $request->ip();
        
        // Check if this IP is banned
        $banned = BannedAttempt::where('ip_address', $request->ip())
            ->where(function($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->exists();
        
        if ($banned) {
            return response()->view('livewire.auth.banned', [
                'ip' => $request->ip(),
                'message' => 'Access blocked due to suspicious activity. Please contact support.',
            ], 403);
        }
        
        // Check if too many attempts
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            
            // If they're exceeding limits severely, create a ban record
            if (RateLimiter::attempts($key) > 10) {
                // Check if this IP has been banned before
                $existingBan = BannedAttempt::where('ip_address', $request->ip())->first();
                
                $banCount = 1;
                if ($existingBan) {
                    // Increment ban count if IP has been banned before
                    $banCount = $existingBan->ban_count + 1;
                }
                
                // Calculate ban duration based on ban count
                $banHours = min(24 * $banCount, 168); // Max 7 days (168 hours)
                
                BannedAttempt::create([
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'email_attempted' => $request->input('email', ''),
                    'reason' => 'Exceeded rate limit severely',
                    'banned_at' => now(),
                    'expires_at' => now()->addHours($banHours),
                    'ban_count' => $banCount,
                ]);
                
                return response()->view('livewire.auth.banned', [
                    'ip' => $request->ip(),
                    'message' => 'Access blocked due to suspicious activity. Please contact support.',
                ], 403);
            }
            
            // Regular rate limit message
            return response()->json([
                'message' => 'Too many login attempts. Please try again in ' . ceil($seconds / 60) . ' minutes.',
            ], 429);
        }
        
        // Track login attempt for non-GET requests to login route
        if ($request->isMethod('post') && $request->routeIs('login')) {
            RateLimiter::hit($key, 60 * 5); // Block for 5 minutes per attempt
        }
        
        return $next($request);
    }
} 