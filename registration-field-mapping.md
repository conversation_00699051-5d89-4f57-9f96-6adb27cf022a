# Dealer/Distributor Registration Module - Livewire Implementation Blueprint

## Project Overview

This document provides a comprehensive blueprint for implementing a multi-stage registration system for dealers and distributors using **Laravel 12.0**, **Livewire 3** (with Flux and Volt), and **Tailwind CSS 4.0**. The system creates a complete entity structure with RBAC (Role-Based Access Control) setup, requiring admin approval before activation.

### Technology Stack
- **Backend:** Laravel 12.0
- **Frontend:** Livewire 3 with Flux UI Components and Volt
- **Styling:** Tailwind CSS 4.0
- **Database:** MySQL/PostgreSQL
- **File Storage:** Laravel Local Storage
- **Email:** Laravel Mail

### Key Principles
- All records created with `is_active = false` and `approval_status = 'pending'`
- First contact/address is always marked as primary
- Maximum 4 contacts/addresses allowed per entity
- File uploads: PDF/JPG only, 5MB max, stored locally
- Email verification required with 30-minute OTP validity
- Web-based only (no API endpoints for external applications)

---

## File Structure

```
app/
├── Livewire/
│   ├── Registration/
│   │   ├── RegistrationWizard.php          # Main wizard component
│   │   ├── Steps/
│   │   │   ├── EmailVerification.php       # Stage 1
│   │   │   ├── EntityTypeSelection.php     # Stage 2
│   │   │   ├── BusinessInformation.php     # Stage 3
│   │   │   ├── PersonalDetails.php         # Stage 4
│   │   │   ├── TaxInformation.php          # Stage 5
│   │   │   ├── BusinessKycDocuments.php    # Stage 6
│   │   │   ├── AdditionalDocuments.php     # Stage 7
│   │   │   ├── PersonalKycDocuments.php    # Stage 8
│   │   │   └── ReviewSubmit.php            # Stage 9
│   │   └── Traits/
│   │       ├── HandlesFileUploads.php
│   │       └── HandlesValidation.php
│   └── Common/
│       ├── StateCity.php                    # State/City selector component
│       └── FileUpload.php                   # Reusable file upload component
├── Services/
│   ├── RegistrationService.php
│   ├── CodeGeneratorService.php
│   └── EmailService.php
├── Models/
│   └── (All existing models)
└── Mail/
    ├── OtpMail.php
    ├── RegistrationCompleteMail.php
    └── DistributorAlertMail.php

resources/
├── views/
│   ├── livewire/
│   │   ├── registration/
│   │   │   ├── registration-wizard.blade.php
│   │   │   └── steps/
│   │   │       ├── email-verification.blade.php
│   │   │       ├── entity-type-selection.blade.php
│   │   │       ├── business-information.blade.php
│   │   │       ├── personal-details.blade.php
│   │   │       ├── tax-information.blade.php
│   │   │       ├── business-kyc-documents.blade.php
│   │   │       ├── additional-documents.blade.php
│   │   │       ├── personal-kyc-documents.blade.php
│   │   │       └── review-submit.blade.php
│   │   └── common/
│   │       ├── state-city.blade.php
│   │       └── file-upload.blade.php
│   └── emails/
│       ├── otp.blade.php
│       ├── registration-complete.blade.php
│       └── distributor-alert.blade.php
└── css/
    └── app.css                              # Tailwind imports

routes/
└── web.php                                  # Registration routes

database/
├── migrations/
│   └── (All existing migrations)
└── seeders/
    ├── IndianStatesSeeder.php
    ├── CitiesSeeder.php
    └── DocumentTypesSeeder.php
```

---

## Stage 1: Email Verification

### Livewire Component: `EmailVerification.php`

```php
<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\User;
use App\Models\RegistrationAttempt;
use App\Mail\OtpMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class EmailVerification extends Component
{
    public $email = '';
    public $otp = '';
    public $showOtpField = false;
    public $otpSent = false;
    public $attemptId = null;
    
    protected $rules = [
        'email' => 'required|email|max:255',
        'otp' => 'required_if:showOtpField,true|digits:6'
    ];
    
    protected $messages = [
        'email.required' => 'Email address is required',
        'email.email' => 'Please provide a valid email address',
        'otp.required_if' => 'OTP is required',
        'otp.digits' => 'OTP must be 6 digits'
    ];
    
    public function sendOtp()
    {
        $this->validate(['email' => 'required|email|max:255']);
        
        // Check if email already registered
        if (User::where('email', $this->email)->exists()) {
            $this->addError('email', 'This email is already registered.');
            return;
        }
        
        // Generate OTP
        $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
        
        // Create or update registration attempt
        $attempt = RegistrationAttempt::updateOrCreate(
            ['email' => $this->email, 'is_submitted' => false],
            [
                'current_stage' => 'email_verification',
                'stages_completed' => json_encode([]),
                'stages_data' => json_encode(['email' => $this->email]),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'verification_token' => $otp,
                'verification_sent_at' => now(),
                'expires_at' => now()->addHours(24),
                'resume_token' => Str::random(32)
            ]
        );
        
        $this->attemptId = $attempt->id;
        
        // Send OTP email
        Mail::to($this->email)->send(new OtpMail($otp));
        
        $this->showOtpField = true;
        $this->otpSent = true;
        
        session()->flash('message', 'OTP sent to your email. Please check your inbox.');
    }
    
    public function verifyOtp()
    {
        $this->validate();
        
        $attempt = RegistrationAttempt::find($this->attemptId);
        
        // Check if OTP is expired (30 minutes)
        if ($attempt->verification_sent_at->addMinutes(30)->isPast()) {
            $this->addError('otp', 'OTP has expired. Please request a new one.');
            return;
        }
        
        // Verify OTP
        if ($attempt->verification_token !== $this->otp) {
            $this->addError('otp', 'Invalid OTP. Please try again.');
            return;
        }
        
        // Mark email as verified
        $attempt->update([
            'is_email_verified' => true,
            'current_stage' => 'entity_type_selection',
            'stages_completed' => json_encode(['email_verification'])
        ]);
        
        // Store attempt ID in session for next steps
        session(['registration_attempt_id' => $attempt->id]);
        
        // Emit event to parent wizard
        $this->dispatch('stepCompleted', 'email_verification');
    }
    
    public function resendOtp()
    {
        if (!$this->attemptId) return;
        
        $attempt = RegistrationAttempt::find($this->attemptId);
        
        // Check if we can resend (minimum 1 minute gap)
        if ($attempt->verification_sent_at->addMinute()->isFuture()) {
            session()->flash('error', 'Please wait before requesting another OTP.');
            return;
        }
        
        // Generate new OTP
        $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
        $attempt->update([
            'verification_token' => $otp,
            'verification_sent_at' => now()
        ]);
        
        // Send OTP email
        Mail::to($this->email)->send(new OtpMail($otp));
        
        session()->flash('message', 'New OTP sent successfully.');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.email-verification');
    }
}
```

### Blade View: `email-verification.blade.php`

```blade
<div>
    <div class="max-w-md mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Email Verification</h3>
        
        @if (session()->has('message'))
            <flux:alert variant="success" class="mb-4">
                {{ session('message') }}
            </flux:alert>
        @endif
        
        @if (session()->has('error'))
            <flux:alert variant="danger" class="mb-4">
                {{ session('error') }}
            </flux:alert>
        @endif
        
        <form wire:submit.prevent="{{ $showOtpField ? 'verifyOtp' : 'sendOtp' }}">
            <flux:input 
                wire:model="email" 
                label="Email Address" 
                type="email" 
                placeholder="Enter your email address"
                :disabled="$showOtpField"
                error="{{ $errors->first('email') }}"
                class="mb-4"
            />
            
            @if($showOtpField)
                <flux:input 
                    wire:model="otp" 
                    label="Enter OTP" 
                    type="text" 
                    placeholder="000000"
                    maxlength="6"
                    class="text-center text-2xl tracking-wider mb-4"
                    error="{{ $errors->first('otp') }}"
                />
                
                <div class="flex items-center justify-between mb-4">
                    <span class="text-sm text-gray-600">Didn't receive OTP?</span>
                    <flux:button 
                        wire:click="resendOtp" 
                        variant="ghost" 
                        size="sm"
                        wire:loading.attr="disabled"
                    >
                        Resend OTP
                    </flux:button>
                </div>
            @endif
            
            <flux:button 
                type="submit" 
                variant="primary" 
                class="w-full"
                wire:loading.attr="disabled"
            >
                <flux:icon.loading wire:loading wire:target="{{ $showOtpField ? 'verifyOtp' : 'sendOtp' }}" />
                {{ $showOtpField ? 'Verify OTP' : 'Send OTP' }}
            </flux:button>
        </form>
        
        @if($otpSent)
            <p class="mt-4 text-sm text-gray-600 text-center">
                OTP is valid for 30 minutes
            </p>
        @endif
    </div>
</div>
```

---

## Stage 2: Entity Type Selection

### Livewire Component: `EntityTypeSelection.php`

```php
<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\Entity;
use App\Models\RegistrationAttempt;
use App\Models\EntityRelationship;

class EntityTypeSelection extends Component
{
    public $entityType = '';
    public $distributorId = '';
    public $showDistributorField = false;
    public $attempt;
    
    protected function rules()
    {
        $rules = [
            'entityType' => 'required|in:dealer,distributor'
        ];
        
        if ($this->entityType === 'dealer' && $this->distributorId) {
            $rules['distributorId'] = [
                'required',
                'regex:/^DIS-[A-Z]{2}-\d{4}$/',
                function ($attribute, $value, $fail) {
                    $distributor = Entity::where('entity_id', $value)
                        ->where('entity_type', 'distributor')
                        ->where('is_active', true)
                        ->first();
                    
                    if (!$distributor) {
                        $fail('Invalid distributor ID or distributor is inactive. Please contact your distributor.');
                    }
                }
            ];
        }
        
        return $rules;
    }
    
    protected $messages = [
        'entityType.required' => 'Please select entity type',
        'entityType.in' => 'Entity type must be either distributor or dealer',
        'distributorId.regex' => 'Invalid distributor ID format'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        // Load saved data if exists
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        if (isset($stagesData['entity_type_selection'])) {
            $this->entityType = $stagesData['entity_type_selection']['entity_type'] ?? '';
            $this->distributorId = $stagesData['entity_type_selection']['distributor_id'] ?? '';
            $this->showDistributorField = $this->entityType === 'dealer';
        }
    }
    
    public function updatedEntityType($value)
    {
        $this->showDistributorField = $value === 'dealer';
        if ($value !== 'dealer') {
            $this->distributorId = '';
        }
    }
    
    public function saveAndContinue()
    {
        $this->validate();
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['entity_type_selection'] = [
            'entity_type' => $this->entityType,
            'distributor_id' => $this->distributorId ?: null
        ];
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('entity_type_selection', $stagesCompleted)) {
            $stagesCompleted[] = 'entity_type_selection';
        }
        
        $this->attempt->update([
            'entity_type' => $this->entityType,
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'business_information'
        ]);
        
        $this->dispatch('stepCompleted', 'entity_type_selection');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.entity-type-selection');
    }
}
```

### Blade View: `entity-type-selection.blade.php`

```blade
<div>
    <div class="max-w-md mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Select Entity Type</h3>
        
        <form wire:submit.prevent="saveAndContinue">
            <div class="space-y-4 mb-6">
                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 {{ $entityType === 'distributor' ? 'border-blue-500 bg-blue-50' : 'border-gray-300' }}">
                    <flux:radio 
                        wire:model.live="entityType" 
                        value="distributor" 
                        class="mr-3"
                    />
                    <div>
                        <div class="font-medium">Distributor</div>
                        <div class="text-sm text-gray-600">Register as a distributor</div>
                    </div>
                </label>
                
                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 {{ $entityType === 'dealer' ? 'border-blue-500 bg-blue-50' : 'border-gray-300' }}">
                    <flux:radio 
                        wire:model.live="entityType" 
                        value="dealer" 
                        class="mr-3"
                    />
                    <div>
                        <div class="font-medium">Dealer</div>
                        <div class="text-sm text-gray-600">Register as a dealer</div>
                    </div>
                </label>
            </div>
            
            @error('entityType')
                <flux:alert variant="danger" class="mb-4">{{ $message }}</flux:alert>
            @enderror
            
            @if($showDistributorField)
                <flux:input 
                    wire:model="distributorId" 
                    label="Distributor ID (Optional)" 
                    type="text" 
                    placeholder="DIS-XX-XXXX"
                    error="{{ $errors->first('distributorId') }}"
                    class="mb-4"
                />
                
                <flux:alert variant="info" class="mb-4">
                    <flux:icon.information-circle class="w-4 h-4" />
                    If you have a distributor ID, enter it to link your account. Leave blank if not applicable.
                </flux:alert>
            @endif
            
            <flux:button 
                type="submit" 
                variant="primary" 
                class="w-full"
                wire:loading.attr="disabled"
                :disabled="!$entityType"
            >
                Continue
                <flux:icon.arrow-right class="ml-2 w-4 h-4" />
            </flux:button>
        </form>
    </div>
</div>
```

---

## Stage 3: Business Information

### Livewire Component: `BusinessInformation.php`

```php
<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\RegistrationAttempt;

class BusinessInformation extends Component
{
    public $entityName = '';
    public $contacts = [];
    public $addresses = [];
    public $attempt;
    
    // Reference data
    public $states = [];
    public $citiesData = [];
    
    protected function rules()
    {
        $rules = [
            'entityName' => 'required|string|max:100',
            'contacts' => 'required|array|min:1|max:4',
            'contacts.*.name' => 'required|string|max:255',
            'contacts.*.email' => 'nullable|email|max:255',
            'contacts.*.phone' => 'required|regex:/^[6-9]\d{9}$/',
            'addresses' => 'required|array|min:1|max:4',
            'addresses.*.address_line1' => 'required|string|max:255',
            'addresses.*.address_line2' => 'nullable|string|max:255',
            'addresses.*.state' => 'required|string|size:2',
            'addresses.*.city' => 'required|string|max:100',
            'addresses.*.postal_code' => 'required|regex:/^[1-9][0-9]{5}$/'
        ];
        
        return $rules;
    }
    
    protected $messages = [
        'entityName.required' => 'Business name is required',
        'contacts.required' => 'At least one contact is required',
        'contacts.min' => 'At least one contact is required',
        'contacts.max' => 'Maximum 4 contacts allowed',
        'contacts.*.name.required' => 'Contact name is required',
        'contacts.*.phone.required' => 'Contact phone is required',
        'contacts.*.phone.regex' => 'Please enter a valid Indian mobile number',
        'addresses.required' => 'At least one address is required',
        'addresses.min' => 'At least one address is required',
        'addresses.max' => 'Maximum 4 addresses allowed',
        'addresses.*.address_line1.required' => 'Address line 1 is required',
        'addresses.*.state.required' => 'State is required',
        'addresses.*.city.required' => 'City is required',
        'addresses.*.postal_code.required' => 'Postal code is required',
        'addresses.*.postal_code.regex' => 'Please enter a valid 6-digit postal code'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        // Load Indian states
        $this->loadStates();
        
        // Load saved data if exists
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        if (isset($stagesData['business_information'])) {
            $data = $stagesData['business_information'];
            $this->entityName = $data['entity_name'] ?? '';
            $this->contacts = $data['contacts'] ?? [['name' => '', 'email' => '', 'phone' => '']];
            $this->addresses = $data['addresses'] ?? [['address_line1' => '', 'address_line2' => '', 'state' => '', 'city' => '', 'postal_code' => '']];
            
            // Load cities for saved addresses
            foreach ($this->addresses as $index => $address) {
                if ($address['state']) {
                    $this->loadCities($address['state'], $index);
                }
            }
        } else {
            // Initialize with one empty contact and address
            $this->contacts = [['name' => '', 'email' => '', 'phone' => '']];
            $this->addresses = [['address_line1' => '', 'address_line2' => '', 'state' => '', 'city' => '', 'postal_code' => '']];
        }
    }
    
    public function loadStates()
    {
        $this->states = [
            ['code' => 'AN', 'name' => 'Andaman and Nicobar Islands'],
            ['code' => 'AP', 'name' => 'Andhra Pradesh'],
            ['code' => 'AR', 'name' => 'Arunachal Pradesh'],
            ['code' => 'AS', 'name' => 'Assam'],
            ['code' => 'BR', 'name' => 'Bihar'],
            ['code' => 'CH', 'name' => 'Chandigarh'],
            ['code' => 'CT', 'name' => 'Chhattisgarh'],
            ['code' => 'DN', 'name' => 'Dadra and Nagar Haveli and Daman and Diu'],
            ['code' => 'DL', 'name' => 'Delhi'],
            ['code' => 'GA', 'name' => 'Goa'],
            ['code' => 'GJ', 'name' => 'Gujarat'],
            ['code' => 'HR', 'name' => 'Haryana'],
            ['code' => 'HP', 'name' => 'Himachal Pradesh'],
            ['code' => 'JK', 'name' => 'Jammu and Kashmir'],
            ['code' => 'JH', 'name' => 'Jharkhand'],
            ['code' => 'KA', 'name' => 'Karnataka'],
            ['code' => 'KL', 'name' => 'Kerala'],
            ['code' => 'LA', 'name' => 'Ladakh'],
            ['code' => 'LD', 'name' => 'Lakshadweep'],
            ['code' => 'MP', 'name' => 'Madhya Pradesh'],
            ['code' => 'MH', 'name' => 'Maharashtra'],
            ['code' => 'MN', 'name' => 'Manipur'],
            ['code' => 'ML', 'name' => 'Meghalaya'],
            ['code' => 'MZ', 'name' => 'Mizoram'],
            ['code' => 'NL', 'name' => 'Nagaland'],
            ['code' => 'OR', 'name' => 'Odisha'],
            ['code' => 'PY', 'name' => 'Puducherry'],
            ['code' => 'PB', 'name' => 'Punjab'],
            ['code' => 'RJ', 'name' => 'Rajasthan'],
            ['code' => 'SK', 'name' => 'Sikkim'],
            ['code' => 'TN', 'name' => 'Tamil Nadu'],
            ['code' => 'TG', 'name' => 'Telangana'],
            ['code' => 'TR', 'name' => 'Tripura'],
            ['code' => 'UP', 'name' => 'Uttar Pradesh'],
            ['code' => 'UT', 'name' => 'Uttarakhand'],
            ['code' => 'WB', 'name' => 'West Bengal']
        ];
    }
    
    public function loadCities($stateCode, $addressIndex)
    {
        // In production, this would fetch from database
        // For now, simplified example
        $cities = [
            'MH' => ['Mumbai', 'Pune', 'Nagpur', 'Thane', 'Nashik', 'Aurangabad'],
            'KA' => ['Bengaluru', 'Mysuru', 'Mangaluru', 'Hubballi', 'Belagavi'],
            'DL' => ['New Delhi', 'Central Delhi', 'East Delhi', 'North Delhi', 'South Delhi', 'West Delhi'],
            // Add more states
        ];
        
        $this->citiesData[$addressIndex] = $cities[$stateCode] ?? [];
    }
    
    public function updatedAddresses($value, $key)
    {
        // Extract index and field from the key (e.g., "0.state")
        if (preg_match('/^(\d+)\.state$/', $key, $matches)) {
            $index = $matches[1];
            $this->loadCities($value, $index);
            // Reset city when state changes
            $this->addresses[$index]['city'] = '';
        }
    }
    
    public function addContact()
    {
        if (count($this->contacts) < 4) {
            $this->contacts[] = ['name' => '', 'email' => '', 'phone' => ''];
        }
    }
    
    public function removeContact($index)
    {
        if (count($this->contacts) > 1) {
            array_splice($this->contacts, $index, 1);
            $this->contacts = array_values($this->contacts);
        }
    }
    
    public function addAddress()
    {
        if (count($this->addresses) < 4) {
            $this->addresses[] = ['address_line1' => '', 'address_line2' => '', 'state' => '', 'city' => '', 'postal_code' => ''];
        }
    }
    
    public function removeAddress($index)
    {
        if (count($this->addresses) > 1) {
            array_splice($this->addresses, $index, 1);
            $this->addresses = array_values($this->addresses);
            
            // Clean up cities data
            unset($this->citiesData[$index]);
            $this->citiesData = array_values($this->citiesData);
        }
    }
    
    public function saveAndContinue()
    {
        $this->validate();
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['business_information'] = [
            'entity_name' => $this->entityName,
            'contacts' => $this->contacts,
            'addresses' => $this->addresses
        ];
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('business_information', $stagesCompleted)) {
            $stagesCompleted[] = 'business_information';
        }
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'personal_details'
        ]);
        
        $this->dispatch('stepCompleted', 'business_information');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.business-information');
    }
}
```

### Blade View: `business-information.blade.php`

```blade
<div>
    <div class="max-w-2xl mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Business Information</h3>
        
        <form wire:submit.prevent="saveAndContinue">
            {{-- Business Name --}}
            <flux:input 
                wire:model="entityName" 
                label="Business Name" 
                placeholder="Enter your business legal name"
                error="{{ $errors->first('entityName') }}"
                class="mb-6"
            />
            
            {{-- Contacts Section --}}
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-medium">Business Contacts</h4>
                    @if(count($contacts) < 4)
                        <flux:button 
                            type="button"
                            wire:click="addContact" 
                            variant="secondary" 
                            size="sm"
                        >
                            <flux:icon.plus class="w-4 h-4 mr-1" />
                            Add Contact
                        </flux:button>
                    @endif
                </div>
                
                @foreach($contacts as $index => $contact)
                    <div class="border rounded-lg p-4 mb-4 {{ $index === 0 ? 'border-blue-300 bg-blue-50' : 'border-gray-300' }}">
                        @if($index === 0)
                            <flux:badge variant="primary" class="mb-2">Primary Contact</flux:badge>
                        @endif
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <flux:input 
                                wire:model="contacts.{{ $index }}.name" 
                                label="Contact Name" 
                                placeholder="Full name"
                                error="{{ $errors->first('contacts.'.$index.'.name') }}"
                            />
                            
                            <flux:input 
                                wire:model="contacts.{{ $index }}.email" 
                                label="Email (Optional)" 
                                type="email"
                                placeholder="<EMAIL>"
                                error="{{ $errors->first('contacts.'.$index.'.email') }}"
                            />
                            
                            <flux:input 
                                wire:model="contacts.{{ $index }}.phone" 
                                label="Phone Number" 
                                type="tel"
                                placeholder="9876543210"
                                maxlength="10"
                                error="{{ $errors->first('contacts.'.$index.'.phone') }}"
                            />
                        </div>
                        
                        @if($index > 0)
                            <flux:button 
                                type="button"
                                wire:click="removeContact({{ $index }})" 
                                variant="ghost" 
                                size="sm"
                                class="mt-2 text-red-600"
                            >
                                <flux:icon.trash class="w-4 h-4 mr-1" />
                                Remove
                            </flux:button>
                        @endif
                    </div>
                @endforeach
                
                @error('contacts')
                    <flux:alert variant="danger">{{ $message }}</flux:alert>
                @enderror
            </div>
            
            {{-- Addresses Section --}}
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-medium">Business Addresses</h4>
                    @if(count($addresses) < 4)
                        <flux:button 
                            type="button"
                            wire:click="addAddress" 
                            variant="secondary" 
                            size="sm"
                        >
                            <flux:icon.plus class="w-4 h-4 mr-1" />
                            Add Address
                        </flux:button>
                    @endif
                </div>
                
                @foreach($addresses as $index => $address)
                    <div class="border rounded-lg p-4 mb-4 {{ $index === 0 ? 'border-blue-300 bg-blue-50' : 'border-gray-300' }}">
                        @if($index === 0)
                            <flux:badge variant="primary" class="mb-2">Primary Address</flux:badge>
                        @endif
                        
                        <div class="space-y-4">
                            <flux:input 
                                wire:model="addresses.{{ $index }}.address_line1" 
                                label="Address Line 1" 
                                placeholder="Street address"
                                error="{{ $errors->first('addresses.'.$index.'.address_line1') }}"
                            />
                            
                            <flux:input 
                                wire:model="addresses.{{ $index }}.address_line2" 
                                label="Address Line 2 (Optional)" 
                                placeholder="Apartment, suite, etc."
                                error="{{ $errors->first('addresses.'.$index.'.address_line2') }}"
                            />
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <flux:select 
                                    wire:model.live="addresses.{{ $index }}.state" 
                                    label="State"
                                    error="{{ $errors->first('addresses.'.$index.'.state') }}"
                                >
                                    <option value="">Select State</option>
                                    @foreach($states as $state)
                                        <option value="{{ $state['code'] }}">{{ $state['name'] }}</option>
                                    @endforeach
                                </flux:select>
                                
                                <flux:select 
                                    wire:model="addresses.{{ $index }}.city" 
                                    label="City"
                                    error="{{ $errors->first('addresses.'.$index.'.city') }}"
                                    :disabled="empty($addresses[$index]['state'])"
                                >
                                    <option value="">Select City</option>
                                    @if(isset($citiesData[$index]))
                                        @foreach($citiesData[$index] as $city)
                                            <option value="{{ $city }}">{{ $city }}</option>
                                        @endforeach
                                    @endif
                                </flux:select>
                                
                                <flux:input 
                                    wire:model="addresses.{{ $index }}.postal_code" 
                                    label="Postal Code" 
                                    placeholder="123456"
                                    maxlength="6"
                                    error="{{ $errors->first('addresses.'.$index.'.postal_code') }}"
                                />
                            </div>
                        </div>
                        
                        @if($index > 0)
                            <flux:button 
                                type="button"
                                wire:click="removeAddress({{ $index }})" 
                                variant="ghost" 
                                size="sm"
                                class="mt-2 text-red-600"
                            >
                                <flux:icon.trash class="w-4 h-4 mr-1" />
                                Remove
                            </flux:button>
                        @endif
                    </div>
                @endforeach
                
                @error('addresses')
                    <flux:alert variant="danger">{{ $message }}</flux:alert>
                @enderror
            </div>
            
            {{-- Navigation --}}
            <div class="flex justify-between">
                <flux:button 
                    type="button"
                    wire:click="$dispatch('stepBack')" 
                    variant="secondary"
                >
                    <flux:icon.arrow-left class="w-4 h-4 mr-2" />
                    Previous
                </flux:button>
                
                <flux:button 
                    type="submit" 
                    variant="primary"
                    wire:loading.attr="disabled"
                >
                    Continue
                    <flux:icon.arrow-right class="w-4 h-4 ml-2" />
                </flux:button>
            </div>
        </form>
    </div>
</div>
```

---

## Stage 4: Personal Details

### Livewire Component: `PersonalDetails.php`

```php
<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\RegistrationAttempt;
use Illuminate\Support\Facades\Hash;

class PersonalDetails extends Component
{
    public $attempt;
    
    // Personal Information
    public $ownerName = '';
    public $ownerEmail = '';
    public $ownerPhone = '';
    public $password = '';
    public $passwordConfirmation = '';
    
    // Personal Contact (pre-filled)
    public $personalContactName = '';
    public $personalContactEmail = '';
    public $personalContactPhone = '';
    
    // Personal Address (pre-filled)
    public $personalAddressLine1 = '';
    public $personalAddressLine2 = '';
    public $personalState = '';
    public $personalCity = '';
    public $personalPostalCode = '';
    
    // Reference data
    public $states = [];
    public $cities = [];
    
    protected function rules()
    {
        return [
            'ownerName' => 'required|string|max:255',
            'ownerEmail' => 'required|email|max:255',
            'ownerPhone' => 'required|regex:/^[6-9]\d{9}$/',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/'
            ],
            'passwordConfirmation' => 'required|same:password',
            'personalContactName' => 'required|string|max:255',
            'personalContactEmail' => 'nullable|email|max:255',
            'personalContactPhone' => 'required|regex:/^[6-9]\d{9}$/',
            'personalAddressLine1' => 'required|string|max:255',
            'personalAddressLine2' => 'nullable|string|max:255',
            'personalState' => 'required|string|size:2',
            'personalCity' => 'required|string|max:100',
            'personalPostalCode' => 'required|regex:/^[1-9][0-9]{5}$/'
        ];
    }
    
    protected $messages = [
        'ownerName.required' => 'Owner name is required',
        'ownerPhone.regex' => 'Please enter a valid Indian mobile number',
        'password.required' => 'Password is required',
        'password.min' => 'Password must be at least 8 characters',
        'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        'passwordConfirmation.same' => 'Password confirmation does not match',
        'personalContactPhone.regex' => 'Please enter a valid Indian mobile number',
        'personalPostalCode.regex' => 'Please enter a valid 6-digit postal code'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        // Load states
        $this->loadStates();
        
        // Pre-fill email from Stage 1
        $this->ownerEmail = $this->attempt->email;
        
        // Load saved data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        
        // Pre-fill from business information
        if (isset($stagesData['business_information'])) {
            $businessData = $stagesData['business_information'];
            
            // Pre-fill contact info from first business contact
            if (isset($businessData['contacts'][0])) {
                $firstContact = $businessData['contacts'][0];
                $this->personalContactName = $firstContact['name'];
                $this->personalContactEmail = $firstContact['email'];
                $this->personalContactPhone = $firstContact['phone'];
            }
            
            // Pre-fill address from first business address
            if (isset($businessData['addresses'][0])) {
                $firstAddress = $businessData['addresses'][0];
                $this->personalAddressLine1 = $firstAddress['address_line1'];
                $this->personalAddressLine2 = $firstAddress['address_line2'];
                $this->personalState = $firstAddress['state'];
                $this->personalCity = $firstAddress['city'];
                $this->personalPostalCode = $firstAddress['postal_code'];
                
                // Load cities for the pre-filled state
                if ($this->personalState) {
                    $this->loadCities($this->personalState);
                }
            }
        }
        
        // Load saved personal details if exists
        if (isset($stagesData['personal_details'])) {
            $personalData = $stagesData['personal_details'];
            $this->ownerName = $personalData['owner_name'] ?? $this->ownerName;
            $this->ownerPhone = $personalData['owner_phone'] ?? $this->ownerPhone;
            $this->personalContactName = $personalData['personal_contact_name'] ?? $this->personalContactName;
            $this->personalContactEmail = $personalData['personal_contact_email'] ?? $this->personalContactEmail;
            $this->personalContactPhone = $personalData['personal_contact_phone'] ?? $this->personalContactPhone;
            $this->personalAddressLine1 = $personalData['personal_address_line1'] ?? $this->personalAddressLine1;
            $this->personalAddressLine2 = $personalData['personal_address_line2'] ?? $this->personalAddressLine2;
            $this->personalState = $personalData['personal_state'] ?? $this->personalState;
            $this->personalCity = $personalData['personal_city'] ?? $this->personalCity;
            $this->personalPostalCode = $personalData['personal_postal_code'] ?? $this->personalPostalCode;
        }
    }
    
    public function loadStates()
    {
        // Same as BusinessInformation component
        $this->states = [
            ['code' => 'AN', 'name' => 'Andaman and Nicobar Islands'],
            // ... (all states)
        ];
    }
    
    public function loadCities($stateCode)
    {
        // In production, fetch from database
        $citiesByState = [
            'MH' => ['Mumbai', 'Pune', 'Nagpur', 'Thane', 'Nashik', 'Aurangabad'],
            'KA' => ['Bengaluru', 'Mysuru', 'Mangaluru', 'Hubballi', 'Belagavi'],
            // ... more states
        ];
        
        $this->cities = $citiesByState[$stateCode] ?? [];
    }
    
    public function updatedPersonalState($value)
    {
        $this->loadCities($value);
        $this->personalCity = '';
    }
    
    public function saveAndContinue()
    {
        $this->validate();
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['personal_details'] = [
            'owner_name' => $this->ownerName,
            'owner_phone' => $this->ownerPhone,
            'password' => Hash::make($this->password), // Hash password for storage
            'personal_contact_name' => $this->personalContactName,
            'personal_contact_email' => $this->personalContactEmail,
            'personal_contact_phone' => $this->personalContactPhone,
            'personal_address_line1' => $this->personalAddressLine1,
            'personal_address_line2' => $this->personalAddressLine2,
            'personal_state' => $this->personalState,
            'personal_city' => $this->personalCity,
            'personal_postal_code' => $this->personalPostalCode
        ];
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('personal_details', $stagesCompleted)) {
            $stagesCompleted[] = 'personal_details';
        }
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'tax_information'
        ]);
        
        $this->dispatch('stepCompleted', 'personal_details');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.personal-details');
    }
}
```

---

## Stage 5-9: Additional Components

Due to length constraints, I'll provide the structure for remaining stages:

### Stage 5: Tax Information (`TaxInformation.php`)
- Tax type selection (GST, PAN, etc.)
- Tax identifier with format validation
- Tax region input

### Stage 6: Business KYC Documents (`BusinessKycDocuments.php`)
- Dynamic document type selection
- File upload with validation
- Document number input

### Stage 7: Additional Documents (`AdditionalDocuments.php`)
- Optional document uploads
- Custom naming and description

### Stage 8: Personal KYC Documents (`PersonalKycDocuments.php`)
- Personal document uploads (Aadhaar, PAN, etc.)
- Format validation for document numbers

### Stage 9: Review & Submit (`ReviewSubmit.php`)
- Display all collected data
- Terms acceptance checkboxes
- Final submission with transaction handling

---

## Main Wizard Component

### `RegistrationWizard.php`

```php
<?php

namespace App\Livewire\Registration;

use Livewire\Component;
use App\Models\RegistrationAttempt;

class RegistrationWizard extends Component
{
    public $currentStep = 1;
    public $totalSteps = 9;
    public $attempt;
    
    public $steps = [
        1 => ['name' => 'Email Verification', 'component' => 'email-verification'],
        2 => ['name' => 'Entity Type', 'component' => 'entity-type-selection'],
        3 => ['name' => 'Business Info', 'component' => 'business-information'],
        4 => ['name' => 'Personal Details', 'component' => 'personal-details'],
        5 => ['name' => 'Tax Info', 'component' => 'tax-information'],
        6 => ['name' => 'Business KYC', 'component' => 'business-kyc-documents'],
        7 => ['name' => 'Additional Docs', 'component' => 'additional-documents'],
        8 => ['name' => 'Personal KYC', 'component' => 'personal-kyc-documents'],
        9 => ['name' => 'Review & Submit', 'component' => 'review-submit']
    ];
    
    protected $listeners = ['stepCompleted', 'stepBack'];
    
    public function mount()
    {
        // Check if resuming registration
        if (session()->has('registration_attempt_id')) {
            $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
            if ($this->attempt) {
                $this->determineCurrentStep();
            }
        }
    }
    
    public function determineCurrentStep()
    {
        $stageToStep = [
            'email_verification' => 1,
            'entity_type_selection' => 2,
            'business_information' => 3,
            'personal_details' => 4,
            'tax_information' => 5,
            'business_kyc_documents' => 6,
            'additional_documents' => 7,
            'personal_kyc_documents' => 8,
            'review_submit' => 9
        ];
        
        $this->currentStep = $stageToStep[$this->attempt->current_stage] ?? 1;
    }
    
    public function stepCompleted($stepName)
    {
        if ($this->currentStep < $this->totalSteps) {
            $this->currentStep++;
        }
    }
    
    public function stepBack()
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }
    
    public function render()
    {
        return view('livewire.registration.registration-wizard');
    }
}
```

### `registration-wizard.blade.php`

```blade
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto">
        {{-- Progress Bar --}}
        <div class="bg-white rounded-lg shadow mb-8 p-6">
            <div class="flex items-center justify-between mb-4">
                @foreach($steps as $step => $info)
                    <div class="flex items-center">
                        <div class="flex items-center justify-center w-10 h-10 rounded-full 
                            {{ $currentStep >= $step ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600' }}">
                            @if($currentStep > $step)
                                <flux:icon.check class="w-5 h-5" />
                            @else
                                {{ $step }}
                            @endif
                        </div>
                        
                        @if($step < $totalSteps)
                            <div class="w-full h-1 mx-2 
                                {{ $currentStep > $step ? 'bg-blue-600' : 'bg-gray-200' }}">
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
            
            <div class="text-center">
                <h2 class="text-2xl font-bold">
                    {{ $attempt && $attempt->entity_type ? ucfirst($attempt->entity_type) : 'Entity' }} Registration
                </h2>
                <p class="text-gray-600 mt-1">
                    Step {{ $currentStep }} of {{ $totalSteps }}: {{ $steps[$currentStep]['name'] }}
                </p>
            </div>
        </div>
        
        {{-- Step Content --}}
        <div class="bg-white rounded-lg shadow p-6">
            @livewire('registration.steps.' . $steps[$currentStep]['component'], key($currentStep))
        </div>
    </div>
</div>
```

---

## Routes Configuration

### `routes/web.php`

```php
<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Registration\RegistrationWizard;

// Public routes
Route::get('/', function () {
    return view('welcome');
});

// Registration routes
Route::prefix('register')->group(function () {
    Route::get('/', RegistrationWizard::class)->name('register');
    
    // Resume registration with token
    Route::get('/resume/{token}', function ($token) {
        $attempt = \App\Models\RegistrationAttempt::where('resume_token', $token)
            ->where('is_submitted', false)
            ->where('expires_at', '>', now())
            ->first();
            
        if ($attempt) {
            session(['registration_attempt_id' => $attempt->id]);
            return redirect()->route('register');
        }
        
        return redirect()->route('register')->with('error', 'Invalid or expired registration link.');
    })->name('register.resume');
});

// Authenticated routes (after registration approval)
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});
```

---

## Email Templates

### `resources/views/emails/otp.blade.php`

```blade
<x-mail::message>
# Email Verification

Your OTP for registration is:

<div style="background: #f3f4f6; padding: 20px; text-align: center; font-size: 32px; font-weight: bold; letter-spacing: 8px; margin: 20px 0;">
{{ $otp }}
</div>

This OTP is valid for **30 minutes**.

If you didn't request this OTP, please ignore this email.

Thanks,<br>
{{ config('app.name') }}
</x-mail::message>
```

---

## Services

### `RegistrationService.php`

```php
<?php

namespace App\Services;

use App\Models\Entity;
use App\Models\User;
use App\Models\Contact;
use App\Models\Address;
use App\Models\Tax;
use App\Models\Kyc;
use App\Models\SystemRole;
use App\Models\OrganizationDepartment;
use App\Models\AccessGuardType;
use App\Models\SystemPermission;
use App\Models\EntityRelationship;
use App\Mail\RegistrationCompleteMail;
use App\Mail\DistributorAlertMail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class RegistrationService
{
    protected $codeGenerator;
    
    public function __construct(CodeGeneratorService $codeGenerator)
    {
        $this->codeGenerator = $codeGenerator;
    }
    
    public function completeRegistration($attemptId)
    {
        $attempt = \App\Models\RegistrationAttempt::findOrFail($attemptId);
        $stagesData = json_decode($attempt->stages_data, true);
        
        return DB::transaction(function () use ($attempt, $stagesData) {
            // Extract all data
            $entityTypeData = $stagesData['entity_type_selection'];
            $businessData = $stagesData['business_information'];
            $personalData = $stagesData['personal_details'];
            $taxData = $stagesData['tax_information'];
            $businessKycData = $stagesData['business_kyc_documents'] ?? [];
            $personalKycData = $stagesData['personal_kyc_documents'] ?? [];
            
            // Get state from primary address for entity_id generation
            $primaryState = $businessData['addresses'][0]['state'];
            
            // 1. Create Entity
            $entity = Entity::create([
                'entity_id' => $this->codeGenerator->generateCode($attempt->entity_type, $primaryState),
                'entity_name' => $businessData['entity_name'],
                'entity_type' => $attempt->entity_type,
                'is_goods_provider' => true,
                'is_service_provider' => false,
                'is_active' => false,
                'is_approval_required' => true,
                'approval_status' => 'pending',
                'created_by' => 1, // System user temporarily
                'created_at' => now()
            ]);
            
            // 2. Create User
            $user = User::create([
                'name' => $personalData['owner_name'],
                'email' => $attempt->email,
                'user_id' => $this->codeGenerator->generateCode('user'),
                'phone' => $personalData['owner_phone'],
                'entity_id' => $entity->id,
                'email_verified_at' => $attempt->is_email_verified ? now() : null,
                'is_active' => false,
                'multi_login' => 1,
                'is_approval_required' => true,
                'approval_status' => 'pending',
                'password' => $personalData['password'], // Already hashed
                'created_by' => null,
                'created_at' => now()
            ]);
            
            // Update created_by references
            $entity->update(['created_by' => $user->id]);
            $user->update(['created_by' => $user->id]);
            
            // 3. Create Entity Relationship if dealer with distributor
            if ($attempt->entity_type === 'dealer' && !empty($entityTypeData['distributor_id'])) {
                $distributor = Entity::where('entity_id', $entityTypeData['distributor_id'])->first();
                
                EntityRelationship::create([
                    'source_entity_id' => $distributor->id,
                    'target_entity_id' => $entity->id,
                    'relationship_type' => 'sells_to',
                    'description' => 'Dealer registered under distributor',
                    'is_active' => false,
                    'is_approval_required' => true,
                    'approval_status' => 'pending',
                    'created_by' => $user->id
                ]);
            }
            
            // 4. Create Contacts
            foreach ($businessData['contacts'] as $index => $contactData) {
                $contact = Contact::create([
                    'name' => $contactData['name'],
                    'email' => $contactData['email'],
                    'phone' => $contactData['phone'],
                    'is_active' => false,
                    'created_by' => $user->id,
                    'created_at' => now()
                ]);
                
                // Link to entity
                DB::table('contactables')->insert([
                    'contact_id' => $contact->id,
                    'contactable_type' => 'App\\Models\\Entity\\Entity',
                    'contactable_id' => $entity->id,
                    'is_primary' => $index === 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            
            // Create personal contact for user
            $personalContact = Contact::create([
                'name' => $personalData['personal_contact_name'],
                'email' => $personalData['personal_contact_email'],
                'phone' => $personalData['personal_contact_phone'],
                'is_active' => false,
                'created_by' => $user->id,
                'created_at' => now()
            ]);
            
            DB::table('contactables')->insert([
                'contact_id' => $personalContact->id,
                'contactable_type' => 'App\\Models\\User',
                'contactable_id' => $user->id,
                'is_primary' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // 5. Create Addresses
            foreach ($businessData['addresses'] as $index => $addressData) {
                $address = Address::create([
                    'address_line1' => $addressData['address_line1'],
                    'address_line2' => $addressData['address_line2'],
                    'state' => $addressData['state'],
                    'city' => $addressData['city'],
                    'postal_code' => $addressData['postal_code'],
                    'is_active' => false,
                    'created_by' => $user->id,
                    'created_at' => now()
                ]);
                
                // Link to entity
                DB::table('addressables')->insert([
                    'address_id' => $address->id,
                    'addressable_type' => 'App\\Models\\Entity\\Entity',
                    'addressable_id' => $entity->id,
                    'is_primary' => $index === 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            
            // Create personal address for user
            $personalAddress = Address::create([
                'address_line1' => $personalData['personal_address_line1'],
                'address_line2' => $personalData['personal_address_line2'],
                'state' => $personalData['personal_state'],
                'city' => $personalData['personal_city'],
                'postal_code' => $personalData['personal_postal_code'],
                'is_active' => false,
                'created_by' => $user->id,
                'created_at' => now()
            ]);
            
            DB::table('addressables')->insert([
                'address_id' => $personalAddress->id,
                'addressable_type' => 'App\\Models\\User',
                'addressable_id' => $user->id,
                'is_primary' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // 6. Create Tax Information
            $tax = Tax::create([
                'tax_identifier' => $taxData['tax_identifier'],
                'tax_type' => $taxData['tax_type'],
                'tax_region' => $taxData['tax_region'],
                'is_active' => false,
                'created_by' => $user->id,
                'created_at' => now()
            ]);
            
            DB::table('taxables')->insert([
                'tax_id' => $tax->id,
                'taxable_type' => 'App\\Models\\Entity\\Entity',
                'taxable_id' => $entity->id,
                'is_primary' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // 7. Create KYC Documents
            // Business KYCs
            foreach ($businessKycData as $kycData) {
                $kyc = Kyc::create([
                    'document_type' => $kycData['document_type'],
                    'document_number' => $kycData['document_number'],
                    'document_file' => $kycData['document_file'] ?? null,
                    'is_active' => false,
                    'created_by' => $user->id,
                    'created_at' => now()
                ]);
                
                DB::table('kycables')->insert([
                    'kyc_id' => $kyc->id,
                    'kycable_type' => 'App\\Models\\Entity\\Entity',
                    'kycable_id' => $entity->id,
                    'is_primary' => true,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            
            // Personal KYCs
            foreach ($personalKycData as $kycData) {
                $kyc = Kyc::create([
                    'document_type' => $kycData['document_type'],
                    'document_number' => $kycData['document_number'],
                    'document_file' => $kycData['document_file'] ?? null,
                    'is_active' => false,
                    'created_by' => $user->id,
                    'created_at' => now()
                ]);
                
                DB::table('kycables')->insert([
                    'kyc_id' => $kyc->id,
                    'kycable_type' => 'App\\Models\\User',
                    'kycable_id' => $user->id,
                    'is_primary' => true,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            
            // 8. Create RBAC Structure
            $this->createRBACStructure($entity, $user);
            
            // 9. Mark registration as submitted
            $attempt->update([
                'is_submitted' => true,
                'submitted_at' => now(),
                'user_id' => $user->id,
                'success' => true
            ]);
            
            // 10. Send emails
            Mail::to($user->email)->send(new RegistrationCompleteMail($entity, $user));
            
            // Send distributor alert if applicable
            if ($attempt->entity_type === 'dealer' && !empty($entityTypeData['distributor_id'])) {
                $this->sendDistributorAlert($distributor, $entity);
            }
            
            return [
                'entity' => $entity,
                'user' => $user
            ];
        });
    }
    
    protected function createRBACStructure($entity, $user)
    {
        // Create role
        $role = SystemRole::create([
            'entity_id' => $entity->id,
            'role_name' => 'Administrator Manager',
            'description' => 'Top-level entity role with full access within its own entity.',
            'parent_role_id' => null,
            'hierarchy_level' => 2,
            'guard_type' => 'web',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'notes' => 'One per entity'
        ]);
        
        // Create department
        $department = OrganizationDepartment::create([
            'entity_id' => $entity->id,
            'dept_name' => 'Administrator',
            'dept_code' => 'ADMIN',
            'description' => 'Group of administrators',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'display_order' => 1,
            'created_by' => $user->id
        ]);
        
        // Create guard type
        $guard = AccessGuardType::create([
            'entity_id' => $entity->id,
            'guard_name' => 'web',
            'description' => 'Website access',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending'
        ]);
        
        // Create permission
        $permission = SystemPermission::create([
            'entity_id' => $entity->id,
            'permission_code' => 'all',
            'permission_name' => 'all access',
            'description' => 'All actions permitted',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending'
        ]);
        
        // Assign role to user
        DB::table('user_role_assignments')->insert([
            'entity_id' => $entity->id,
            'user_id' => $user->id,
            'role_id' => $role->id,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Assign department to user
        DB::table('user_department_assignments')->insert([
            'entity_id' => $entity->id,
            'user_id' => $user->id,
            'department_id' => $department->id,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_by' => $user->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Assign guard access
        DB::table('user_guard_access')->insert([
            'entity_id' => $entity->id,
            'user_id' => $user->id,
            'guard_id' => $guard->id,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        // Grant permission to role
        DB::table('permission_grants')->insert([
            'entity_id' => $entity->id,
            'permission_id' => $permission->id,
            'role_id' => $role->id,
            'guard_id' => $guard->id,
            'department_id' => null,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
    
    protected function sendDistributorAlert($distributor, $newDealer)
    {
        // Get distributor's primary contact
        $distributorContact = DB::table('contactables')
            ->join('contacts', 'contactables.contact_id', '=', 'contacts.id')
            ->where('contactables.contactable_type', 'App\\Models\\Entity\\Entity')
            ->where('contactables.contactable_id', $distributor->id)
            ->where('contactables.is_primary', true)
            ->first();
            
        if ($distributorContact && $distributorContact->email) {
            Mail::to($distributorContact->email)->send(new DistributorAlertMail($distributor, $newDealer));
        }
    }
}
```

---

## Tailwind CSS Configuration

### `tailwind.config.js`

```javascript
/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
        "./vendor/livewire/flux/resources/**/*.blade.php",
    ],
    theme: {
        extend: {
            // Custom theme extensions if needed
        },
    },
    plugins: [],
}
```

---

## Database Seeders

### `IndianStatesSeeder.php`

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IndianStatesSeeder extends Seeder
{
    public function run(): void
    {
        $states = [
            ['code' => 'AN', 'name' => 'Andaman and Nicobar Islands'],
            ['code' => 'AP', 'name' => 'Andhra Pradesh'],
            ['code' => 'AR', 'name' => 'Arunachal Pradesh'],
            ['code' => 'AS', 'name' => 'Assam'],
            ['code' => 'BR', 'name' => 'Bihar'],
            ['code' => 'CH', 'name' => 'Chandigarh'],
            ['code' => 'CT', 'name' => 'Chhattisgarh'],
            ['code' => 'DN', 'name' => 'Dadra and Nagar Haveli and Daman and Diu'],
            ['code' => 'DL', 'name' => 'Delhi'],
            ['code' => 'GA', 'name' => 'Goa'],
            ['code' => 'GJ', 'name' => 'Gujarat'],
            ['code' => 'HR', 'name' => 'Haryana'],
            ['code' => 'HP', 'name' => 'Himachal Pradesh'],
            ['code' => 'JK', 'name' => 'Jammu and Kashmir'],
            ['code' => 'JH', 'name' => 'Jharkhand'],
            ['code' => 'KA', 'name' => 'Karnataka'],
            ['code' => 'KL', 'name' => 'Kerala'],
            ['code' => 'LA', 'name' => 'Ladakh'],
            ['code' => 'LD', 'name' => 'Lakshadweep'],
            ['code' => 'MP', 'name' => 'Madhya Pradesh'],
            ['code' => 'MH', 'name' => 'Maharashtra'],
            ['code' => 'MN', 'name' => 'Manipur'],
            ['code' => 'ML', 'name' => 'Meghalaya'],
            ['code' => 'MZ', 'name' => 'Mizoram'],
            ['code' => 'NL', 'name' => 'Nagaland'],
            ['code' => 'OR', 'name' => 'Odisha'],
            ['code' => 'PY', 'name' => 'Puducherry'],
            ['code' => 'PB', 'name' => 'Punjab'],
            ['code' => 'RJ', 'name' => 'Rajasthan'],
            ['code' => 'SK', 'name' => 'Sikkim'],
            ['code' => 'TN', 'name' => 'Tamil Nadu'],
            ['code' => 'TG', 'name' => 'Telangana'],
            ['code' => 'TR', 'name' => 'Tripura'],
            ['code' => 'UP', 'name' => 'Uttar Pradesh'],
            ['code' => 'UT', 'name' => 'Uttarakhand'],
            ['code' => 'WB', 'name' => 'West Bengal']
        ];
        
        DB::table('indian_states')->insert($states);
    }
}
```

---

## Implementation Checklist

### Phase 1: Setup & Infrastructure
- [ ] Run all existing migrations
- [ ] Create CodeGeneratorService based on config
- [ ] Set up file storage configuration
- [ ] Configure mail settings in .env
- [ ] Create database seeders for states/cities
- [ ] Install Livewire Flux if not already installed

### Phase 2: Livewire Components
- [ ] Create main RegistrationWizard component
- [ ] Create all 9 step components
- [ ] Create reusable StateCity component
- [ ] Create reusable FileUpload component
- [ ] Implement navigation between steps
- [ ] Add form validation for each step

### Phase 3: Backend Services
- [ ] Implement RegistrationService
- [ ] Create email templates
- [ ] Set up file upload handling
- [ ] Implement transaction-based submission
- [ ] Create RBAC setup logic

### Phase 4: Frontend & UX
- [ ] Style with Tailwind CSS
- [ ] Add loading states
- [ ] Implement error handling
- [ ] Add success messages
- [ ] Create responsive design
- [ ] Add progress indicators

### Phase 5: Testing & Deployment
- [ ] Test complete registration flow
- [ ] Test file uploads
- [ ] Test email sending
- [ ] Test validation rules
- [ ] Test resume functionality
- [ ] Create admin approval interface

### Phase 6: Documentation
- [ ] Document Livewire components
- [ ] Create user guide
- [ ] Document troubleshooting steps
- [ ] Create deployment guide

---

## Security Considerations

1. **CSRF Protection**: Automatically handled by Livewire
2. **File Upload Security**: Validate MIME types and file content
3. **SQL Injection**: Use Eloquent ORM and parameterized queries
4. **XSS Protection**: Use Blade's automatic escaping
5. **Rate Limiting**: Add to OTP endpoints
6. **Session Security**: Configure secure sessions in Laravel

---

## Performance Optimization

1. **Lazy Loading**: Load cities only when state is selected
2. **File Chunking**: For large file uploads
3. **Database Indexing**: Add indexes on frequently queried columns
4. **Query Optimization**: Use eager loading for relationships
5. **Caching**: Cache states/cities data
6. **Queue Jobs**: Queue email sending

---

## Maintenance & Monitoring

1. **Logging**: Log all registration attempts
2. **Cleanup Jobs**: Remove expired registration attempts
3. **Monitoring**: Track registration completion rates
4. **Backups**: Regular database backups
5. **Updates**: Keep Laravel and packages updated

This comprehensive blueprint provides everything needed to implement the Dealer/Distributor Registration Module using Livewire and Tailwind CSS, following all your specifications and requirements.