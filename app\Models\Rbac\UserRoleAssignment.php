<?php

// app/Models/Rbac/UserRoleAssignment.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Entity\Entity;
use App\Models\User;
use App\Traits\HasAuditFields;

/**
 * Class UserRoleAssignment
 *
 * Assigns roles to users with time-based constraints.
 *
 * @property int $id
 * @property int|null $entity_id Associated entity identifier
 * @property int $user_id Associated user
 * @property int $role_id Assigned role
 * @property \Carbon\Carbon|null $assigned_from Assignment start date
 * @property \Carbon\Carbon|null $assigned_until Assignment end date
 * @property bool $is_active Active status
 * @property bool $is_approval_required Flag indicating if approval is needed
 * @property string $approval_status Current approval status
 * @property string|null $assignment_notes Assignment-specific notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class UserRoleAssignment extends Model
{
    use HasAuditFields;

    protected $fillable = [
        'entity_id',
        'user_id',
        'role_id',
        'assigned_from',
        'assigned_until',
        'is_active',
        'is_approval_required',
        'approval_status',
        'assignment_notes',
    ];

    protected $casts = [
        'assigned_from' => 'date',
        'assigned_until' => 'date',
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
    ];

    protected $appends = ['is_expired', 'is_current'];

    public function getIsExpiredAttribute(): bool
    {
        return $this->assigned_until && $this->assigned_until->isPast();
    }

    public function getIsCurrentAttribute(): bool
    {
        if (!$this->is_active || $this->approval_status !== 'approved') {
            return false;
        }

        if ($this->assigned_from && $this->assigned_from->isFuture()) {
            return false;
        }

        if ($this->assigned_until && $this->assigned_until->isPast()) {
            return false;
        }

        return true;
    }

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(SystemRole::class, 'role_id');
    }

    // ===== QUERY SCOPES =====

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        return $query->active()
            ->where('approval_status', 'approved')
            ->where(function ($q) {
                $q->whereNull('assigned_from')
                    ->orWhere('assigned_from', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('assigned_until')
                    ->orWhere('assigned_until', '>', now());
            });
    }

    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForRole($query, int $roleId)
    {
        return $query->where('role_id', $roleId);
    }
}
