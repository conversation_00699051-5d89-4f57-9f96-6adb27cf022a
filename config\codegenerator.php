<?php
// config/codegenerator.php

/**
 * ==============================================================================
 * Enhanced Code Generator Configuration
 * ==============================================================================
 *
 * This configuration file is the heart of the CodeGenerator service. Think of it
 * as the blueprint library at our code mint - it defines exactly how each type
 * of identifier should be formatted, validated, and managed.
 *
 * CRITICAL UNDERSTANDING:
 * Once a code type is in production use, changing its prefix or pattern will
 * cause the sequence to reset, potentially creating duplicate codes. Any changes
 * to production code formats require careful migration planning.
 *
 * Configuration Hierarchy:
 * 1. Runtime overrides (passed to generate method) - Highest priority
 * 2. Type-specific configuration (defined below) - Normal priority
 * 3. Default configuration (fallback values) - Lowest priority
 *
 * @version 3.0
 * @date July 9, 2025
 */

return [

    /*
    |--------------------------------------------------------------------------
    | Service-Level Configuration
    |--------------------------------------------------------------------------
    |
    | These settings control the behavior of the CodeGenerator service itself,
    | not specific to any code type.
    |
    */

    'service' => [
        // Default buffer size for high-frequency code types
        // Larger buffers reduce database load but may waste sequences if server crashes
        'default_buffer_size' => 10,

        // Which code types should use buffering by default
        'buffered_types' => ['sales_order', 'transaction', 'user'],

        // Audit trail settings
        'audit' => [
            'enabled' => true, // Set to false to disable audit trail
            'detailed_metadata' => true, // Include full config in audit metadata
            'retention_days' => 365, // How long to keep audit records
        ],

        // Performance settings
        'cache' => [
            'enabled' => true, // Enable sequence buffering
            'ttl' => 86400, // Cache TTL in seconds (24 hours)
            'prefix' => 'codegen', // Cache key prefix
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Fallback Configuration
    |--------------------------------------------------------------------------
    |
    | These values serve as defaults when specific values aren't provided in
    | the type configuration. They ensure the system can always generate
    | something, even for undefined types (though this should be avoided).
    |
    */

    'defaults' => [
        // The prefix identifies the code type at a glance
        'prefix' => 'GEN',

        // Location code for multi-region deployments
        'location' => env('DEFAULT_LOCATION_CODE', 'MH'),

        // Number of digits for the sequence (will be zero-padded)
        'sequence_length' => 4,

        // Date format for temporal codes (PHP date format)
        'date_format' => 'ymd',

        // Time format for high-frequency codes (PHP date format)
        'time_format' => 'Hi',

        // The template pattern for assembling the final code
        'pattern' => '{prefix}{date_format}{location}{sequence}',

        // Whether to use sequence buffering for performance
        'use_buffer' => false,

        // Validation pattern (regex) to ensure generated codes are correct
        'validation_pattern' => null,
    ],

    /*
    |--------------------------------------------------------------------------
    | Code Type Definitions
    |--------------------------------------------------------------------------
    |
    | Define each type of code your system generates. Each type can override
    | any default value and add type-specific configuration.
    |
    | Available pattern placeholders:
    | - {prefix}      : The type identifier
    | - {location}    : Geographic or branch code
    | - {date_format} : Date component
    | - {time_format} : Time component
    | - {sequence}    : Zero-padded sequence number
    |
    */

    'types' => [

        // ================================================================
        // Entity Identifiers (Permanent, Location-Based)
        // ================================================================

        'supplier' => [
            'prefix' => 'SUP',
            'location' => 'MH', // Maharashtra as default
            'sequence_length' => 4,
            'pattern' => '{prefix}-{location}-{sequence}',
            'validation_pattern' => '/^SUP-[A-Z]{2}-\d{4}$/',
            'description' => 'Supplier entity identifier',
            // No reset_period - these are permanent identifiers
        ],

        'distributor' => [
            'prefix' => 'DIS',
            'location' => 'MH',
            'sequence_length' => 4,
            'pattern' => '{prefix}-{location}-{sequence}',
            'validation_pattern' => '/^DIS-[A-Z]{2}-\d{4}$/',
            'description' => 'Distributor entity identifier',
        ],

        'dealer' => [
            'prefix' => 'DEA',
            'location' => 'MH',
            'sequence_length' => 5, // Higher volume expected
            'pattern' => '{prefix}-{location}-{sequence}',
            'validation_pattern' => '/^DEA-[A-Z]{2}-\d{5}$/',
            'description' => 'Dealer entity identifier',
        ],

        // ================================================================
        // User Identifiers (Simple, High-Volume)
        // ================================================================

        'user' => [
            'prefix' => 'USR',
            'sequence_length' => 6,
            'pattern' => '{prefix}{sequence}',
            'validation_pattern' => '/^USR\d{6}$/',
            'use_buffer' => true, // Enable buffering for performance
            'description' => 'User account identifier',
        ],

        // ================================================================
        // Transaction Codes (Time-Based, Resetting)
        // ================================================================

        'purchase_order' => [
            'prefix' => 'PO',
            'date_format' => 'Y-m', // Year-Month for monthly grouping
            'sequence_length' => 4,
            'pattern' => '{prefix}/{date_format}/{sequence}',
            'validation_pattern' => '/^PO\/\d{4}-\d{2}\/\d{4}$/',
            'reset_period' => 'monthly', // Sequence resets each month
            'description' => 'Purchase order from distributor to supplier',
        ],

        'sales_order' => [
            'prefix' => 'ORD',
            'date_format' => 'ymd', // Compressed date format
            'sequence_length' => 5,
            'pattern' => '{prefix}-{date_format}-{sequence}',
            'validation_pattern' => '/^ORD-\d{6}-\d{5}$/',
            'reset_period' => 'daily', // High volume, daily reset
            'use_buffer' => true, // Enable buffering
            'description' => 'Sales order from distributor to dealer',
        ],

        'transaction' => [
            'prefix' => 'TRN',
            'date_format' => 'Ymd',
            'time_format' => 'His', // Include seconds for uniqueness
            'sequence_length' => 3,
            'pattern' => '{prefix}-{date_format}{time_format}-{sequence}',
            'validation_pattern' => '/^TRN-\d{8}\d{6}-\d{3}$/',
            'use_buffer' => true, // High frequency requires buffering
            'description' => 'Financial transaction reference',
        ],

        // ================================================================
        // Return and Service Codes
        // ================================================================

        'return_order' => [
            'prefix' => 'RET',
            'date_format' => 'Ymd',
            'sequence_length' => 4,
            'pattern' => '{prefix}-{date_format}-{sequence}',
            'validation_pattern' => '/^RET-\d{8}-\d{4}$/',
            'reset_period' => 'daily',
            'description' => 'Product return order',
        ],

        'service_ticket' => [
            'prefix' => 'TKT',
            'date_format' => 'ym', // Year-month in short format
            'sequence_length' => 5,
            'pattern' => '{prefix}-{date_format}-{sequence}',
            'validation_pattern' => '/^TKT-\d{4}-\d{5}$/',
            'reset_period' => 'monthly',
            'description' => 'Service or support ticket',
        ],

        // ================================================================
        // Inventory and Logistics Codes
        // ================================================================

        'batch_number' => [
            'prefix' => 'BAT',
            'location' => 'MH',
            'date_format' => 'Ymd',
            'sequence_length' => 3,
            'pattern' => '{prefix}-{location}-{date_format}-{sequence}',
            'validation_pattern' => '/^BAT-[A-Z]{2}-\d{8}-\d{3}$/',
            'description' => 'Inventory batch identifier',
        ],

        'shipment_tracking' => [
            'prefix' => 'SHP',
            'date_format' => 'ymdHi', // Include time for uniqueness
            'sequence_length' => 4,
            'pattern' => '{prefix}{date_format}{sequence}',
            'validation_pattern' => '/^SHP\d{10}\d{4}$/',
            'description' => 'Shipment tracking number',
        ],

        // ================================================================
        // Financial Codes
        // ================================================================

        'invoice' => [
            'prefix' => 'INV',
            'date_format' => 'Y/m',
            'sequence_length' => 5,
            'pattern' => '{prefix}-{date_format}-{sequence}',
            'validation_pattern' => '/^INV-\d{4}\/\d{2}-\d{5}$/',
            'reset_period' => 'monthly',
            'description' => 'Invoice number',
        ],

        'receipt' => [
            'prefix' => 'RCP',
            'date_format' => 'Ymd',
            'sequence_length' => 6,
            'pattern' => '{prefix}-{date_format}-{sequence}',
            'validation_pattern' => '/^RCP-\d{8}-\d{6}$/',
            'reset_period' => 'daily',
            'description' => 'Payment receipt number',
        ],

        // ================================================================
        // Special Administrative Codes
        // ================================================================

        'registration_token' => [
            'prefix' => 'REG',
            'time_format' => 'His',
            'sequence_length' => 8, // Longer for security
            'pattern' => '{prefix}-{time_format}-{sequence}',
            'validation_pattern' => '/^REG-\d{6}-\d{8}$/',
            'use_buffer' => false, // Security tokens shouldn't be buffered
            'description' => 'User registration token identifier',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Sequence Reset Periods
    |--------------------------------------------------------------------------
    |
    | Define how different reset periods work. This is used when a type
    | specifies a 'reset_period' value.
    |
    */

    'reset_periods' => [
        'daily' => [
            'format' => 'Ymd',
            'description' => 'Resets at midnight each day',
        ],
        'monthly' => [
            'format' => 'Ym',
            'description' => 'Resets on the first day of each month',
        ],
        'yearly' => [
            'format' => 'Y',
            'description' => 'Resets on January 1st each year',
        ],
        'never' => [
            'format' => '',
            'description' => 'Never resets (permanent sequence)',
        ],
    ],
];
