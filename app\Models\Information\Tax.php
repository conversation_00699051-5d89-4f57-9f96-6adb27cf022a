<?php
// app/Models/Information/Tax.php
namespace App\Models\Information;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Entity\Entity;
use App\Models\User;

/**
 * Class Tax
 *
 * Represents tax information that can be associated with both entities and users.
 * Uses polymorphic many-to-many relationships via the taxables pivot table.
 *
 * Tax information includes various types of tax identifiers (GST, VAT, Income Tax, etc.)
 * with their unique identifiers and applicable regions. This is essential for compliance
 * and business operations across different jurisdictions.
 *
 * @property int $id
 * @property string $tax_identifier Unique identifier for the tax (e.g., GSTIN, VAT ID)
 * @property string $tax_type Type of tax (e.g., GST, VAT, Income Tax)
 * @property string|null $tax_region Region or jurisdiction where the tax applies
 * @property \Carbon\Carbon|null $effective_date Date when the tax registration becomes effective
 * @property \Carbon\Carbon|null $expiry_date Date when the tax registration expires
 * @property bool $is_active Status: true=active, false=inactive
 * @property int $created_by User ID who created this record
 * @property int|null $updated_by User ID who last updated this record
 * @property int|null $deleted_by User ID who soft deleted this record
 * @property int|null $restored_by User ID who restored this record
 * @property \Carbon\Carbon|null $restored_at When the record was restored
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder byRegion(string $region)
 * @method static \Illuminate\Database\Eloquent\Builder search(string $search)
 */
class Tax extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'taxes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'tax_identifier',
        'tax_type',
        'tax_region',
        'effective_date',
        'expiry_date',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'effective_date' => 'date',
        'expiry_date' => 'date',
        'restored_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'display_name',
        'tax_type_label',
        'is_gst',
        'is_indian_tax'
    ];

    /**
     * Tax type constants with labels.
     *
     * @var array<string, string>
     */
    const TAX_TYPES = [
        'gst' => 'Goods and Services Tax (GST)',
        'vat' => 'Value Added Tax (VAT)',
        'income_tax' => 'Income Tax',
        'corporate_tax' => 'Corporate Tax',
        'service_tax' => 'Service Tax',
        'excise_duty' => 'Excise Duty',
        'customs_duty' => 'Customs Duty',
        'property_tax' => 'Property Tax',
        'professional_tax' => 'Professional Tax',
        'tds' => 'Tax Deducted at Source (TDS)',
        'tcs' => 'Tax Collected at Source (TCS)',
        'sales_tax' => 'Sales Tax',
        'other' => 'Other Tax'
    ];

    /**
     * Indian tax types.
     *
     * @var array<string>
     */
    const INDIAN_TAX_TYPES = [
        'gst',
        'income_tax',
        'service_tax',
        'excise_duty',
        'customs_duty',
        'professional_tax',
        'tds',
        'tcs'
    ];

    /**
     * Tax types that typically apply to businesses.
     *
     * @var array<string>
     */
    const BUSINESS_TAX_TYPES = [
        'gst',
        'vat',
        'corporate_tax',
        'service_tax',
        'excise_duty',
        'customs_duty',
        'tcs'
    ];

    /**
     * Tax types that typically apply to individuals.
     *
     * @var array<string>
     */
    const INDIVIDUAL_TAX_TYPES = [
        'income_tax',
        'professional_tax',
        'property_tax',
        'tds'
    ];

    // ===== ACCESSORS =====

    /**
     * Get human-readable tax type.
     *
     * @return string
     */
    public function getTaxTypeLabelAttribute(): string
    {
        return self::TAX_TYPES[$this->tax_type] ?? 'Unknown Tax Type';
    }

    /**
     * Get display name for the tax information.
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        $display = "{$this->tax_type_label}: {$this->tax_identifier}";
        
        if ($this->tax_region) {
            $display .= " ({$this->tax_region})";
        }
        
        return $display;
    }

    /**
     * Check if this is a GST number.
     *
     * @return bool
     */
    public function getIsGstAttribute(): bool
    {
        return $this->isGstNumber();
    }

    /**
     * Check if this is an Indian tax type.
     *
     * @return bool
     */
    public function getIsIndianTaxAttribute(): bool
    {
        return $this->isIndianTax();
    }

    // ===== HELPER METHODS =====

    /**
     * Check if this is a GST number.
     *
     * @return bool
     */
    public function isGstNumber(): bool
    {
        return $this->tax_type === 'gst';
    }

    /**
     * Check if this is an Indian tax type.
     *
     * @return bool
     */
    public function isIndianTax(): bool
    {
        return in_array($this->tax_type, self::INDIAN_TAX_TYPES);
    }

    /**
     * Check if this tax type typically applies to businesses.
     *
     * @return bool
     */
    public function isBusinessTax(): bool
    {
        return in_array($this->tax_type, self::BUSINESS_TAX_TYPES);
    }

    /**
     * Check if this tax type typically applies to individuals.
     *
     * @return bool
     */
    public function isIndividualTax(): bool
    {
        return in_array($this->tax_type, self::INDIVIDUAL_TAX_TYPES);
    }

    /**
     * Validate tax identifier format based on tax type.
     *
     * @return bool
     */
    public function hasValidFormat(): bool
    {
        return match ($this->tax_type) {
            'gst' => $this->isValidGstNumber(),
            'income_tax' => $this->isValidPanNumber(),
            'tds', 'tcs' => $this->isValidTanNumber(),
            default => !empty($this->tax_identifier)
        };
    }

    /**
     * Check if GST number format is valid.
     *
     * @return bool
     */
    protected function isValidGstNumber(): bool
    {
        // GST format: 2 digits state code + 10 digit PAN + 1 check digit + Z + 1 alphanumeric
        return preg_match('/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/', $this->tax_identifier);
    }

    /**
     * Check if PAN number format is valid.
     *
     * @return bool
     */
    protected function isValidPanNumber(): bool
    {
        // PAN format: 5 letters + 4 digits + 1 letter
        return preg_match('/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/', $this->tax_identifier);
    }

    /**
     * Check if TAN number format is valid.
     *
     * @return bool
     */
    protected function isValidTanNumber(): bool
    {
        // TAN format: 4 letters + 5 digits + 1 letter
        return preg_match('/^[A-Z]{4}[0-9]{5}[A-Z]{1}$/', $this->tax_identifier);
    }

    /**
     * Get the state code from GST number.
     *
     * @return string|null
     */
    public function getGstStateCode(): ?string
    {
        if (!$this->isGstNumber() || !$this->hasValidFormat()) {
            return null;
        }

        return substr($this->tax_identifier, 0, 2);
    }

    /**
     * Get the PAN from GST number.
     *
     * @return string|null
     */
    public function getPanFromGst(): ?string
    {
        if (!$this->isGstNumber() || !$this->hasValidFormat()) {
            return null;
        }

        return substr($this->tax_identifier, 2, 10);
    }

    // ===== POLYMORPHIC RELATIONSHIPS =====

    /**
     * Get all entities that have this tax information.
     *
     * @return MorphToMany
     */
    public function entities(): MorphToMany
    {
        return $this->morphedByMany(Entity::class, 'taxable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get all users that have this tax information.
     *
     * @return MorphToMany
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'taxable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get all models (entities and users) that have this tax information.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllTaxables()
    {
        return collect()
            ->merge($this->entities)
            ->merge($this->users);
    }

    /**
     * Check if this tax information is primary for any model.
     *
     * @return bool
     */
    public function isPrimaryForAny(): bool
    {
        $entityPrimary = $this->entities()->wherePivot('is_primary', true)->exists();
        $userPrimary = $this->users()->wherePivot('is_primary', true)->exists();

        return $entityPrimary || $userPrimary;
    }

    // ===== AUDIT RELATIONSHIPS =====

    /**
     * Get the user who created this tax information.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this tax information.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who soft deleted this tax information.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored this tax information.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to only active tax information.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to tax information by type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('tax_type', $type);
    }

    /**
     * Scope to tax information by region.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $region
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRegion($query, string $region)
    {
        return $query->where('tax_region', $region);
    }

    /**
     * Scope to GST numbers only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeGstOnly($query)
    {
        return $query->where('tax_type', 'gst');
    }

    /**
     * Scope to Indian tax types.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIndianTaxes($query)
    {
        return $query->whereIn('tax_type', self::INDIAN_TAX_TYPES);
    }

    /**
     * Scope to business tax types.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBusinessTaxes($query)
    {
        return $query->whereIn('tax_type', self::BUSINESS_TAX_TYPES);
    }

    /**
     * Scope to individual tax types.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIndividualTaxes($query)
    {
        return $query->whereIn('tax_type', self::INDIVIDUAL_TAX_TYPES);
    }

    /**
     * Scope to search tax information by identifier, type, or region.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('tax_identifier', 'like', "%{$search}%")
                ->orWhere('tax_type', 'like', "%{$search}%")
                ->orWhere('tax_region', 'like', "%{$search}%");
        });
    }

    // ===== STATIC METHODS =====

    /**
     * Find tax information by identifier.
     *
     * @param string $taxIdentifier
     * @param string|null $taxType
     * @return self|null
     */
    public static function findByIdentifier(string $taxIdentifier, ?string $taxType = null): ?self
    {
        $query = self::where('tax_identifier', $taxIdentifier);

        if ($taxType) {
            $query->where('tax_type', $taxType);
        }

        return $query->first();
    }

    /**
     * Check if tax identifier already exists.
     *
     * @param string $taxIdentifier
     * @param string|null $taxType
     * @param int|null $excludeId
     * @return bool
     */
    public static function identifierExists(string $taxIdentifier, ?string $taxType = null, ?int $excludeId = null): bool
    {
        $query = self::where('tax_identifier', $taxIdentifier);

        if ($taxType) {
            $query->where('tax_type', $taxType);
        }

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get available tax types for dropdown.
     *
     * @param bool $forBusiness Whether to return business tax types
     * @param bool $indianOnly Whether to return only Indian tax types
     * @return array
     */
    public static function getTaxTypes(bool $forBusiness = false, bool $indianOnly = false): array
    {
        $types = self::TAX_TYPES;

        if ($indianOnly) {
            $types = array_intersect_key(
                $types,
                array_flip(self::INDIAN_TAX_TYPES)
            );
        }

        if ($forBusiness) {
            $allowedTypes = $indianOnly ? 
                array_intersect(self::BUSINESS_TAX_TYPES, self::INDIAN_TAX_TYPES) :
                self::BUSINESS_TAX_TYPES;
                
            $types = array_intersect_key(
                $types,
                array_flip($allowedTypes)
            );
        }

        return $types;
    }

    /**
     * Get GST state codes mapping.
     *
     * @return array
     */
    public static function getGstStateCodes(): array
    {
        return [
            '01' => 'Jammu and Kashmir',
            '02' => 'Himachal Pradesh',
            '03' => 'Punjab',
            '04' => 'Chandigarh',
            '05' => 'Uttarakhand',
            '06' => 'Haryana',
            '07' => 'Delhi',
            '08' => 'Rajasthan',
            '09' => 'Uttar Pradesh',
            '10' => 'Bihar',
            '11' => 'Sikkim',
            '12' => 'Arunachal Pradesh',
            '13' => 'Nagaland',
            '14' => 'Manipur',
            '15' => 'Mizoram',
            '16' => 'Tripura',
            '17' => 'Meghalaya',
            '18' => 'Assam',
            '19' => 'West Bengal',
            '20' => 'Jharkhand',
            '21' => 'Odisha',
            '22' => 'Chhattisgarh',
            '23' => 'Madhya Pradesh',
            '24' => 'Gujarat',
            '25' => 'Daman and Diu',
            '26' => 'Dadra and Nagar Haveli',
            '27' => 'Maharashtra',
            '28' => 'Andhra Pradesh',
            '29' => 'Karnataka',
            '30' => 'Goa',
            '31' => 'Lakshadweep',
            '32' => 'Kerala',
            '33' => 'Tamil Nadu',
            '34' => 'Puducherry',
            '35' => 'Andaman and Nicobar Islands',
            '36' => 'Telangana',
            '37' => 'Andhra Pradesh',
            '38' => 'Ladakh'
        ];
    }
}