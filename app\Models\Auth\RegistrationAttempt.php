<?php
// app/Models/Auth/RegistrationAttempt.php
namespace App\Models\Auth;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class RegistrationAttempt extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'user_id',
        'entity_code',
        'email',
        'ip_address',
        'user_agent',
        'source',
        'success',
        'attempted_at',
        // Multi-stage registration fields
        'current_stage',
        'stages_data',
        'stages_completed',
        'entity_type',
        'verification_token',
        'verification_sent_at',
        'is_email_verified',
        'resume_token',
        'resume_token_created_at',
        'expires_at',
        'is_submitted',
        'submitted_at',
        'personal_kyc_documents', // NECESSARY CHANGE: Added to allow mass assignment
    ];

    protected $casts = [
        'success' => 'boolean',
        'attempted_at' => 'datetime',
        'stages_data' => 'array',
        'stages_completed' => 'array',
        'verification_sent_at' => 'datetime',
        'is_email_verified' => 'boolean',
        'resume_token_created_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_submitted' => 'boolean',
        'submitted_at' => 'datetime',
        'personal_kyc_documents' => 'array', // NECESSARY CHANGE: Added to handle JSON conversion
    ];

    /**
     * The registration stages in order.
     */
    const STAGES = [
        'email_verification',
        'entity_type_selection',
        'business_information',
        'personal_details',
        'tax_information',
        'business_kyc_documents',
        'additional_documents',
        'personal_kyc_documents',
        'review_submit',
    ];

    /**
     * Entity types with their display names.
     */
    const ENTITY_TYPES = [
        'distributor' => 'Distributor',
        'dealer' => 'Dealer',
    ];

    /**
     * Get the user relationship.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a new registration attempt with initial data.
     */
    public static function initiate(string $email, string $ipAddress, string $userAgent): self
    {
        $attempt = new self([
            'email' => strtolower(trim($email)),
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'attempted_at' => now(),
            'current_stage' => 'email_verification',
            'stages_completed' => [],
            'stages_data' => [],
            'expires_at' => now()->addHours(24),
        ]);

        $attempt->generateVerificationToken();
        $attempt->generateResumeToken();
        $attempt->save();

        return $attempt;
    }

    /**
     * Generate a verification token (OTP) for email verification.
     */
    public function generateVerificationToken(): void
    {
        // Generate a 6-digit OTP
        $this->verification_token = str_pad((string)mt_rand(100000, 999999), 6, '0', STR_PAD_LEFT);
        $this->verification_sent_at = now();
    }

    /**
     * Verify the email with the given OTP.
     */
    public function verifyEmail(string $otp): bool
    {
        // Check if OTP is valid and not expired
        if ($this->verification_token !== $otp) {
            return false;
        }

        // Check if OTP is expired (15 minutes)
        if ($this->verification_sent_at->addMinutes(15)->isPast()) {
            return false;
        }

        $this->is_email_verified = true;
        $this->completeStage('email_verification');
        $this->save();

        return true;
    }

    /**
     * Generate a resume token for continuing registration later.
     */
    public function generateResumeToken(): void
    {
        $this->resume_token = Str::random(64);
        $this->resume_token_created_at = now();
    }

    /**
     * Find a registration attempt by resume token.
     */
    public static function findByResumeToken(string $token): ?self
    {
        return self::where('resume_token', $token)
            ->where('expires_at', '>', now())
            ->where('is_submitted', false)
            ->first();
    }

    /**
     * Move to the next stage of registration.
     */
    public function nextStage(): bool
    {
        $currentIndex = array_search($this->current_stage, self::STAGES);
        
        if ($currentIndex === false || $currentIndex >= count(self::STAGES) - 1) {
            return false;
        }

        $this->current_stage = self::STAGES[$currentIndex + 1];
        $this->save();

        return true;
    }

    /**
     * Go back to the previous stage.
     */
    public function previousStage(): bool
    {
        $currentIndex = array_search($this->current_stage, self::STAGES);
        
        if ($currentIndex === false || $currentIndex <= 0) {
            return false;
        }

        $this->current_stage = self::STAGES[$currentIndex - 1];
        $this->save();

        return true;
    }

    /**
     * Mark a stage as completed and store its data.
     */
    public function completeStage(string $stage, array $data = []): void
    {
        $stages = $this->stages_completed ?? [];
        
        if (!in_array($stage, $stages)) {
            $stages[] = $stage;
        }
        
        $this->stages_completed = $stages;
        
        if (!empty($data)) {
            $stagesData = $this->stages_data ?? [];
            $stagesData[$stage] = $data;
            $this->stages_data = $stagesData;
        }
    }

    /**
     * Check if a stage is completed.
     */
    public function isStageCompleted(string $stage): bool
    {
        return in_array($stage, $this->stages_completed ?? []);
    }

    /**
     * Get data for a specific stage.
     */
    public function getStageData(string $stage): array
    {
        return $this->stages_data[$stage] ?? [];
    }

    /**
     * Get all stage data combined.
     */
    public function getAllData(): array
    {
        $data = [];
        foreach (self::STAGES as $stage) {
            if (isset($this->stages_data[$stage])) {
                $data = array_merge($data, $this->stages_data[$stage]);
            }
        }
        return $data;
    }

    /**
     * Set the entity type and adjust stages if needed.
     */
    public function setEntityType(string $type): void
    {
        if (!array_key_exists($type, self::ENTITY_TYPES)) {
            throw new \InvalidArgumentException("Invalid entity type: $type");
        }

        $this->entity_type = $type;
        $this->save();
    }

    /**
     * Check if registration has expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Extend expiration time.
     */
    public function extendExpiration(int $hours = 24): void
    {
        $this->expires_at = now()->addHours($hours);
        $this->save();
    }

    /**
     * Submit the registration for approval.
     */
    public function submit(): void
    {
        $this->is_submitted = true;
        $this->submitted_at = now();
        $this->save();
    }

    /**
     * Get the progress percentage.
     */
    public function getProgressPercentage(): int
    {
        $totalStages = count(self::STAGES);
        $completedStages = count($this->stages_completed ?? []);
        
        return (int) round(($completedStages / $totalStages) * 100);
    }

    /**
     * Get the resume URL.
     */
    public function getResumeUrl(): string
    {
        return route('entity.registration.resume', ['token' => $this->resume_token]);
    }

    /**
     * Check if all required stages are completed.
     */
    public function isComplete(): bool
    {
        // Check if all stages except the final review are completed
        $requiredStages = array_slice(self::STAGES, 0, -1);
        
        foreach ($requiredStages as $stage) {
            if (!$this->isStageCompleted($stage)) {
                return false;
            }
        }
        
        return true;
    }
}
