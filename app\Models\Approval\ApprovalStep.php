<?php
// app\Models\Approval\ApprovalStep.php
namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Rbac\SystemRole;
use App\Traits\HasAuditFields;

/**
 * Class ApprovalStep
 *
 * Represents a single step in an approval workflow.
 * Each step defines who can approve and under what conditions.
 *
 * @property int $id
 * @property int $workflow_id Which workflow this step belongs to
 * @property int $step_number Order of this step (1, 2, 3...)
 * @property int $role_id Role that can approve this step
 * @property bool $is_required Whether this step is mandatory
 * @property bool $notify_approver Send notification when pending
 * @property array|null $approval_criteria Additional conditions for this step
 * @property array|null $condition Rule that must be met for step to be active
 * @property int|null $escalation_after_hours Hours before escalation
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class ApprovalStep extends Model
{
    use SoftDeletes, HasAuditFields;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'workflow_id',
        'step_number',
        'role_id',
        'is_required',
        'notify_approver',
        'approval_criteria',
        'condition',
        'escalation_after_hours',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_required' => 'boolean',
        'notify_approver' => 'boolean',
        'approval_criteria' => 'array',
        'condition' => 'array',
        'step_number' => 'integer',
        'escalation_after_hours' => 'integer',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = ['has_escalation', 'is_conditional'];

    /**
     * Check if this step has escalation configured.
     *
     * @return bool
     */
    public function getHasEscalationAttribute(): bool
    {
        return $this->escalation_after_hours !== null && $this->escalation_after_hours > 0;
    }

    /**
     * Check if this step is conditional.
     *
     * @return bool
     */
    public function getIsConditionalAttribute(): bool
    {
        return !empty($this->condition);
    }

    /**
     * Get escalation deadline for a given start time.
     *
     * @param \Carbon\Carbon|null $startTime
     * @return \Carbon\Carbon|null
     */
    public function getEscalationDeadline($startTime = null): ?\Carbon\Carbon
    {
        if (!$this->has_escalation) {
            return null;
        }

        $start = $startTime ?? now();
        return $start->addHours($this->escalation_after_hours);
    }

    /**
     * Check if this step should be escalated based on start time.
     *
     * @param \Carbon\Carbon $startTime
     * @return bool
     */
    public function shouldEscalate(\Carbon\Carbon $startTime): bool
    {
        if (!$this->has_escalation) {
            return false;
        }

        return now()->greaterThan($this->getEscalationDeadline($startTime));
    }

    /**
     * Check if step condition is met.
     *
     * @param array $data Request data to check against
     * @return bool
     */
    public function isConditionMet(array $data): bool
    {
        if (!$this->is_conditional) {
            return true;
        }

        return $this->evaluateCondition($this->condition, $data);
    }

    /**
     * Check if approval criteria are met.
     *
     * @param array $context Approval context (user, request data, etc.)
     * @return bool
     */
    public function checkApprovalCriteria(array $context): bool
    {
        if (empty($this->approval_criteria)) {
            return true;
        }

        foreach ($this->approval_criteria as $criterion) {
            if (!$this->evaluateCriterion($criterion, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     *
     * @param array $condition
     * @param array $data
     * @return bool
     */
    protected function evaluateCondition(array $condition, array $data): bool
    {
        $field = $condition['field'] ?? null;
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? null;

        if (!$field || !isset($data[$field])) {
            return false;
        }

        $dataValue = $data[$field];

        return match ($operator) {
            '=' => $dataValue == $value,
            '!=' => $dataValue != $value,
            '>' => $dataValue > $value,
            '>=' => $dataValue >= $value,
            '<' => $dataValue < $value,
            '<=' => $dataValue <= $value,
            'in' => in_array($dataValue, (array)$value),
            'not_in' => !in_array($dataValue, (array)$value),
            default => false,
        };
    }

    /**
     * Evaluate approval criterion.
     *
     * @param array $criterion
     * @param array $context
     * @return bool
     */
    protected function evaluateCriterion(array $criterion, array $context): bool
    {
        $type = $criterion['type'] ?? null;

        return match ($type) {
            'department_match' => $this->checkDepartmentMatch($criterion, $context),
            'amount_limit' => $this->checkAmountLimit($criterion, $context),
            'user_attribute' => $this->checkUserAttribute($criterion, $context),
            default => true,
        };
    }

    /**
     * Check if approver and requester are in same department.
     *
     * @param array $criterion
     * @param array $context
     * @return bool
     */
    protected function checkDepartmentMatch(array $criterion, array $context): bool
    {
        $approver = $context['approver'] ?? null;
        $requester = $context['requester'] ?? null;

        if (!$approver || !$requester) {
            return false;
        }

        // This would check department assignments once implemented
        // return $approver->department_id === $requester->department_id;
        return true;
    }

    /**
     * Check if amount is within approver's limit.
     *
     * @param array $criterion
     * @param array $context
     * @return bool
     */
    protected function checkAmountLimit(array $criterion, array $context): bool
    {
        $amount = $context['amount'] ?? 0;
        $limit = $criterion['max_amount'] ?? PHP_INT_MAX;

        return $amount <= $limit;
    }

    /**
     * Check user attribute criterion.
     *
     * @param array $criterion
     * @param array $context
     * @return bool
     */
    protected function checkUserAttribute(array $criterion, array $context): bool
    {
        $user = $context['approver'] ?? null;
        $attribute = $criterion['attribute'] ?? null;
        $value = $criterion['value'] ?? null;

        if (!$user || !$attribute) {
            return false;
        }

        return $user->$attribute == $value;
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the workflow this step belongs to.
     *
     * @return BelongsTo
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(ApprovalWorkflow::class, 'workflow_id');
    }

    /**
     * Get the role that can approve this step.
     *
     * @return BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(SystemRole::class, 'role_id');
    }

    /**
     * Get all responses for this step across all requests.
     *
     * @return HasMany
     */
    public function responses(): HasMany
    {
        return $this->hasMany(ApprovalResponse::class, 'step_number', 'step_number')
            ->whereHas('request', function ($query) {
                $query->where('workflow_id', $this->workflow_id);
            });
    }

    /**
     * Get escalations for this step.
     *
     * @return HasMany
     */
    public function escalations(): HasMany
    {
        return $this->hasMany(ApprovalEscalation::class, 'step_number', 'step_number')
            ->whereHas('request', function ($query) {
                $query->where('workflow_id', $this->workflow_id);
            });
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to required steps only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope to optional steps only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOptional($query)
    {
        return $query->where('is_required', false);
    }

    /**
     * Scope to steps with escalation.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithEscalation($query)
    {
        return $query->whereNotNull('escalation_after_hours')
            ->where('escalation_after_hours', '>', 0);
    }

    /**
     * Scope to conditional steps.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeConditional($query)
    {
        return $query->whereNotNull('condition');
    }

    /**
     * Scope to steps for a specific role.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $roleId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForRole($query, int $roleId)
    {
        return $query->where('role_id', $roleId);
    }

    // ===== STATIC METHODS =====

    /**
     * Create a standard approval step.
     *
     * @param int $workflowId
     * @param int $stepNumber
     * @param int $roleId
     * @param array $options
     * @return self
     */
    public static function createStep(
        int $workflowId,
        int $stepNumber,
        int $roleId,
        array $options = []
    ): self {
        return self::create(array_merge([
            'workflow_id' => $workflowId,
            'step_number' => $stepNumber,
            'role_id' => $roleId,
            'is_required' => true,
            'notify_approver' => true,
        ], $options));
    }

    /**
     * Get next step number for a workflow.
     *
     * @param int $workflowId
     * @return int
     */
    public static function getNextStepNumber(int $workflowId): int
    {
        $maxStep = self::where('workflow_id', $workflowId)
            ->max('step_number');

        return ($maxStep ?? 0) + 1;
    }
}
