<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\MagicLink;

class MagicLinkUsed extends Notification implements ShouldQueue
{
    use Queueable;

    protected $magicLink;
    protected $ipAddress;

    /**
     * Create a new notification instance.
     */
    public function __construct(MagicLink $magicLink, $ipAddress)
    {
        $this->magicLink = $magicLink;
        $this->ipAddress = $ipAddress;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Your Magic Link Was Used')
            ->greeting('Hello!')
            ->line('Your magic link was just used to log in.')
            ->line('IP Address: ' . $this->ipAddress)
            ->line('Time: ' . now()->format('Y-m-d H:i:s'))
            ->line('If this was not you, please contact support immediately.')
            ->action('Manage Your Account', url('/sessions/history-dashboard'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'magic_link_id' => $this->magicLink->id,
            'ip_address' => $this->ipAddress,
            'used_at' => $this->magicLink->used_at,
        ];
    }
}
