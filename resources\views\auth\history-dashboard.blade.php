<x-layouts.app :title="'History Dashboard'">
    <div class="max-w-7xl mx-auto py-10 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-bold mb-6">History Dashboard</h2>

        <!-- Magic Link History Table -->
        <div class="mb-10">
            <h3 class="text-lg font-semibold mb-2">Magic Link History</h3>
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 mb-4">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-2">Email</th>
                        <th class="px-4 py-2">Created</th>
                        <th class="px-4 py-2">Validity (min)</th>
                        <th class="px-4 py-2">Expires At</th>
                        <th class="px-4 py-2">Status</th>
                        <th class="px-4 py-2">Used At</th>
                        <th class="px-4 py-2">Cancelled At</th>
                        <th class="px-4 py-2">Session</th>
                        <th class="px-4 py-2">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($magicLinks as $link)
                        <tr>
                            <td class="px-4 py-2">{{ $link->email }}</td>
                            <td class="px-4 py-2">{{ $link->created_at->format('Y-m-d H:i') }}</td>
                            <td class="px-4 py-2">{{ $link->validity_in_minutes }}</td>
                            <td class="px-4 py-2">{{ $link->expires_at->format('Y-m-d H:i') }}</td>
                            <td class="px-4 py-2">
                                @if($link->isCancelled())
                                    <span class="text-red-600">Cancelled</span>
                                @elseif($link->isUsed())
                                    <span class="text-green-600">Used</span>
                                @elseif($link->isExpired())
                                    <span class="text-gray-500">Expired</span>
                                @else
                                    <span class="text-blue-600">Active</span>
                                @endif
                            </td>
                            <td class="px-4 py-2">{{ $link->used_at ? $link->used_at->format('Y-m-d H:i') : '-' }}</td>
                            <td class="px-4 py-2">{{ $link->cancelled_at ? $link->cancelled_at->format('Y-m-d H:i') : '-' }}</td>
                            <td class="px-4 py-2">{{ $link->history_id ?? '-' }}</td>
                            <td class="px-4 py-2">
                                @if(!$link->isUsed() && !$link->isCancelled() && !$link->isExpired())
                                    <form action="{{ route('magic-link.cancel', $link->id) }}" method="POST" onsubmit="return confirm('Cancel this magic link?');">
                                        @csrf
                                        <button type="submit" class="text-red-600 hover:underline">Cancel</button>
                                    </form>
                                @else
                                    -
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Login History Table -->
        <div class="mb-10">
            <h3 class="text-lg font-semibold mb-2">Login History</h3>
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 mb-4">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-2">Login At</th>
                        <th class="px-4 py-2">IP</th>
                        <th class="px-4 py-2">Device</th>
                        <th class="px-4 py-2">Browser</th>
                        <th class="px-4 py-2">Session ID</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($loginHistories as $history)
                        <tr>
                            <td class="px-4 py-2">{{ $history->login_at->format('Y-m-d H:i') }}</td>
                            <td class="px-4 py-2">{{ $history->ip_address }}</td>
                            <td class="px-4 py-2">{{ $history->device_type }}</td>
                            <td class="px-4 py-2">{{ $history->browser }}</td>
                            <td class="px-4 py-2">{{ $history->session_id }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- User Login Statistics Table -->
        <div class="mb-10">
            <h3 class="text-lg font-semibold mb-2">User Login Statistics</h3>
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 mb-4">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-2">Date</th>
                        <th class="px-4 py-2">Total Logins</th>
                        <th class="px-4 py-2">Successful</th>
                        <th class="px-4 py-2">Failed</th>
                        <th class="px-4 py-2">Suspicious</th>
                        <th class="px-4 py-2">New Device</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($loginStats as $stat)
                        <tr>
                            <td class="px-4 py-2">{{ $stat->login_date->format('Y-m-d') }}</td>
                            <td class="px-4 py-2">{{ $stat->total_logins }}</td>
                            <td class="px-4 py-2">{{ $stat->successful_logins }}</td>
                            <td class="px-4 py-2">{{ $stat->failed_logins }}</td>
                            <td class="px-4 py-2">{{ $stat->suspicious_logins }}</td>
                            <td class="px-4 py-2">{{ $stat->new_device_logins }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Transferred Session History Table -->
        <div class="mb-10">
            <h3 class="text-lg font-semibold mb-2">Transferred Session History</h3>
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 mb-4">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-2">Login At</th>
                        <th class="px-4 py-2">Transferred From</th>
                        <th class="px-4 py-2">Transferred To</th>
                        <th class="px-4 py-2">Session ID</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($transferredSessions as $session)
                        <tr>
                            <td class="px-4 py-2">{{ $session->login_at->format('Y-m-d H:i') }}</td>
                            <td class="px-4 py-2">{{ $session->transferred_from ?? '-' }}</td>
                            <td class="px-4 py-2">{{ $session->transferred_to ?? '-' }}</td>
                            <td class="px-4 py-2">{{ $session->session_id }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</x-layouts.app> 