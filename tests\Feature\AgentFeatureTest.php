<?php

namespace Tests\Feature;

use App\Agent\Facades\Agent;
use Tests\TestCase;

class AgentFeatureTest extends TestCase
{
    /**
     * Test that the Agent facade works correctly
     */
    public function testAgentFacade()
    {
        $this->app['request']->headers->set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        
        $this->assertEquals('Chrome', Agent::browser());
        $this->assertEquals('Windows', Agent::platform());
        $this->assertTrue(Agent::isDesktop());
    }

    /**
     * Test that the Agent class is properly bound as a singleton
     */
    public function testAgentIsBoundAsSingleton()
    {
        $instance1 = $this->app['agent'];
        $instance2 = $this->app['agent'];
        
        $this->assertSame($instance1, $instance2);
    }

    /**
     * Test the Request macro
     */
    public function testRequestMacro()
    {
        $this->app['request']->headers->set('User-Agent', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1');
        
        $this->assertTrue($this->app['request']->agent()->isMobile());
        $this->assertEquals('Apple', $this->app['request']->agent()->deviceBrand());
    }

    /**
     * Test Blade directives by compiling a view string
     */
    public function testBladeDirectives()
    {
        $this->app['request']->headers->set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        
        $blade = '@ifAgent("Desktop") Test Content @endifAgent';
        $expected = '<?php if (app(\'agent\')->isDesktop()): ?> Test Content <?php endif; ?>';
        
        $this->assertEquals($expected, $this->app['blade.compiler']->compileString($blade));
    }
} 