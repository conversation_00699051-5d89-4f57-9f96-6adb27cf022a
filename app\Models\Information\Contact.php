<?php
// app/Models/Information/Contact.php
namespace App\Models\Information;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use App\Models\Entity\Entity;
use App\Models\User;

/**
 * Class Contact
 *
 * Represents contact information that can be associated with both entities and users.
 * Uses polymorphic many-to-many relationships via the contactables pivot table.
 *
 * Contact information includes name, email, and phone with comprehensive validation,
 * formatting, and search capabilities. The system supports multiple contacts per
 * entity/user with primary designation. Advanced features include contact deduplication,
 * validation, and various formatting options for different regions.
 *
 * Key Features:
 * - Polymorphic many-to-many relationships with entities and users
 * - Email and phone validation with regional formatting
 * - Contact deduplication and similarity detection
 * - Comprehensive search and filtering capabilities
 * - Primary contact designation
 * - Contact completeness scoring
 * - Avatar generation from initials
 * - Audit trail and soft deletes
 *
 * @property int $id
 * @property string $name Full name of the contact person
 * @property string|null $title Formal title (Mr., Ms., Dr., etc.)
 * @property string|null $position Job title or role
 * @property string|null $email Email address of the contact
 * @property string|null $phone Phone number of the contact
 * @property bool $is_active Status: true=active, false=inactive
 * @property int $created_by User ID who created this record
 * @property int|null $updated_by User ID who last updated this record
 * @property int|null $deleted_by User ID who soft deleted this record
 * @property int|null $restored_by User ID who restored this record
 * @property \Carbon\Carbon|null $restored_at When the record was restored
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 *
 * // Computed Attributes
 * @property string $display_name Name with email/phone
 * @property bool $has_complete_info Has name, email, and phone
 * @property string $initials Initials for avatar
 * @property string|null $formatted_phone Formatted phone number
 * @property string $first_name First part of name
 * @property string $last_name Last part of name
 * @property bool $has_email Has valid email
 * @property bool $has_phone Has valid phone
 * @property int $completeness_score Completeness score (0-100)
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder withEmail()
 * @method static \Illuminate\Database\Eloquent\Builder withPhone()
 * @method static \Illuminate\Database\Eloquent\Builder complete()
 * @method static \Illuminate\Database\Eloquent\Builder search(string $search)
 * @method static \Illuminate\Database\Eloquent\Builder byDomain(string $domain)
 */
class Contact extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'contacts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'title',
        'position',
        'email',
        'phone',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'restored_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array<string>
     */
    protected $hidden = [
        'deleted_by',
        'restored_by',
        'restored_at'
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'display_name',
        'has_complete_info',
        'initials',
        'formatted_phone',
        'first_name',
        'last_name',
        'has_email',
        'has_phone',
        'completeness_score'
    ];

    /**
     * Common business email domains.
     *
     * @var array<string>
     */
    const BUSINESS_EMAIL_DOMAINS = [
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
        'rediffmail.com', 'ymail.com', 'live.com', 'protonmail.com'
    ];

    /**
     * Indian mobile number prefixes.
     *
     * @var array<string>
     */
    const INDIAN_MOBILE_PREFIXES = [
        '6', '7', '8', '9' // Indian mobile numbers start with 6, 7, 8, or 9
    ];

    // ===== ACCESSORS =====

    /**
     * Get the contact's display name with email/phone.
     *
     * @return string
     */
    public function getDisplayNameAttribute(): string
    {
        $contact = $this->name;

        if ($this->email) {
            $contact .= " ({$this->email})";
        } elseif ($this->phone) {
            $contact .= " ({$this->formatted_phone})";
        }

        return $contact;
    }

    /**
     * Check if contact has complete information.
     *
     * @return bool
     */
    public function getHasCompleteInfoAttribute(): bool
    {
        return !empty($this->name) && !empty($this->email) && !empty($this->phone);
    }

    /**
     * Get contact's initials for avatar.
     *
     * @return string
     */
    public function getInitialsAttribute(): string
    {
        if (empty($this->name)) {
            return '??';
        }

        $names = explode(' ', trim($this->name));
        $initials = '';

        foreach ($names as $name) {
            if (!empty($name)) {
                $initials .= strtoupper($name[0]);
                if (strlen($initials) >= 2) break; // Max 2 initials
            }
        }

        return strlen($initials) > 0 ? $initials : '??';
    }

    /**
     * Format phone number for display.
     *
     * @return string|null
     */
    public function getFormattedPhoneAttribute(): ?string
    {
        if (!$this->phone) {
            return null;
        }

        return $this->formatPhoneNumber($this->phone);
    }

    /**
     * Get first name from full name.
     *
     * @return string
     */
    public function getFirstNameAttribute(): string
    {
        if (empty($this->name)) {
            return '';
        }

        $parts = explode(' ', trim($this->name));
        return $parts[0] ?? '';
    }

    /**
     * Get last name from full name.
     *
     * @return string
     */
    public function getLastNameAttribute(): string
    {
        if (empty($this->name)) {
            return '';
        }

        $parts = explode(' ', trim($this->name));
        if (count($parts) <= 1) {
            return '';
        }

        return end($parts);
    }

    /**
     * Check if contact has email.
     *
     * @return bool
     */
    public function getHasEmailAttribute(): bool
    {
        return !empty($this->email) && $this->isValidEmail();
    }

    /**
     * Check if contact has phone.
     *
     * @return bool
     */
    public function getHasPhoneAttribute(): bool
    {
        return !empty($this->phone) && $this->isValidPhone();
    }

    /**
     * Get contact completeness score (0-100).
     *
     * @return int
     */
    public function getCompletenessScoreAttribute(): int
    {
        $score = 0;
        $totalFields = 3;

        if (!empty($this->name)) $score++;
        if ($this->has_email) $score++;
        if ($this->has_phone) $score++;

        return round(($score / $totalFields) * 100);
    }

    /**
     * Get email domain.
     *
     * @return string|null
     */
    public function getEmailDomainAttribute(): ?string
    {
        if (!$this->email) {
            return null;
        }

        $parts = explode('@', $this->email);
        return count($parts) === 2 ? strtolower($parts[1]) : null;
    }

    /**
     * Check if email is from a business domain.
     *
     * @return bool
     */
    public function getIsBusinessEmailAttribute(): bool
    {
        if (!$this->email_domain) {
            return false;
        }

        return !in_array($this->email_domain, self::BUSINESS_EMAIL_DOMAINS);
    }

    // ===== HELPER METHODS =====

    /**
     * Validate email format.
     *
     * @return bool
     */
    public function isValidEmail(): bool
    {
        if (empty($this->email)) {
            return false;
        }

        return filter_var($this->email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate phone number format.
     *
     * @param string $country Country code for validation
     * @return bool
     */
    public function isValidPhone(string $country = 'IN'): bool
    {
        if (empty($this->phone)) {
            return false;
        }

        return $this->validatePhoneNumber($this->phone, $country);
    }

    /**
     * Format phone number based on country.
     *
     * @param string $phone
     * @param string $country
     * @return string
     */
    protected function formatPhoneNumber(string $phone, string $country = 'IN'): string
    {
        // Remove all non-numeric characters
        $cleaned = preg_replace('/[^0-9]/', '', $phone);

        return match (strtoupper($country)) {
            'IN', 'INDIA' => $this->formatIndianPhone($cleaned),
            'US', 'USA' => $this->formatUSPhone($cleaned),
            'UK', 'GB' => $this->formatUKPhone($cleaned),
            default => $this->formatGenericPhone($cleaned)
        };
    }

    /**
     * Format Indian phone number.
     *
     * @param string $phone
     * @return string
     */
    protected function formatIndianPhone(string $phone): string
    {
        // Remove country code if present
        if (strlen($phone) === 12 && substr($phone, 0, 2) === '91') {
            $phone = substr($phone, 2);
        }
        if (strlen($phone) === 13 && substr($phone, 0, 3) === '091') {
            $phone = substr($phone, 3);
        }

        // Format 10-digit mobile number
        if (strlen($phone) === 10 && in_array($phone[0], self::INDIAN_MOBILE_PREFIXES)) {
            return '+91 ' . substr($phone, 0, 5) . ' ' . substr($phone, 5);
        }

        // Format landline (area code + number)
        if (strlen($phone) >= 7 && strlen($phone) <= 11) {
            return '+91 ' . $phone;
        }

        return $phone;
    }

    /**
     * Format US phone number.
     *
     * @param string $phone
     * @return string
     */
    protected function formatUSPhone(string $phone): string
    {
        if (strlen($phone) === 10) {
            return '+1 (' . substr($phone, 0, 3) . ') ' . substr($phone, 3, 3) . '-' . substr($phone, 6);
        }
        if (strlen($phone) === 11 && $phone[0] === '1') {
            $phone = substr($phone, 1);
            return '+1 (' . substr($phone, 0, 3) . ') ' . substr($phone, 3, 3) . '-' . substr($phone, 6);
        }

        return $phone;
    }

    /**
     * Format UK phone number.
     *
     * @param string $phone
     * @return string
     */
    protected function formatUKPhone(string $phone): string
    {
        // Basic UK formatting - mobile numbers
        if (strlen($phone) === 11 && $phone[0] === '7') {
            return '+44 ' . substr($phone, 0, 4) . ' ' . substr($phone, 4, 3) . ' ' . substr($phone, 7);
        }

        return '+44 ' . $phone;
    }

    /**
     * Format generic phone number.
     *
     * @param string $phone
     * @return string
     */
    protected function formatGenericPhone(string $phone): string
    {
        // Basic formatting with spaces every 3-4 digits
        if (strlen($phone) >= 10) {
            return chunk_split($phone, 3, ' ');
        }

        return $phone;
    }

    /**
     * Validate phone number for specific country.
     *
     * @param string $phone
     * @param string $country
     * @return bool
     */
    protected function validatePhoneNumber(string $phone, string $country = 'IN'): bool
    {
        $cleaned = preg_replace('/[^0-9]/', '', $phone);

        return match (strtoupper($country)) {
            'IN', 'INDIA' => $this->isValidIndianPhone($cleaned),
            'US', 'USA' => $this->isValidUSPhone($cleaned),
            'UK', 'GB' => $this->isValidUKPhone($cleaned),
            default => strlen($cleaned) >= 7 && strlen($cleaned) <= 15
        };
    }

    /**
     * Validate Indian phone number.
     *
     * @param string $phone
     * @return bool
     */
    protected function isValidIndianPhone(string $phone): bool
    {
        // Remove country code if present
        if (strlen($phone) === 12 && substr($phone, 0, 2) === '91') {
            $phone = substr($phone, 2);
        }
        if (strlen($phone) === 13 && substr($phone, 0, 3) === '091') {
            $phone = substr($phone, 3);
        }

        // Mobile number validation (10 digits starting with 6, 7, 8, or 9)
        if (strlen($phone) === 10 && in_array($phone[0], self::INDIAN_MOBILE_PREFIXES)) {
            return true;
        }

        // Landline validation (7-11 digits)
        if (strlen($phone) >= 7 && strlen($phone) <= 11) {
            return true;
        }

        return false;
    }

    /**
     * Validate US phone number.
     *
     * @param string $phone
     * @return bool
     */
    protected function isValidUSPhone(string $phone): bool
    {
        // 10 digits or 11 digits starting with 1
        return (strlen($phone) === 10) || (strlen($phone) === 11 && $phone[0] === '1');
    }

    /**
     * Validate UK phone number.
     *
     * @param string $phone
     * @return bool
     */
    protected function isValidUKPhone(string $phone): bool
    {
        // Basic UK validation - mobile (11 digits starting with 7) or landline
        return strlen($phone) >= 10 && strlen($phone) <= 11;
    }

    /**
     * Standardize contact information.
     *
     * @return self
     */
    public function standardize(): self
    {
        // Standardize name (proper case)
        if ($this->name) {
            $this->name = ucwords(strtolower(trim($this->name)));
        }

        // Standardize email (lowercase)
        if ($this->email) {
            $this->email = strtolower(trim($this->email));
        }

        // Clean phone number
        if ($this->phone) {
            $this->phone = preg_replace('/[^0-9+]/', '', $this->phone);
        }

        return $this;
    }

    /**
     * Check similarity with another contact.
     *
     * @param Contact $other
     * @return float Similarity score (0-1)
     */
    public function similarityTo(Contact $other): float
    {
        $nameScore = 0;
        $emailScore = 0;
        $phoneScore = 0;

        // Name similarity
        if ($this->name && $other->name) {
            similar_text(strtolower($this->name), strtolower($other->name), $namePercent);
            $nameScore = $namePercent / 100;
        }

        // Email similarity (exact match or domain match)
        if ($this->email && $other->email) {
            if ($this->email === $other->email) {
                $emailScore = 1.0;
            } elseif ($this->email_domain === $other->email_domain) {
                $emailScore = 0.5;
            }
        }

        // Phone similarity (cleaned comparison)
        if ($this->phone && $other->phone) {
            $cleanPhone1 = preg_replace('/[^0-9]/', '', $this->phone);
            $cleanPhone2 = preg_replace('/[^0-9]/', '', $other->phone);
            
            if ($cleanPhone1 === $cleanPhone2) {
                $phoneScore = 1.0;
            } elseif (strlen($cleanPhone1) >= 7 && strlen($cleanPhone2) >= 7) {
                // Compare last 7 digits for partial match
                $suffix1 = substr($cleanPhone1, -7);
                $suffix2 = substr($cleanPhone2, -7);
                if ($suffix1 === $suffix2) {
                    $phoneScore = 0.8;
                }
            }
        }

        // Weighted average
        $weights = ['name' => 0.4, 'email' => 0.4, 'phone' => 0.2];
        return ($nameScore * $weights['name']) + 
               ($emailScore * $weights['email']) + 
               ($phoneScore * $weights['phone']);
    }

    // ===== POLYMORPHIC RELATIONSHIPS =====

    /**
     * Get all entities that have this contact.
     *
     * @return MorphToMany
     */
    public function entities(): MorphToMany
    {
        return $this->morphedByMany(Entity::class, 'contactable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all users that have this contact.
     *
     * @return MorphToMany
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'contactable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all models (entities and users) that have this contact.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllContactables()
    {
        return collect()
            ->merge($this->entities)
            ->merge($this->users);
    }

    /**
     * Check if this contact is primary for any model.
     *
     * @return bool
     */
    public function isPrimaryForAny(): bool
    {
        $entityPrimary = $this->entities()->wherePivot('is_primary', true)->exists();
        $userPrimary = $this->users()->wherePivot('is_primary', true)->exists();

        return $entityPrimary || $userPrimary;
    }

    /**
     * Get count of entities using this contact.
     *
     * @return int
     */
    public function getEntityCount(): int
    {
        return $this->entities()->count();
    }

    /**
     * Get count of users using this contact.
     *
     * @return int
     */
    public function getUserCount(): int
    {
        return $this->users()->count();
    }

    /**
     * Get total usage count.
     *
     * @return int
     */
    public function getTotalUsageCount(): int
    {
        return $this->getEntityCount() + $this->getUserCount();
    }

    // ===== AUDIT RELATIONSHIPS =====

    /**
     * Get the user who created this contact.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this contact.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who soft deleted this contact.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored this contact.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to only active contacts.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to contacts with email.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithEmail($query)
    {
        return $query->whereNotNull('email')->where('email', '!=', '');
    }

    /**
     * Scope to contacts with phone.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithPhone($query)
    {
        return $query->whereNotNull('phone')->where('phone', '!=', '');
    }

    /**
     * Scope to contacts with complete information.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeComplete($query)
    {
        return $query->whereNotNull('name')
            ->where('name', '!=', '')
            ->withEmail()
            ->withPhone();
    }

    /**
     * Scope to contacts with business emails.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBusinessEmails($query)
    {
        $domains = implode('|', self::BUSINESS_EMAIL_DOMAINS);
        return $query->whereNotNull('email')
            ->whereRaw("email NOT REGEXP '@({$domains})$'");
    }

    /**
     * Scope to contacts by email domain.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $domain
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByDomain($query, string $domain)
    {
        return $query->where('email', 'like', "%@{$domain}");
    }

    /**
     * Scope to search contacts by name, email, or phone.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('email', 'like', "%{$search}%")
                ->orWhere('phone', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to contacts with mobile numbers.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMobileNumbers($query)
    {
        $prefixes = implode('|', self::INDIAN_MOBILE_PREFIXES);
        return $query->whereNotNull('phone')
            ->whereRaw("REGEXP_REPLACE(phone, '[^0-9]', '') REGEXP '^([+]?91)?[{$prefixes}][0-9]{9}$'");
    }

    /**
     * Scope to contacts with landline numbers.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLandlineNumbers($query)
    {
        return $query->withPhone()->whereRaw("
            CHAR_LENGTH(REGEXP_REPLACE(phone, '[^0-9]', '')) BETWEEN 7 AND 11 
            AND REGEXP_REPLACE(phone, '[^0-9]', '') NOT REGEXP '^([+]?91)?[6789]'
        ");
    }

    // ===== STATIC METHODS =====

    /**
     * Find contact by email or phone.
     *
     * @param string $identifier
     * @return self|null
     */
    public static function findByEmailOrPhone(string $identifier): ?self
    {
        // Clean phone identifier
        $cleanedPhone = preg_replace('/[^0-9]/', '', $identifier);
        
        return self::where('email', $identifier)
            ->orWhere(function ($query) use ($cleanedPhone) {
                if (strlen($cleanedPhone) >= 7) {
                    $query->whereRaw("REGEXP_REPLACE(phone, '[^0-9]', '') = ?", [$cleanedPhone]);
                }
            })
            ->first();
    }

    /**
     * Check if email is already used by another contact.
     *
     * @param string $email
     * @param int|null $excludeId
     * @return bool
     */
    public static function emailExists(string $email, ?int $excludeId = null): bool
    {
        $query = self::where('email', strtolower(trim($email)));

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if phone is already used by another contact.
     *
     * @param string $phone
     * @param int|null $excludeId
     * @return bool
     */
    public static function phoneExists(string $phone, ?int $excludeId = null): bool
    {
        $cleanedPhone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($cleanedPhone) < 7) {
            return false;
        }

        $query = self::whereRaw("REGEXP_REPLACE(phone, '[^0-9]', '') = ?", [$cleanedPhone]);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Find similar contacts.
     *
     * @param string $name
     * @param string|null $email
     * @param string|null $phone
     * @param float $threshold Similarity threshold (0-1)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findSimilar(
        string $name,
        ?string $email = null,
        ?string $phone = null,
        float $threshold = 0.7
    ) {
        $tempContact = new self(['name' => $name, 'email' => $email, 'phone' => $phone]);
        
        return self::active()
            ->get()
            ->filter(function ($contact) use ($tempContact, $threshold) {
                return $contact->similarityTo($tempContact) >= $threshold;
            });
    }

    /**
     * Get popular email domains.
     *
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public static function getPopularEmailDomains(int $limit = 20)
    {
        return Cache::remember('popular_email_domains', 3600, function () use ($limit) {
            return self::withEmail()
                ->selectRaw("SUBSTRING_INDEX(email, '@', -1) as domain, COUNT(*) as count")
                ->groupBy('domain')
                ->orderByDesc('count')
                ->limit($limit)
                ->pluck('count', 'domain');
        });
    }

    /**
     * Get contacts by first letter of name.
     *
     * @param string $letter
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByFirstLetter(string $letter)
    {
        return self::active()
            ->where('name', 'like', strtoupper($letter) . '%')
            ->orWhere('name', 'like', strtolower($letter) . '%')
            ->orderBy('name')
            ->get();
    }

    /**
     * Bulk validate contacts.
     *
     * @param array $contacts Array of contact data
     * @return array Validation results
     */
    public static function bulkValidate(array $contacts): array
    {
        $results = [];
        
        foreach ($contacts as $index => $contactData) {
            $validator = Validator::make($contactData, [
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'phone' => 'nullable|string|max:20'
            ]);

            $results[$index] = [
                'valid' => $validator->passes(),
                'errors' => $validator->errors()->toArray(),
                'data' => $contactData
            ];
        }

        return $results;
    }

    /**
     * Generate random contact for testing.
     *
     * @return array
     */
    public static function generateRandom(): array
    {
        $firstNames = ['Raj', 'Priya', 'Amit', 'Sunita', 'Vikram', 'Anita', 'Suresh', 'Kavita'];
        $lastNames = ['Sharma', 'Patel', 'Singh', 'Kumar', 'Gupta', 'Agarwal', 'Jain', 'Shah'];
        $domains = ['gmail.com', 'yahoo.com', 'company.com', 'business.in'];

        $firstName = $firstNames[array_rand($firstNames)];
        $lastName = $lastNames[array_rand($lastNames)];
        $domain = $domains[array_rand($domains)];

        return [
            'name' => "{$firstName} {$lastName}",
            'email' => strtolower("{$firstName}.{$lastName}@{$domain}"),
            'phone' => '+91 ' . rand(70000, 99999) . ' ' . rand(10000, 99999)
        ];
    }

    // ===== MODEL EVENTS =====

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        // Auto-standardize contact on creating/updating
        static::creating(function ($contact) {
            $contact->standardize();
        });

        static::updating(function ($contact) {
            $contact->standardize();
        });

        // Clear cache when contacts change
        static::saved(function ($contact) {
            Cache::forget('popular_email_domains');
        });

        static::deleted(function ($contact) {
            Cache::forget('popular_email_domains');
        });
    }
}