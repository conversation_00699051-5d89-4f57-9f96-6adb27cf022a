<?php

namespace Tests\Feature;

use App\Agent\Agent;
use Tests\TestCase;

class AgentSimpleTest extends TestCase
{
    /**
     * Test basic Agent functionality
     */
    public function testBasicAgentFunctionality()
    {
        $agent = new Agent(['HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36']);
        
        $this->assertEquals('Chrome', $agent->browser());
        $this->assertEquals('Windows', $agent->platform());
        $this->assertEquals('Desktop', $agent->deviceType());
        
        $agent->setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1');
        
        $this->assertTrue($agent->isMobile());
        $this->assertEquals('iOS', $agent->platform());
        $this->assertEquals('Apple', $agent->deviceBrand());
    }
    
    /**
     * Test that the Agent class provides correct information for various browsers
     */
    public function testMultipleBrowsers()
    {
        $browsers = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36' => 'Chrome',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0' => 'Firefox',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0' => 'Edge',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15' => 'Safari',
        ];
        
        $agent = new Agent();
        
        foreach ($browsers as $userAgent => $expectedBrowser) {
            $agent->setUserAgent($userAgent);
            $this->assertEquals($expectedBrowser, $agent->browser());
        }
    }
    
    /**
     * Test device type detection
     */
    public function testDeviceTypes()
    {
        $devices = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1' => 'Mobile',
            'Mozilla/5.0 (iPad; CPU OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1' => 'Tablet',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36' => 'Desktop',
            'Mozilla/5.0 (SMART-TV; Linux; Tizen 5.0) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/2.2 Chrome/63.0.3239.84 TV Safari/537.36' => 'TV',
            'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)' => 'Bot',
        ];
        
        $agent = new Agent();
        
        foreach ($devices as $userAgent => $expectedType) {
            $agent->setUserAgent($userAgent);
            $this->assertEquals($expectedType, $agent->deviceType());
        }
    }
} 