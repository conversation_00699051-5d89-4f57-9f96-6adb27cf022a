@props([
    'size' => 'base',
    'accent' => false,
    'level' => null,
])

@php
$baseClasses = 'font-medium';

$accentClasses = $accent 
    ? 'text-blue-600 dark:text-blue-400' 
    : 'text-zinc-800 dark:text-white';

$sizeClasses = match ($size) {
    'xl' => 'text-2xl mb-2',
    'lg' => 'text-xl mb-2',
    default => 'text-base mb-2',
};

$classes = "$baseClasses $accentClasses $sizeClasses";
@endphp

@switch((int) $level)
    @case(1)
        <h1 {{ $attributes->merge(['class' => $classes]) }} data-flux-heading>{{ $slot }}</h1>
        @break
    @case(2)
        <h2 {{ $attributes->merge(['class' => $classes]) }} data-flux-heading>{{ $slot }}</h2>
        @break
    @case(3)
        <h3 {{ $attributes->merge(['class' => $classes]) }} data-flux-heading>{{ $slot }}</h3>
        @break
    @case(4)
        <h4 {{ $attributes->merge(['class' => $classes]) }} data-flux-heading>{{ $slot }}</h4>
        @break
    @default
        <div {{ $attributes->merge(['class' => $classes]) }} data-flux-heading>{{ $slot }}</div>
@endswitch
