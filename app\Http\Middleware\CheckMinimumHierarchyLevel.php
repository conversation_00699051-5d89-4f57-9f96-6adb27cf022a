<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckMinimumHierarchyLevel
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, int $minLevel): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $userHierarchyLevel = session('active_role_level');
        $isSuperAdmin = session('is_super_admin', false);

        // Super Admin bypasses all hierarchy checks
        if ($isSuperAdmin) {
            return $next($request);
        }

        // Check if user meets minimum hierarchy level (lower number = higher privilege)
        if (!$userHierarchyLevel || $userHierarchyLevel > $minLevel) {
            abort(403, 'Insufficient privileges for this action.');
        }

        return $next($request);
    }
}
