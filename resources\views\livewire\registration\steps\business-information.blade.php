<div>
    <div class="max-w-2xl mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Business Information</h3>
        
        <form wire:submit.prevent="saveAndContinue">
            {{-- Business Name --}}
            <div class="mb-6">
                <label for="entityName" class="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                <input 
                    wire:model="entityName" 
                    type="text"
                    id="entityName"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Enter your business legal name"
                >
                @error('entityName') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
            </div>
            
            {{-- Website URL --}}
            <div class="mb-6">
                <label for="website" class="block text-sm font-medium text-gray-700 mb-1">Website URL (Optional)</label>
                <input 
                    wire:model="website" 
                    type="url"
                    id="website"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="https://www.example.com"
                >
                @error('website') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
            </div>
            
            {{-- Logo Upload (Optional) --}}
            <div class="mb-6">
                <label for="logo" class="block text-sm font-medium text-gray-700 mb-1">Business Logo (Optional)</label>
                <input 
                    wire:model="logo" 
                    type="file"
                    id="logo"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    accept="image/*"
                >
                <p class="mt-1 text-xs text-gray-500">Recommended size: 200x200 pixels. Max file size: 2MB.</p>
                @error('logo') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                
                @if($logo)
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">Preview:</p>
                        <img src="{{ $logo->temporaryUrl() }}" class="mt-1 h-20 w-20 object-cover rounded-md border border-gray-300">
                    </div>
                @endif
            </div>
            
            {{-- Contacts Section --}}
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-medium">Business Contacts</h4>
                    @if(count($contacts) < 4)
                        <button 
                            type="button"
                            wire:click="addContact" 
                            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Add Contact
                        </button>
                    @endif
                </div>
                
                @foreach($contacts as $index => $contact)
                    <div class="border rounded-lg p-4 mb-4 {{ $index === 0 ? 'border-blue-300 bg-blue-50' : 'border-gray-300' }}">
                        @if($index === 0)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-2">
                                Primary Contact
                            </span>
                        @endif
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="contact_name_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Contact Name</label>
                                <input 
                                    wire:model="contacts.{{ $index }}.name" 
                                    type="text"
                                    id="contact_name_{{ $index }}"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    placeholder="Full name"
                                >
                                @error('contacts.'.$index.'.name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="contact_email_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Email (Optional)</label>
                                <input 
                                    wire:model="contacts.{{ $index }}.email" 
                                    type="email"
                                    id="contact_email_{{ $index }}"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    placeholder="<EMAIL>"
                                >
                                @error('contacts.'.$index.'.email') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="contact_phone_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                <input 
                                    wire:model="contacts.{{ $index }}.phone" 
                                    type="tel"
                                    id="contact_phone_{{ $index }}"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    placeholder="9876543210"
                                    maxlength="10"
                                >
                                @error('contacts.'.$index.'.phone') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                                <label for="contact_title_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Title (Optional)</label>
                                <input 
                                    wire:model="contacts.{{ $index }}.title" 
                                    type="text"
                                    id="contact_title_{{ $index }}"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    placeholder="Mr., Ms., Dr., etc."
                                    maxlength="20"
                                >
                                @error('contacts.'.$index.'.title') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="contact_position_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Position (Optional)</label>
                                <input 
                                    wire:model="contacts.{{ $index }}.position" 
                                    type="text"
                                    id="contact_position_{{ $index }}"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    placeholder="Job title or role"
                                    maxlength="100"
                                >
                                @error('contacts.'.$index.'.position') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                        </div>
                        
                        @if($index > 0)
                            <button 
                                type="button"
                                wire:click="removeContact({{ $index }})" 
                                class="mt-3 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs leading-4 font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                                <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                Remove
                            </button>
                        @endif
                    </div>
                @endforeach
                
                @error('contacts') 
                    <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">{{ $message }}</p>
                            </div>
                        </div>
                    </div>
                @enderror
            </div>
            
            {{-- Addresses Section --}}
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-medium">Business Addresses</h4>
                    @if(count($addresses) < 4)
                        <button 
                            type="button"
                            wire:click="addAddress" 
                            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Add Address
                        </button>
                    @endif
                </div>
                
                @foreach($addresses as $index => $address)
                    <div class="border rounded-lg p-4 mb-4 {{ $index === 0 ? 'border-blue-300 bg-blue-50' : 'border-gray-300' }}">
                        @if($index === 0)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-2">
                                Primary Address
                            </span>
                        @endif
                        
                        <div class="space-y-4">
                            <div>
                                <label for="address_line1_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                                <input 
                                    wire:model="addresses.{{ $index }}.address_line1" 
                                    type="text"
                                    id="address_line1_{{ $index }}"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    placeholder="Street address"
                                >
                                @error('addresses.'.$index.'.address_line1') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="address_line2_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Address Line 2 (Optional)</label>
                                <input 
                                    wire:model="addresses.{{ $index }}.address_line2" 
                                    type="text"
                                    id="address_line2_{{ $index }}"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    placeholder="Apartment, suite, etc."
                                >
                                @error('addresses.'.$index.'.address_line2') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label for="address_state_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">State</label>
                                    <select 
                                        wire:model.live="addresses.{{ $index }}.state" 
                                        id="address_state_{{ $index }}"
                                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                                    >
                                        <option value="">Select State</option>
                                        @foreach($states as $state)
                                            <option value="{{ $state['code'] }}">{{ $state['name'] }}</option>
                                        @endforeach
                                    </select>
                                    @error('addresses.'.$index.'.state') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                                
                                <div>
                                    <label for="address_city_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                    <select 
                                        wire:model="addresses.{{ $index }}.city" 
                                        id="address_city_{{ $index }}"
                                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                                        {{ empty($addresses[$index]['state']) ? 'disabled' : '' }}
                                    >
                                        <option value="">Select City</option>
                                        @if(isset($citiesData[$index]))
                                            @foreach($citiesData[$index] as $city)
                                                <option value="{{ $city }}">{{ $city }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('addresses.'.$index.'.city') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                                
                                <div>
                                    <label for="address_postal_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                                    <input 
                                        wire:model="addresses.{{ $index }}.postal_code" 
                                        type="text"
                                        id="address_postal_{{ $index }}"
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        placeholder="123456"
                                        maxlength="6"
                                    >
                                    @error('addresses.'.$index.'.postal_code') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                            </div>
                        </div>
                        
                        @if($index > 0)
                            <button 
                                type="button"
                                wire:click="removeAddress({{ $index }})" 
                                class="mt-3 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs leading-4 font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                                <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                Remove
                            </button>
                        @endif
                    </div>
                @endforeach
                
                @error('addresses') 
                    <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">{{ $message }}</p>
                            </div>
                        </div>
                    </div>
                @enderror
            </div>
            
            {{-- Navigation --}}
            <div class="flex justify-between">
                <button 
                    type="button"
                    wire:click="stepBack" 
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    Previous
                </button>
                
                <button 
                    type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    Continue
                    <svg class="w-5 h-5 ml-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div> 