{{-- resources/views/livewire/settings/appearance.blade.php - Enhanced Version --}}
<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Appearance')" :subheading="__('Update the appearance settings for your account')">
        <div class="space-y-6">
            <!-- Theme Selection -->
            <div>
                <flux:label class="mb-3">{{ __('Theme Preference') }}</flux:label>
                <flux:description class="mb-4">
                    {{ __('Choose your preferred color scheme. System will automatically switch based on your device settings.') }}
                </flux:description>

                <flux:radio.group x-data variant="segmented" x-model="$flux.appearance" class="w-full">
                    <flux:radio value="light" icon="sun">{{ __('Light') }}</flux:radio>
                    <flux:radio value="dark" icon="moon">{{ __('Dark') }}</flux:radio>
                    <flux:radio value="system" icon="computer-desktop">{{ __('System') }}</flux:radio>
                </flux:radio.group>
            </div>

            <!-- Theme Preview -->
            <div class="border border-zinc-200 rounded-lg p-4 dark:border-zinc-700">
                <flux:label class="mb-3">{{ __('Preview') }}</flux:label>
                <div class="grid gap-4 sm:grid-cols-2">
                    <!-- Light Preview -->
                    <div class="relative overflow-hidden rounded-lg border border-zinc-200 bg-white">
                        <div class="bg-zinc-50 px-3 py-2 border-b border-zinc-200">
                            <div class="flex items-center gap-2">
                                <div class="h-2 w-2 rounded-full bg-red-400"></div>
                                <div class="h-2 w-2 rounded-full bg-yellow-400"></div>
                                <div class="h-2 w-2 rounded-full bg-green-400"></div>
                            </div>
                        </div>
                        <div class="p-3 space-y-2">
                            <div class="h-3 w-20 bg-zinc-800 rounded"></div>
                            <div class="h-2 w-full bg-zinc-200 rounded"></div>
                            <div class="h-2 w-3/4 bg-zinc-200 rounded"></div>
                        </div>
                        <div class="absolute bottom-2 left-3 text-xs text-zinc-600">{{ __('Light') }}</div>
                    </div>

                    <!-- Dark Preview -->
                    <div class="relative overflow-hidden rounded-lg border border-zinc-700 bg-zinc-900">
                        <div class="bg-zinc-800 px-3 py-2 border-b border-zinc-700">
                            <div class="flex items-center gap-2">
                                <div class="h-2 w-2 rounded-full bg-red-400"></div>
                                <div class="h-2 w-2 rounded-full bg-yellow-400"></div>
                                <div class="h-2 w-2 rounded-full bg-green-400"></div>
                            </div>
                        </div>
                        <div class="p-3 space-y-2">
                            <div class="h-3 w-20 bg-zinc-100 rounded"></div>
                            <div class="h-2 w-full bg-zinc-700 rounded"></div>
                            <div class="h-2 w-3/4 bg-zinc-700 rounded"></div>
                        </div>
                        <div class="absolute bottom-2 left-3 text-xs text-zinc-400">{{ __('Dark') }}</div>
                    </div>
                </div>
            </div>

            <!-- Additional Appearance Settings -->
            <div class="border-t border-zinc-200 pt-6 dark:border-zinc-700">
                <flux:label class="mb-3">{{ __('Advanced Settings') }}</flux:label>

                <div class="space-y-4">
                    <!-- Reduced Motion -->
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:label>{{ __('Reduce Motion') }}</flux:label>
                            <flux:description class="text-sm">
                                {{ __('Minimize animations and transitions for better performance') }}
                            </flux:description>
                        </div>
                        <flux:switch />
                    </div>

                    <!-- High Contrast -->
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:label>{{ __('High Contrast') }}</flux:label>
                            <flux:description class="text-sm">
                                {{ __('Increase contrast for better visibility') }}
                            </flux:description>
                        </div>
                        <flux:switch />
                    </div>

                    <!-- Compact Mode -->
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:label>{{ __('Compact Mode') }}</flux:label>
                            <flux:description class="text-sm">
                                {{ __('Use smaller spacing and compact layouts') }}
                            </flux:description>
                        </div>
                        <flux:switch />
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="border-t border-zinc-200 pt-6 dark:border-zinc-700">
                <flux:label class="mb-3">{{ __('Quick Actions') }}</flux:label>
                <div class="flex flex-wrap gap-2">
                    <flux:button variant="ghost" size="sm">
                        <span class="mr-2">🌅</span>
                        {{ __('Light Mode') }}
                    </flux:button>
                    <flux:button variant="ghost" size="sm">
                        <span class="mr-2">🌙</span>
                        {{ __('Dark Mode') }}
                    </flux:button>
                    <flux:button variant="ghost" size="sm">
                        <span class="mr-2">⚡</span>
                        {{ __('Auto Switch') }}
                    </flux:button>
                </div>
            </div>
        </div>
    </x-settings.layout>
</section>