<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Auth;
use App\Models\MagicLink;
use App\Mail\MagicLinkMail;
use App\Models\User;
use App\Notifications\MagicLinkUsed;

class MagicLinkController extends Controller
{
    /**
     * Generate a magic link and send it to the provided email.
     */
    public function generate(Request $request)
    {
        $request->validate([
            'validity_minutes' => 'required|integer|min:1|max:1440',
            'email' => 'required|email',
        ]);

        $user = Auth::user();
        $email = $request->input('email');
        $validity = (int) $request->input('validity_minutes');
        $expiresAt = now()->addMinutes($validity);
        
        // Enhanced token generation with additional entropy
        $randomString = Str::random(32);
        $userPart = $user ? substr(md5($user->id . $user->email), 0, 8) : Str::random(8);
        $timePart = substr(md5(now()->timestamp), 0, 8);
        $token = $randomString . $userPart . $timePart;

        $magicLink = MagicLink::create([
            'user_id' => $user ? $user->id : null,
            'email' => $email,
            'token' => hash('sha256', $token),
            'expires_at' => $expiresAt,
            'created_ip' => $request->ip(),
            'validity_in_minutes' => $validity,
        ]);

        $magicLinkUrl = URL::to('/magic-link/' . $token);

        // Send the email
        Mail::to($email)->send(new MagicLinkMail($magicLinkUrl, $expiresAt));

        // Show the link to the user as well
        return redirect()->back()->with([
            'magic_link_url' => $magicLinkUrl,
            'magic_link_expires_at' => $expiresAt->format('Y-m-d H:i'),
        ]);
    }

    /**
     * Consume a magic link and log the user in if valid.
     */
    public function consume(Request $request, $token)
    {
        $hashedToken = hash('sha256', $token);
        $magicLink = MagicLink::where('token', $hashedToken)
            ->whereNull('used_at')
            ->where('expires_at', '>', now())
            ->whereNull('cancelled_at')
            ->first();

        if (!$magicLink) {
            return view('auth.magic-link-invalid');
        }

        // Mark as used
        $magicLink->used_at = now();
        $magicLink->save();

        // Log in the user if possible
        $user = $magicLink->user;
        if ($user) {
            Auth::login($user);
            // Set session variables for forced logout enforcement
            session(['magic_link_id' => $magicLink->id]);
            session(['magic_link_login_time' => now()]);
            // Create login history and set history_id
            $loginHistory = app(\App\Services\LoginTrackingService::class)->recordSuccessfulLogin(
                $request,
                $user->id,
                null,
                false,
                null,
                null
            );
            $magicLink->history_id = $loginHistory->id;
            $magicLink->save();
            
            // Send notification about magic link usage
            $user->notify(new MagicLinkUsed($magicLink, $request->ip()));
            
            return redirect()->route('dashboard')->with('status', 'Logged in via magic link!');
        }

        // If no user, just show a message
        return view('auth.magic-link-success');
    }
    
    /**
     * Cancel a magic link to prevent its future use.
     *
     * @param Request $request
     * @param int $id The ID of the magic link to cancel
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $id)
    {
        $magicLink = MagicLink::findOrFail($id);
        
        // Authorization check
        if ($magicLink->user_id !== auth()->id() && !auth()->user()->hasRole(['admin', 'super_admin'])) {
            abort(403, 'Unauthorized action.');
        }
        
        // Only cancel if not already used or expired or cancelled
        if (!$magicLink->isUsed() && !$magicLink->isExpired() && !$magicLink->isCancelled()) {
            $magicLink->cancelled_at = now();
            $magicLink->save();
            return redirect()->back()->with('status', 'Magic link has been cancelled successfully.');
        }
        
        // Return with appropriate message based on the link's status
        if ($magicLink->isUsed()) {
            return redirect()->back()->with('error', 'This magic link has already been used and cannot be cancelled.');
        } elseif ($magicLink->isExpired()) {
            return redirect()->back()->with('error', 'This magic link has already expired and cannot be cancelled.');
        } else {
            return redirect()->back()->with('error', 'This magic link has already been cancelled.');
        }
    }
} 