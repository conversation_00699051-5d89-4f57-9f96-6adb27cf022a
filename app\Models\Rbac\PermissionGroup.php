<?php

// app/Models/Rbac/PermissionGroup.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Entity\Entity;
use App\Traits\HasAuditFields;

/**
 * Class PermissionGroup
 *
 * Groups related permissions together for easier management.
 *
 * @property int $id
 * @property int|null $entity_id Associated entity identifier
 * @property string $group_name Permission bundle name
 * @property array|null $permission_ids Array of permission IDs
 * @property \Carbon\Carbon|null $active_from Group activation date
 * @property \Carbon\Carbon|null $active_until Group expiration date
 * @property bool $is_active Activation status
 * @property string|null $notes Administrative notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class PermissionGroup extends Model
{
    use HasAuditFields;

    protected $fillable = [
        'entity_id',
        'group_name',
        'permission_ids',
        'active_from',
        'active_until',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'permission_ids' => 'array',
        'active_from' => 'date',
        'active_until' => 'date',
        'is_active' => 'boolean',
    ];

    protected $appends = ['permission_count', 'is_expired'];

    public function getPermissionCountAttribute(): int
    {
        return count($this->permission_ids ?? []);
    }

    public function getIsExpiredAttribute(): bool
    {
        return $this->active_until && $this->active_until->isPast();
    }

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    /**
     * Get permissions in this group.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPermissions()
    {
        if (empty($this->permission_ids)) {
            return collect();
        }

        return SystemPermission::whereIn('id', $this->permission_ids)->get();
    }

    // ===== QUERY SCOPES =====

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('active_until')
                ->orWhere('active_until', '>', now());
        });
    }
}
