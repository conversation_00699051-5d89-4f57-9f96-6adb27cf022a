<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\Auth\RegistrationAttempt;
use Illuminate\Support\Facades\DB;
use App\Services\RegistrationService;
use Illuminate\Support\Facades\App;

class ReviewSubmit extends Component
{
    public $attempt;
    public $stagesData;
    public $termsAccepted = false;
    public $privacyAccepted = false;
    
    protected $rules = [
        'termsAccepted' => 'accepted',
        'privacyAccepted' => 'accepted'
    ];
    
    protected $messages = [
        'termsAccepted.accepted' => 'You must accept the terms and conditions to proceed',
        'privacyAccepted.accepted' => 'You must accept the privacy policy to proceed'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        $this->stagesData = json_decode($this->attempt->stages_data, true) ?? [];
    }
    
    public function submitRegistration()
    {
        $this->validate();
        
        try {
            // Mark review step as completed
            $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
            if (!in_array('review_submit', $stagesCompleted)) {
                $stagesCompleted[] = 'review_submit';
            }
            
            $this->attempt->update([
                'stages_completed' => json_encode($stagesCompleted),
                'current_stage' => 'review_submit'
            ]);
            
            // Process registration through service
            $registrationService = App::make(RegistrationService::class);
            $result = $registrationService->completeRegistration($this->attempt->id);
            
            if ($result) {
                // Clean up session
                session()->forget('registration_attempt_id');
                
                // Redirect to completion page
                return redirect()->route('entity.register.complete');
            } else {
                $this->addError('form', 'There was an error processing your registration.');
            }
        } catch (\Exception $e) {
            $this->addError('form', 'An unexpected error occurred: ' . $e->getMessage());
        }
    }
    
    public function render()
    {
        return view('livewire.registration.steps.review-submit');
    }
} 