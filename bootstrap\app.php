<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\EnsureUserIsActive;
use App\Http\Middleware\CheckActiveSessions;
use App\Http\Middleware\CheckUserHasAnyRole;
use App\Http\Middleware\CheckMinimumHierarchyLevel;
use App\Http\Middleware\LoginRateLimiter;
use App\Http\Middleware\UpdateLastActivity;
use App\Http\Middleware\EnforceMagicLinkValidity;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/health',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Add custom middleware
        $middleware->web(LoginRateLimiter::class);
        $middleware->web(UpdateLastActivity::class);
        $middleware->web(EnforceMagicLinkValidity::class);
        
        // Register middleware aliases
        $middleware->alias([
            'active' => EnsureUserIsActive::class,
            'check.sessions' => CheckActiveSessions::class,
            'check.roles' => CheckUserHasAnyRole::class,
            'update.activity' => UpdateLastActivity::class,
            'min.hierarchy' => CheckMinimumHierarchyLevel::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->create();
