<x-layouts.app :title="__('Dashboard')">
    <div class="flex h-full w-full flex-1 flex-col gap-6 p-6">
        <!-- Dashboard Header -->
        <header class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold text-zinc-900 dark:text-zinc-100">{{ __('Dashboard') }}</h1>
                <p class="mt-1 text-sm text-zinc-600 dark:text-zinc-400">{{ __('Welcome back! Here\'s an overview of your account.') }}</p>
            </div>
            <div class="flex items-center gap-3">
                <flux:button variant="outline" size="sm" icon="plus">
                    {{ __('New Item') }}
                </flux:button>
            </div>
        </header>

        <!-- Stats Cards -->
        <section aria-label="{{ __('Dashboard Statistics') }}">
            <div class="grid auto-rows-min gap-4 md:grid-cols-3">
                <article class="relative aspect-video overflow-hidden rounded-xl border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900 transition-colors hover:border-zinc-300 dark:hover:border-zinc-600">
                    <div class="p-6 h-full flex flex-col justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-zinc-600 dark:text-zinc-400">{{ __('Total Users') }}</h3>
                            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100 mt-2">1,234</p>
                        </div>
                        <div class="text-xs text-zinc-500 dark:text-zinc-500">
                            <span class="text-green-600 dark:text-green-400">+12%</span> {{ __('from last month') }}
                        </div>
                    </div>
                    <x-placeholder-pattern class="absolute inset-0 size-full stroke-zinc-900/5 dark:stroke-zinc-100/5 -z-10" />
                </article>

                <article class="relative aspect-video overflow-hidden rounded-xl border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900 transition-colors hover:border-zinc-300 dark:hover:border-zinc-600">
                    <div class="p-6 h-full flex flex-col justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-zinc-600 dark:text-zinc-400">{{ __('Active Sessions') }}</h3>
                            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100 mt-2">567</p>
                        </div>
                        <div class="text-xs text-zinc-500 dark:text-zinc-500">
                            <span class="text-blue-600 dark:text-blue-400">+5%</span> {{ __('from last week') }}
                        </div>
                    </div>
                    <x-placeholder-pattern class="absolute inset-0 size-full stroke-zinc-900/5 dark:stroke-zinc-100/5 -z-10" />
                </article>

                <article class="relative aspect-video overflow-hidden rounded-xl border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900 transition-colors hover:border-zinc-300 dark:hover:border-zinc-600">
                    <div class="p-6 h-full flex flex-col justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-zinc-600 dark:text-zinc-400">{{ __('Revenue') }}</h3>
                            <p class="text-2xl font-bold text-zinc-900 dark:text-zinc-100 mt-2">$89,012</p>
                        </div>
                        <div class="text-xs text-zinc-500 dark:text-zinc-500">
                            <span class="text-green-600 dark:text-green-400">+8%</span> {{ __('from last month') }}
                        </div>
                    </div>
                    <x-placeholder-pattern class="absolute inset-0 size-full stroke-zinc-900/5 dark:stroke-zinc-100/5 -z-10" />
                </article>
            </div>
        </section>

        <!-- Main Content Area -->
        <section class="flex-1" aria-label="{{ __('Main Dashboard Content') }}">
            <div class="relative h-full overflow-hidden rounded-xl border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
                <div class="p-6 h-full flex flex-col">
                    <h2 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">{{ __('Recent Activity') }}</h2>
                    <div class="flex-1 flex items-center justify-center">
                        <div class="text-center">
                            <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-zinc-100 dark:bg-zinc-800 flex items-center justify-center">
                                <flux:icon name="layout-grid" class="w-8 h-8 text-zinc-400 dark:text-zinc-500" />
                            </div>
                            <h3 class="text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-1">{{ __('No recent activity') }}</h3>
                            <p class="text-xs text-zinc-500 dark:text-zinc-400">{{ __('Activity will appear here when available') }}</p>
                        </div>
                    </div>
                </div>
                <x-placeholder-pattern class="absolute inset-0 size-full stroke-zinc-900/5 dark:stroke-zinc-100/5 -z-10" />
            </div>
        </section>
    </div>
</x-layouts.app>
