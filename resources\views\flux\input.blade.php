@props([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'type' => 'text',
    'invalid' => null,
    'size' => 'base',
    'iconLeading' => null,
    'iconTrailing' => null,
])

@php
$invalid ??= ($name && $errors->has($name));

$sizeClasses = match ($size) {
    'sm' => 'px-3 py-1.5 text-sm',
    'lg' => 'px-4 py-3 text-lg',
    default => 'px-3 py-2 text-sm',
};

$baseClasses = 'block w-full rounded-lg border shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';

$stateClasses = $invalid 
    ? 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600 dark:text-red-100 dark:placeholder-red-400 dark:focus:border-red-500'
    : 'border-zinc-300 text-zinc-900 placeholder-zinc-400 focus:border-blue-500 focus:ring-blue-500 dark:border-zinc-600 dark:bg-zinc-800 dark:text-zinc-100 dark:placeholder-zinc-400 dark:focus:border-blue-400';

$classes = "$baseClasses $sizeClasses $stateClasses";

$hasIcons = $iconLeading || $iconTrailing;
@endphp

@if($hasIcons)
<div class="relative">
    @if($iconLeading)
    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        {{ $iconLeading }}
    </div>
    @endif
    
    <input 
        {{ $attributes->merge([
            'type' => $type,
            'class' => $classes . ($iconLeading ? ' pl-10' : '') . ($iconTrailing ? ' pr-10' : '')
        ]) }}
    />
    
    @if($iconTrailing)
    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        {{ $iconTrailing }}
    </div>
    @endif
</div>
@else
<input 
    {{ $attributes->merge([
        'type' => $type,
        'class' => $classes
    ]) }}
/>
@endif
