<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Session Transfer Settings
    |--------------------------------------------------------------------------
    |
    | This file contains configuration settings for session transfers between
    | devices. These settings control the behavior of the transfer process.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Transfer Code Expiration
    |--------------------------------------------------------------------------
    |
    | The number of seconds a transfer code remains valid. After this period,
    | the code will expire and can no longer be used to transfer a session.
    | Default is 600 seconds (10 minutes).
    |
    */
    'expiration_time' => env('SESSION_TRANSFER_EXPIRATION', 600),

    /*
    |--------------------------------------------------------------------------
    | Maximum Attempt Count
    |--------------------------------------------------------------------------
    |
    | The maximum number of times a user can attempt to use a transfer code
    | before it becomes invalid. This helps prevent brute force attacks.
    | Default is 3 attempts.
    |
    */
    'max_attempts' => env('SESSION_TRANSFER_MAX_ATTEMPTS', 3),

    /*
    |--------------------------------------------------------------------------
    | Transfer Code Length
    |--------------------------------------------------------------------------
    |
    | The length of the generated transfer code. Longer codes are more secure
    | but harder for users to type correctly. Default is 8 characters.
    |
    */
    'code_length' => env('SESSION_TRANSFER_CODE_LENGTH', 8),

    /*
    |--------------------------------------------------------------------------
    | Transfer Code Format
    |--------------------------------------------------------------------------
    |
    | The format of the transfer code. Options are:
    | - 'alphanumeric': A mix of letters and numbers
    | - 'numeric': Numbers only
    | - 'uppercase': Uppercase letters only
    | Default is 'uppercase'.
    |
    */
    'code_format' => env('SESSION_TRANSFER_CODE_FORMAT', 'uppercase'),

    /*
    |--------------------------------------------------------------------------
    | Cache Store
    |--------------------------------------------------------------------------
    |
    | The cache store to use for storing transfer codes. This should match
    | one of the stores defined in config/cache.php. Default is 'file'.
    |
    */
    'cache_store' => env('SESSION_TRANSFER_CACHE_STORE', null),

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    |
    | The prefix for cache keys used to store transfer codes. This helps
    | avoid collisions with other cached items. Default is 'session_transfer:'.
    |
    */
    'cache_key_prefix' => env('SESSION_TRANSFER_CACHE_PREFIX', 'session_transfer:'),
]; 