<?php
// app/Models/Rbac/AccessGuardType.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Entity\Entity;
use App\Traits\HasAuditFields;

/**
 * Class AccessGuardType
 *
 * Defines different types of authentication guards (web, api, mobile).
 *
 * @property int $id
 * @property int|null $entity_id Associated entity identifier
 * @property string $guard_name Guard identifier
 * @property string|null $description Guard purpose description
 * @property bool $is_active Activation status
 * @property bool $is_approval_required Flag indicating if approval is needed
 * @property string $approval_status Current approval status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class AccessGuardType extends Model
{
    use HasAuditFields;

    protected $fillable = [
        'entity_id',
        'guard_name',
        'description',
        'is_active',
        'is_approval_required',
        'approval_status',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
    ];

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function userAccess(): HasMany
    {
        return $this->hasMany(UserGuardAccess::class, 'guard_id');
    }

    public function permissionGrants(): HasMany
    {
        return $this->hasMany(PermissionGrant::class, 'guard_id');
    }

    // ===== QUERY SCOPES =====

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }
}
