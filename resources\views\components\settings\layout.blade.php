{{-- resources/views/components/settings/layout.blade.php - Enhanced Version --}}
@props(['heading', 'subheading'])

<div class="h-full w-full p-6">
    <!-- Breadcrumb Navigation -->
    <x-breadcrumb :items="[
        ['label' => __('Dashboard'), 'url' => route('dashboard')],
        ['label' => __('Settings'), 'url' => route('settings.profile')]
    ]" />

    <!-- Settings Grid Layout -->
    <div class="grid gap-6 lg:grid-cols-4">
        <!-- Left Navigation -->
        <div class="lg:col-span-1">
            <nav class="space-y-1" aria-label="{{ __('Settings Navigation') }}">
                <a href="{{ route('settings.profile') }}"
                    class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('settings.profile') ? 'bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-zinc-100' : 'text-zinc-600 hover:bg-zinc-50 hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-100' }}"
                    wire:navigate>
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    {{ __('Profile') }}
                </a>

                <a href="{{ route('settings.password') }}"
                    class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('settings.password') ? 'bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-zinc-100' : 'text-zinc-600 hover:bg-zinc-50 hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-100' }}"
                    wire:navigate>
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    {{ __('Password') }}
                </a>

                <a href="{{ route('settings.appearance') }}"
                    class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors {{ request()->routeIs('settings.appearance') ? 'bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-zinc-100' : 'text-zinc-600 hover:bg-zinc-50 hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-800 dark:hover:text-zinc-100' }}"
                    wire:navigate>
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4a2 2 0 012-2z" />
                    </svg>
                    {{ __('Appearance') }}
                </a>
            </nav>
        </div>

        <!-- Right Content Area -->
        <div class="lg:col-span-3">
            <!-- Settings Card -->
            <flux:card>
                <flux:card.header>
                    <flux:heading size="lg">{{ $heading }}</flux:heading>
                    <flux:subheading>{{ $subheading }}</flux:subheading>
                </flux:card.header>

                <flux:card.body>
                    {{ $slot }}
                </flux:card.body>
            </flux:card>
        </div>
    </div>
</div>