<div class="flex flex-col gap-6">
    <x-auth-header :title="__('Log in to your account')" :description="__('Enter your email and password below to log in')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    @if($showEntitySelector)
        <section class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800" aria-labelledby="entity-selector-heading">
            <h2 id="entity-selector-heading" class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">{{ __('Super Administrator Access') }}</h2>
            <p class="text-sm text-blue-700 dark:text-blue-300 mb-6">
                {{ __('Please select which entity you would like to access, or skip to access the portal without entity restriction:') }}
            </p>
            
            <form wire:submit="continueLogin" class="flex flex-col gap-6" aria-label="{{ __('Entity Selection Form') }}">
                <!-- Entity Selection -->
                <div class="space-y-2">
                    <label for="entityId" class="block text-sm font-medium text-blue-900 dark:text-blue-100">{{ __('Entity') }}</label>
                    <select wire:model="entityId"
                            wire:change="selectEntity"
                            id="entityId"
                            class="w-full rounded-lg border border-blue-200 dark:border-blue-700 bg-white dark:bg-blue-900/50 px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-blue-50 dark:focus:ring-offset-blue-900/20"
                            aria-describedby="entityId-error">
                        <option value="">{{ __('-- Select Entity --') }}</option>
                        @foreach($availableEntities as $entity)
                            <option value="{{ $entity['entity_id'] }}">{{ $entity['entity_name'] }} ({{ $entity['entity_type'] }})</option>
                        @endforeach
                    </select>
                    @error('entityId')
                        <p id="entityId-error" class="text-red-600 dark:text-red-400 text-sm" role="alert">{{ $message }}</p>
                    @enderror
                </div>
                
                <!-- Role Selection (if entity is selected) -->
                @if($entityId && count($availableRoles) > 0)
                    <div class="space-y-2">
                        <label for="roleId" class="block text-sm font-medium text-blue-900 dark:text-blue-100">{{ __('Role (Optional)') }}</label>
                        <select wire:model="roleId"
                                id="roleId"
                                class="w-full rounded-lg border border-blue-200 dark:border-blue-700 bg-white dark:bg-blue-900/50 px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-blue-50 dark:focus:ring-offset-blue-900/20"
                                aria-describedby="roleId-error">
                            <option value="">{{ __('-- Super Administrator --') }}</option>
                            @foreach($availableRoles as $role)
                                <option value="{{ $role['id'] }}">{{ $role['role_name'] }} (Level: {{ $role['hierarchy_level'] }})</option>
                            @endforeach
                        </select>
                        @error('roleId')
                            <p id="roleId-error" class="text-red-600 dark:text-red-400 text-sm" role="alert">{{ $message }}</p>
                        @enderror
                    </div>
                @endif
                
                <!-- Session Label -->
                <flux:input
                    wire:model="sessionLabel"
                    :label="__('Session Label (Optional)')"
                    type="text"
                    placeholder="Work Laptop, Home Office, etc." />
                
                <div class="flex items-center justify-between gap-4">
                    <flux:button wire:click="skipAndContinue" variant="outline" type="button" class="w-full">
                        {{ __('Skip Entity Selection') }}
                    </flux:button>
                    <flux:button variant="primary" type="submit" class="w-full">{{ __('Continue') }}</flux:button>
                </div>
            </form>
        </section>
    @else
        <form wire:submit="login" class="flex flex-col gap-6" aria-label="{{ __('Login Form') }}">
            <!-- Email Address -->
            <flux:input
                wire:model="email"
                :label="__('Email address')"
                type="email"
                required
                autofocus
                autocomplete="email"
                placeholder="<EMAIL>" />

            <!-- Password -->
            <div class="space-y-2">
                <div class="flex items-center justify-between">
                    <label for="password" class="block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                        {{ __('Password') }}
                    </label>
                    @if (Route::has('password.request'))
                        <flux:link class="text-sm text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100 transition-colors"
                                   :href="route('password.request')"
                                   wire:navigate>
                            {{ __('Forgot your password?') }}
                        </flux:link>
                    @endif
                </div>
                <flux:input
                    wire:model="password"
                    type="password"
                    required
                    autocomplete="current-password"
                    :placeholder="__('Password')"
                    viewable />
            </div>

            <!-- Session Label (Optional) -->
            <flux:input
                wire:model="sessionLabel"
                :label="__('Session Label (Optional)')"
                type="text"
                placeholder="Work Laptop, Home Office, etc." />

            <!-- Remember Me -->
            <div class="flex items-center">
                <flux:checkbox wire:model="remember" :label="__('Remember me')" />
            </div>

            <div class="flex items-center justify-end">
                <flux:button variant="primary" type="submit" class="w-full">{{ __('Log in') }}</flux:button>
            </div>
        </form>

        <!-- Auth Links -->
        <footer class="space-y-4 text-center">
            @if (Route::has('register'))
                <div class="text-sm text-zinc-600 dark:text-zinc-400">
                    <span>{{ __('Don\'t have an account?') }}</span>
                    <flux:link :href="route('register')"
                               wire:navigate
                               class="font-medium text-zinc-900 dark:text-zinc-100 hover:text-zinc-700 dark:hover:text-zinc-300 transition-colors">
                        {{ __('Sign up') }}
                    </flux:link>
                </div>
            @endif

            <div class="text-sm text-zinc-600 dark:text-zinc-400">
                <span>{{ __('Have a session transfer code?') }}</span>
                <flux:link :href="route('sessions.transfer')"
                           wire:navigate
                           class="font-medium text-zinc-900 dark:text-zinc-100 hover:text-zinc-700 dark:hover:text-zinc-300 transition-colors">
                    {{ __('Transfer Session') }}
                </flux:link>
            </div>
        </footer>
    @endif
</div>