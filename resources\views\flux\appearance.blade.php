{{-- resources/views/flux/appearance.blade.php --}}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Basic theme management
        const themeToggle = {
            init() {
                // Check for saved theme preference or use system preference
                const savedTheme = localStorage.getItem('theme') || 'system';
                this.setTheme(savedTheme);
                
                // Listen for system preference changes
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                    if (localStorage.getItem('theme') === 'system') {
                        this.applySystemTheme();
                    }
                });
            },
            
            setTheme(theme) {
                localStorage.setItem('theme', theme);
                
                if (theme === 'system') {
                    this.applySystemTheme();
                } else {
                    document.documentElement.classList.toggle('dark', theme === 'dark');
                }
            },
            
            applySystemTheme() {
                const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
                document.documentElement.classList.toggle('dark', isDarkMode);
            }
        };
        
        // Initialize theme
        themeToggle.init();
        
        // Make available globally
        window.$flux = window.$flux || {};
        window.$flux.appearance = localStorage.getItem('theme') || 'system';
        
        // Add listener for theme changes
        document.addEventListener('flux:theme-change', (e) => {
            themeToggle.setTheme(e.detail.theme);
        });
    });
</script> 