@props([
    'label' => null,
    'description' => null,
    'disabled' => false,
])

<div {{ $attributes->class(['flex items-start gap-3']) }}>
    <div class="flex-shrink-0 mt-0.5">
        <input
            type="checkbox"
            {{ $disabled ? 'disabled' : '' }}
            {{ $attributes->whereStartsWith('wire:')->class([
                'h-4 w-4 rounded border-zinc-300 text-primary-600 focus:ring-primary-600 dark:border-zinc-600 dark:bg-zinc-800 dark:focus:ring-primary-500',
                'cursor-not-allowed opacity-60' => $disabled,
            ])->whereDoesntStartWith(['class', 'wire:']) }}
        />
    </div>
    
    @if(isset($label) && $label || isset($slot) && $slot->isNotEmpty() || isset($description) && $description)
        <div class="flex flex-col">
            @if(isset($label) && $label || isset($slot) && $slot->isNotEmpty())
                <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                    {{ $label ?? $slot }}
                </span>
            @endif
            
            @if(isset($description) && $description)
                <span class="text-sm text-zinc-500 dark:text-zinc-400">
                    {{ $description }}
                </span>
            @endif
        </div>
    @endif
</div> 