<?php

namespace App\Livewire\Registration;

use Livewire\Component;
use Livewire\Attributes\Layout;
use App\Models\Auth\RegistrationAttempt;

#[Layout('components.layouts.auth.simple')]
class RegistrationWizard extends Component
{
    public $currentStep = 1;
    public $totalSteps = 9;
    public $attempt;
    
    public $steps = [
        1 => ['name' => 'Email Verification', 'component' => 'email-verification'],
        2 => ['name' => 'Entity Type', 'component' => 'entity-type-selection'],
        3 => ['name' => 'Business Info', 'component' => 'business-information'],
        4 => ['name' => 'Personal Details', 'component' => 'personal-details'],
        5 => ['name' => 'Tax Info', 'component' => 'tax-information'],
        6 => ['name' => 'Business KYC', 'component' => 'business-kyc-documents'],
        7 => ['name' => 'Additional Docs', 'component' => 'additional-documents'],
        8 => ['name' => 'Personal KYC', 'component' => 'personal-kyc-documents'],
        9 => ['name' => 'Review & Submit', 'component' => 'review-submit']
    ];
    
    protected $listeners = ['stepCompleted', 'stepBack', 'forceStep'];
    
    public function mount($step = null)
    {
        // \Log::debug('RegistrationWizard::mount - Starting');
        
        // Check if resuming registration
        if (session()->has('registration_attempt_id')) {
            $attemptId = session('registration_attempt_id');
            // \Log::debug("RegistrationWizard::mount - Found registration_attempt_id in session: {$attemptId}");
            
            $this->attempt = RegistrationAttempt::find($attemptId);
            if ($this->attempt) {
                // \Log::debug("RegistrationWizard::mount - Found registration attempt with ID: {$this->attempt->id}");
                // \Log::debug("RegistrationWizard::mount - Current stage: {$this->attempt->current_stage}");
                // \Log::debug("RegistrationWizard::mount - Completed stages: " . json_encode($this->attempt->completed_stages));
                
                // Set source to 'web' if not already set
                if (empty($this->attempt->source)) {
                    $this->attempt->update(['source' => 'web']);
                }
                
                $this->determineCurrentStep();
            } else {
                // \Log::warning("RegistrationWizard::mount - Registration attempt not found with ID: {$attemptId}");
            }
        } else {
            // \Log::warning("RegistrationWizard::mount - No registration_attempt_id in session");
        }
        
        // First check for route parameter
        if ($step && is_numeric($step) && $step >= 1 && $step <= $this->totalSteps) {
            $oldStep = $this->currentStep;
            $this->currentStep = (int)$step;
            // \Log::info("RegistrationWizard::mount - Step overridden from route parameter: {$oldStep} -> {$this->currentStep}");
            
            // Update the database if needed
            $this->updateDatabaseStage();
        }
        // Then check for query parameter (lower priority)
        else {
            $stepParam = request()->query('step');
            if ($stepParam && is_numeric($stepParam) && $stepParam >= 1 && $stepParam <= $this->totalSteps) {
                $oldStep = $this->currentStep;
                $this->currentStep = (int)$stepParam;
                // \Log::info("RegistrationWizard::mount - Step overridden from URL query parameter: {$oldStep} -> {$this->currentStep}");
                
                // Update the database if needed
                $this->updateDatabaseStage();
            }
        }
        
        // \Log::debug("RegistrationWizard::mount - Initial current step: {$this->currentStep}");
        // \Log::debug('RegistrationWizard::mount - Completed');
    }
    
    public function determineCurrentStep()
    {
        // \Log::debug('RegistrationWizard::determineCurrentStep - Starting');
        
        $stageToStep = [
            'email_verification' => 1,
            'entity_type_selection' => 2,
            'business_information' => 3,
            'personal_details' => 4,
            'tax_information' => 5,
            'business_kyc_documents' => 6,
            'additional_documents' => 7,
            'personal_kyc_documents' => 8,
            'review_submit' => 9
        ];
        
        $oldStep = $this->currentStep;
        $this->currentStep = $stageToStep[$this->attempt->current_stage] ?? 1;
        
        // \Log::debug("RegistrationWizard::determineCurrentStep - Stage '{$this->attempt->current_stage}' maps to step {$this->currentStep} (was {$oldStep})");
        // \Log::debug('RegistrationWizard::determineCurrentStep - Completed');
    }
    
    public function stepCompleted($stepName)
    {
        // \Log::debug("RegistrationWizard::stepCompleted - Starting with stepName: {$stepName}");
        // \Log::info("RegistrationWizard::stepCompleted - Step completed: {$stepName}, Current step: {$this->currentStep}, Total steps: {$this->totalSteps}");
        
        try {
            // Special handling for personal-kyc-documents step which has been problematic
            if ($stepName === 'personal-kyc-documents' || $stepName === 'personal_kyc_documents') {
                // \Log::debug("RegistrationWizard::stepCompleted - Detected personal KYC documents step completion");
                
                // Force step 8 to move to step 9 (Review & Submit)
                if ($this->currentStep === 8) {
                    $this->currentStep = 9;
                    // \Log::info("RegistrationWizard::stepCompleted - Forced move to step 9 (Review & Submit) from Personal KYC step");
                    
                    // Force a re-render of the component
                    $this->dispatch('refreshComponent');
                    // \Log::debug("RegistrationWizard::stepCompleted - Dispatched refreshComponent event after forced step change");
                    
                    // \Log::debug("RegistrationWizard::stepCompleted - Completed with special handling. Current step is now: {$this->currentStep}");
                    return;
                }
            }
            
            // Normalize step name (replace hyphens with underscores for consistency)
            $normalizedStepName = str_replace('-', '_', $stepName);
            // \Log::debug("RegistrationWizard::stepCompleted - Normalized step name: {$normalizedStepName}");
            
            // Get component name for the current step and normalize it too
            $currentComponent = $this->steps[$this->currentStep]['component'] ?? null;
            $normalizedCurrentComponent = str_replace('-', '_', $currentComponent);
            // \Log::debug("RegistrationWizard::stepCompleted - Current component: {$currentComponent} (normalized: {$normalizedCurrentComponent})");
            
            // Verify the step name matches the current component (for debugging)
            if ($normalizedCurrentComponent !== $normalizedStepName) {
                // \Log::warning("RegistrationWizard::stepCompleted - Step name mismatch: received '{$normalizedStepName}', expected '{$normalizedCurrentComponent}'");
            }
            
            // Make sure we don't exceed the total steps
            if ($this->currentStep < $this->totalSteps) {
                $oldStep = $this->currentStep;
                $this->currentStep++;
                // \Log::info("RegistrationWizard::stepCompleted - Moving from step {$oldStep} to step {$this->currentStep}");
                
                // Force a re-render of the component
                $this->dispatch('refreshComponent');
                // \Log::debug("RegistrationWizard::stepCompleted - Dispatched refreshComponent event");
            } else {
                // \Log::info("RegistrationWizard::stepCompleted - Already at the last step ({$this->currentStep}), not incrementing");
            }
            
            // Debug the current state after step completion
            if ($this->attempt) {
                // \Log::debug("RegistrationWizard::stepCompleted - Current registration attempt state:");
                // \Log::debug("  - ID: {$this->attempt->id}");
                // \Log::debug("  - Current stage: {$this->attempt->current_stage}");
                // \Log::debug("  - Completed stages: " . json_encode($this->attempt->completed_stages));
            } else {
                // \Log::warning("RegistrationWizard::stepCompleted - No registration attempt available");
            }
        } catch (\Exception $e) {
            // \Log::error("RegistrationWizard::stepCompleted - Error: " . $e->getMessage());
            // \Log::error("RegistrationWizard::stepCompleted - Stack trace: " . $e->getTraceAsString());
        }
        
        // \Log::debug("RegistrationWizard::stepCompleted - Completed. Current step is now: {$this->currentStep}");
    }
    
    public function stepBack()
    {
        // \Log::debug("RegistrationWizard::stepBack - Starting. Current step: {$this->currentStep}");
        
        if ($this->currentStep > 1) {
            $oldStep = $this->currentStep;
            $this->currentStep--;
            // \Log::info("RegistrationWizard::stepBack - Moving from step {$oldStep} to step {$this->currentStep}");
        } else {
            // \Log::info("RegistrationWizard::stepBack - Already at the first step, cannot go back");
        }
        
        // \Log::debug("RegistrationWizard::stepBack - Completed. Current step is now: {$this->currentStep}");
    }

    /**
     * Force the current step to a specific value
     * This can be called directly by other components
     */
    public function forceStep($step)
    {
        // \Log::debug("RegistrationWizard::forceStep - Starting with step: {$step}");
        
        if (is_numeric($step) && $step >= 1 && $step <= $this->totalSteps) {
            $oldStep = $this->currentStep;
            $this->currentStep = (int)$step;
            // \Log::info("RegistrationWizard::forceStep - Step changed: {$oldStep} -> {$this->currentStep}");
            
            // Force a re-render of the component
            $this->dispatch('refreshComponent');
            // \Log::debug("RegistrationWizard::forceStep - Dispatched refreshComponent event");
        } else {
            // \Log::warning("RegistrationWizard::forceStep - Invalid step value: {$step}");
        }
        
        // \Log::debug("RegistrationWizard::forceStep - Completed. Current step is now: {$this->currentStep}");
    }
    
    /**
     * Update the database stage based on the current step
     */
    protected function updateDatabaseStage()
    {
        // If we have an attempt, update its current_stage to match the step parameter
        if ($this->attempt) {
            // Map step numbers to stage names
            $stepToStage = [
                1 => 'email_verification',
                2 => 'entity_type_selection',
                3 => 'business_information',
                4 => 'personal_details',
                5 => 'tax_information',
                6 => 'business_kyc_documents',
                7 => 'additional_documents',
                8 => 'personal_kyc_documents',
                9 => 'review_submit'
            ];
            
            if (isset($stepToStage[$this->currentStep])) {
                $newStage = $stepToStage[$this->currentStep];
                // \Log::info("RegistrationWizard::updateDatabaseStage - Updating attempt current_stage to: {$newStage}");
                
                // Only update if the step is different
                if ($this->attempt->current_stage !== $newStage) {
                    $this->attempt->update(['current_stage' => $newStage]);
                }
            }
        }
    }
    
    public function render()
    {
        // \Log::debug("RegistrationWizard::render - Rendering with current step: {$this->currentStep}");
        return view('livewire.registration.registration-wizard');
    }
} 