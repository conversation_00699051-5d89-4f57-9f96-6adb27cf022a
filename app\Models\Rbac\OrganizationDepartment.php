<?php

// app/Models/Rbac/OrganizationDepartment.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Entity\Entity;
use App\Models\User;
use App\Traits\HasAuditFields;

/**
 * Class OrganizationDepartment
 *
 * Represents organizational departments with hierarchical structure.
 *
 * @property int $id
 * @property string $dept_name Full department name
 * @property string|null $dept_code Short department code
 * @property string|null $description Department description
 * @property string|null $email Department email
 * @property string|null $phone Department phone
 * @property int|null $parent_id Parent department ID
 * @property bool $is_active Activation status
 * @property bool $is_approval_required Flag indicating if approval is needed
 * @property string $approval_status Current approval status
 * @property int $display_order Display order
 * @property int|null $entity_id Associated entity
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class OrganizationDepartment extends Model
{
    use SoftDeletes, HasAuditFields;

    protected $fillable = [
        'dept_name',
        'dept_code',
        'description',
        'email',
        'phone',
        'parent_id',
        'is_active',
        'is_approval_required',
        'approval_status',
        'display_order',
        'entity_id',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
        'display_order' => 'integer',
        'restored_at' => 'datetime',
    ];

    protected $appends = ['is_hierarchical', 'full_name'];

    public function getIsHierarchicalAttribute(): bool
    {
        return $this->parent_id !== null || $this->children()->exists();
    }

    public function getFullNameAttribute(): string
    {
        if ($this->dept_code) {
            return "{$this->dept_name} ({$this->dept_code})";
        }
        return $this->dept_name;
    }

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(OrganizationDepartment::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(OrganizationDepartment::class, 'parent_id');
    }

    public function userAssignments(): HasMany
    {
        return $this->hasMany(UserDepartmentAssignment::class, 'department_id');
    }

    public function permissionGrants(): HasMany
    {
        return $this->hasMany(PermissionGrant::class, 'department_id');
    }

    // ===== QUERY SCOPES =====

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order')->orderBy('dept_name');
    }
}
