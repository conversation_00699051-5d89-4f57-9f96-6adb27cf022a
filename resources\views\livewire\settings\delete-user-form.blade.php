{{-- resources/views/livewire/settings/delete-user-form.blade.php - Enhanced Version --}}
<section class="mt-10 space-y-6">
    <!-- Danger Zone Header -->
    <div class="relative mb-5 p-4 border border-red-200 rounded-lg bg-red-50 dark:border-red-800 dark:bg-red-900/20">
        <div class="flex items-start gap-3">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <div>
                <flux:heading class="text-red-800 dark:text-red-200">{{ __('Danger Zone') }}</flux:heading>
                <flux:subheading class="text-red-700 dark:text-red-300">
                    {{ __('Once you delete your account, there is no going back. Please be certain.') }}
                </flux:subheading>
            </div>
        </div>
    </div>

    <!-- Delete Account Section -->
    <div class="border border-red-200 rounded-lg dark:border-red-800">
        <div class="p-4 border-b border-red-200 dark:border-red-800">
            <flux:heading class="text-red-600 dark:text-red-400">{{ __('Delete account') }}</flux:heading>
            <flux:subheading class="text-zinc-600 dark:text-zinc-400">
                {{ __('Delete your account and all of its resources') }}
            </flux:subheading>
        </div>

        <div class="p-4">
            <!-- Warning Information -->
            <div class="mb-4 space-y-2 text-sm text-zinc-600 dark:text-zinc-400">
                <p class="font-medium">{{ __('This action will permanently delete:') }}</p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li>{{ __('Your profile and account information') }}</li>
                    <li>{{ __('All your content and data') }}</li>
                    <li>{{ __('Your settings and preferences') }}</li>
                    <li>{{ __('Any associated files or uploads') }}</li>
                </ul>
            </div>

            <flux:modal.trigger name="confirm-user-deletion">
                <flux:button
                    variant="danger"
                    x-data=""
                    x-on:click.prevent="$dispatch('open-modal', 'confirm-user-deletion')"
                    class="w-full sm:w-auto">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    {{ __('Delete account') }}
                </flux:button>
            </flux:modal.trigger>
        </div>
    </div>

    <!-- Enhanced Modal -->
    <flux:modal name="confirm-user-deletion" :show="$errors->isNotEmpty()" focusable class="max-w-lg">
        <form wire:submit="deleteUser" class="space-y-6 relative">
            <!-- Loading overlay for deletion -->
            <div wire:loading.flex wire:target="deleteUser"
                class="absolute inset-0 z-10 bg-white/90 dark:bg-zinc-900/90 backdrop-blur-sm rounded-lg">
                <div class="flex h-full items-center justify-center">
                    <x-loading-spinner type="spinner" size="lg" text="{{ __('Deleting account...') }}" />
                </div>
            </div>

            <!-- Modal Header -->
            <div class="text-center">
                <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
                    <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>

                <flux:heading size="lg" class="mt-4 text-red-600 dark:text-red-400">
                    {{ __('Are you absolutely sure?') }}
                </flux:heading>

                <flux:subheading class="mt-2">
                    {{ __('This action cannot be undone. This will permanently delete your account and remove all your data from our servers.') }}
                </flux:subheading>
            </div>

            <!-- Confirmation Steps -->
            <div class="bg-zinc-50 dark:bg-zinc-800 p-4 rounded-lg">
                <p class="text-sm font-medium text-zinc-900 dark:text-zinc-100 mb-2">
                    {{ __('Please type your password to confirm:') }}
                </p>
                <flux:input
                    wire:model="password"
                    :label="__('Password')"
                    type="password"
                    required
                    autocomplete="current-password"
                    wire:loading.attr="disabled"
                    wire:target="deleteUser"
                    placeholder="{{ __('Enter your password') }}" />
            </div>

            <!-- Final Warning -->
            <div class="border border-yellow-200 bg-yellow-50 p-3 rounded-lg dark:border-yellow-800 dark:bg-yellow-900/20">
                <div class="flex items-start gap-2">
                    <svg class="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        {{ __('This will immediately log you out and delete your account. You will not be able to recover any data.') }}
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-2 rtl:space-x-reverse">
                <flux:modal.close>
                    <flux:button
                        variant="filled"
                        wire:loading.attr="disabled"
                        wire:target="deleteUser">
                        {{ __('Cancel') }}
                    </flux:button>
                </flux:modal.close>

                <flux:button
                    variant="danger"
                    type="submit"
                    wire:loading.attr="disabled"
                    wire:target="deleteUser">
                    <span wire:loading.remove wire:target="deleteUser">
                        {{ __('Yes, delete my account') }}
                    </span>
                    <span wire:loading wire:target="deleteUser" class="flex items-center gap-2">
                        <x-loading-spinner type="inline" />
                        {{ __('Deleting...') }}
                    </span>
                </flux:button>
            </div>
        </form>
    </flux:modal>
</section>