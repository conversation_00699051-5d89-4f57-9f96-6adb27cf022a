<?php

namespace App\Livewire\Actions;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Services\LoginTrackingService;

class Logout
{
    /**
     * Log the current user out of the application.
     */
    public function __invoke()
    {
        $userId = Auth::id();
        $sessionId = Session::getId();
        $roleId = Session::get('active_role_id');
        
        if ($userId) {
            // Record logout in the login history with role context
            app(LoginTrackingService::class)->recordLogout(
                $userId, 
                $sessionId, 
                'user_logout' . ($roleId ? '_role_' . $roleId : '')
            );
        }
        
        Auth::guard('web')->logout();

        Session::invalidate();
        Session::regenerateToken();

        return redirect('/');
    }
}
