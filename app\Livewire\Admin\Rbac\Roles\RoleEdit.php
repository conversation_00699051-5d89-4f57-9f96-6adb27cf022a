<?php

namespace App\Livewire\Admin\Rbac\Roles;

use Livewire\Component;
use App\Models\Rbac\SystemRole;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;
use Livewire\Attributes\Layout;

#[Layout('components.admin.layout')]
class RoleEdit extends Component
{
    public SystemRole $role;

    #[Validate('required|string|max:255')]
    public $role_name = '';

    #[Validate('nullable|string|max:1000')]
    public $description = '';

    #[Validate('nullable|exists:system_roles,id')]
    public $parent_role_id = null;

    #[Validate('required|integer|min:3|max:10')]
    public $hierarchy_level = 3;

    #[Validate('required|in:web,api,mobile')]
    public $guard_type = 'web';

    #[Validate('nullable|date')]
    public $active_from = null;

    #[Validate('nullable|date|after:active_from')]
    public $active_until = null;

    public function mount(SystemRole $role)
    {
        // Check if user can edit this role
        $this->checkAuthorization('update', $role);
        
        $this->role = $role;
        $this->role_name = $role->role_name;
        $this->description = $role->description;
        $this->parent_role_id = $role->parent_role_id;
        $this->hierarchy_level = $role->hierarchy_level;
        $this->guard_type = $role->guard_type;
        $this->active_from = $role->active_from?->toDateString();
        $this->active_until = $role->active_until?->toDateString();
    }

    public function getAvailableParentRolesProperty()
    {
        $currentUserLevel = session('active_role_level', 999);
        
        return SystemRole::where('entity_id', Auth::user()->entity_id)
            ->where('hierarchy_level', '<', $this->hierarchy_level)
            ->where('hierarchy_level', '>', $currentUserLevel)
            ->where('id', '!=', $this->role->id) // Exclude self
            ->operational()
            ->orderBy('hierarchy_level')
            ->get();
    }

    public function save()
    {
        $this->validate();

        // Additional business logic validation
        $this->validateHierarchyLevel();

        $this->role->update([
            'role_name' => $this->role_name,
            'description' => $this->description,
            'parent_role_id' => $this->parent_role_id,
            'hierarchy_level' => $this->hierarchy_level,
            'guard_type' => $this->guard_type,
            'active_from' => $this->active_from,
            'active_until' => $this->active_until,
            'updated_by' => Auth::id(),
        ]);

        session()->flash('success', __('Role updated successfully.'));
        
        return $this->redirect(route('admin.rbac.roles.index'), navigate: true);
    }

    private function validateHierarchyLevel()
    {
        $currentUserLevel = session('active_role_level', 999);
        
        if ($this->hierarchy_level <= $currentUserLevel) {
            $this->addError('hierarchy_level', 
                __('You can only edit roles with lower privilege levels than your own.'));
        }
    }

    private function checkAuthorization($action, $role)
    {
        $currentUserLevel = session('active_role_level', 999);
        $isSuperAdmin = session('is_super_admin', false);
        
        // Super Admin can edit any role
        if ($isSuperAdmin) {
            return;
        }
        
        // Users can only edit roles from their entity with lower privilege levels
        if ($role->entity_id !== Auth::user()->entity_id || 
            $role->hierarchy_level <= $currentUserLevel) {
            abort(403, 'Unauthorized to edit this role.');
        }
    }

    public function render()
    {
        return view('livewire.admin.rbac.roles.role-edit', [
            'availableParentRoles' => $this->availableParentRoles,
            'role' => $this->role
        ])->with([
            'heading' => __('Edit Role: :name', ['name' => $this->role->role_name]),
            'subheading' => __('Modify role settings and permissions'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Roles'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Edit'), 'url' => route('admin.rbac.roles.edit', $this->role)]
            ]
        ]);
    }
}
