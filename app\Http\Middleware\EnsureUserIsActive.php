<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Services\LoginValidationService;
use App\Services\LoginTrackingService;

class EnsureUserIsActive
{
    protected LoginValidationService $loginValidator;
    protected LoginTrackingService $loginTracker;
    
    /**
     * Create a new middleware instance.
     *
     * @param LoginValidationService $loginValidator
     * @param LoginTrackingService $loginTracker
     */
    public function __construct(LoginValidationService $loginValidator, LoginTrackingService $loginTracker)
    {
        $this->loginValidator = $loginValidator;
        $this->loginTracker = $loginTracker;
    }
    
    /**
     * Handle an incoming request.
     * Ensures that authenticated user has an active account.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check active status if user is authenticated
        if (Auth::check()) {
            $user = Auth::user();
            
            // Use the comprehensive validation service
            $validationResult = $this->loginValidator->checkUserStatus($user);
            
            if ($validationResult !== true) {
                // Record the logout
                $this->loginTracker->recordLogout($user->id, session()->getId(), $validationResult['error']);
                
                // Log the user out
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                // If we have a specific redirect, use it
                if (isset($validationResult['redirect'])) {
                    return redirect()->route($validationResult['redirect']);
                }
                
                // Otherwise redirect to login with status message
                return redirect()->route('login')->with('status', $validationResult['message']);
            }
            
            // Also check entity status
            $entityCheck = $this->loginValidator->checkEntityStatus($user);
            if ($entityCheck !== true) {
                // Record the logout
                $this->loginTracker->recordLogout($user->id, session()->getId(), $entityCheck['error']);
                
                // Log the user out
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                return redirect()->route('login')->with('status', $entityCheck['message']);
            }
        }

        return $next($request);
    }
} 