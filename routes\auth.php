<?php

use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\Auth\SessionController;
use App\Http\Controllers\Auth\SessionDashboardController;
use App\Livewire\Auth\ConfirmPassword;
use App\Livewire\Auth\ForgotPassword;
use App\Livewire\Auth\Login;
use App\Livewire\Auth\ResetPassword;
use App\Livewire\Auth\VerifyEmail;
use Illuminate\Support\Facades\Route;

Route::middleware('guest')->group(function () {
    Route::get('login', Login::class)->name('login');
    // Default register route removed to avoid conflicts with entity registration
    Route::get('forgot-password', ForgotPassword::class)->name('password.request');
    Route::get('reset-password/{token}', ResetPassword::class)->name('password.reset');
    
    // Session Transfer page (public)
    Route::get('transfer-session', App\Livewire\Auth\SessionTransfer::class)->name('sessions.transfer');
});

Route::middleware('auth')->group(function () {
    Route::get('verify-email', VerifyEmail::class)
        ->name('verification.notice');

    Route::get('verify-email/{id}/{hash}', VerifyEmailController::class)
        ->middleware(['signed', 'throttle:6,1'])
        ->name('verification.verify');

    Route::get('confirm-password', ConfirmPassword::class)
        ->name('password.confirm');
        
    // Session management routes
    Route::get('session-management', App\Livewire\Auth\SessionManagement::class)
        ->name('auth.session-management');
    
    Route::post('terminate-session', [SessionController::class, 'terminateSession'])
        ->name('auth.terminate-session');
        
    // Role selection routes
    Route::post('select-role', [SessionController::class, 'selectRole'])
        ->name('auth.select-role');
        
    // Session Dashboard
    Route::middleware('auth')->group(function () {
        Route::get('sessions', [SessionDashboardController::class, 'index'])
            ->name('sessions.dashboard');
            
        Route::post('sessions/terminate', [SessionDashboardController::class, 'terminateSession'])
            ->name('sessions.terminate');
            
        Route::post('sessions/update-label', [SessionDashboardController::class, 'updateSessionLabel'])
            ->name('sessions.update-label');
            
        Route::post('sessions/create-transfer', [SessionDashboardController::class, 'createTransferCode'])
            ->name('sessions.create-transfer');
    });
});

Route::post('logout', App\Livewire\Actions\Logout::class)
    ->name('logout');
