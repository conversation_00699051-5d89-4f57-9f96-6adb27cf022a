<div>
    <div class="max-w-2xl mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Business KYC Documents</h3>
        
        <div class="mb-4 bg-blue-50 border-l-4 border-blue-400 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        Please upload clear, complete documents in PDF or JPG format. Maximum file size is 5MB.
                    </p>
                </div>
            </div>
        </div>
        
        <form wire:submit.prevent="saveAndContinue">
            <div class="space-y-6 mb-8">
                @foreach($documents as $index => $document)
                    <div class="bg-white border border-gray-300 rounded-lg p-5">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Document Type</label>
                            <input 
                                type="text" 
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700 sm:text-sm"
                                value="{{ $requiredDocumentTypes[$document['document_type']] ?? $document['document_type'] }}" 
                                readonly
                            >
                            <input type="hidden" wire:model="documents.{{ $index }}.document_type">
                            @error('documents.'.$index.'.document_type') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Document Number</label>
                            <input 
                                type="text"
                                wire:model="documents.{{ $index }}.document_number"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                placeholder="Enter document identification number"
                            >
                            @error('documents.'.$index.'.document_number') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date (Optional)</label>
                                <input 
                                    type="date"
                                    wire:model="documents.{{ $index }}.expiry_date"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                >
                                @error('documents.'.$index.'.expiry_date') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Verification Status</label>
                                <select 
                                    wire:model="documents.{{ $index }}.verification_status"
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                                    disabled
                                >
                                    <option value="pending">Pending Verification</option>
                                    <option value="verified">Verified</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="expired">Expired</option>
                                </select>
                                <p class="mt-1 text-xs text-gray-500">Status will be updated after review</p>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Upload Document</label>
                            
                            @if(!empty($document['document_file_path']))
                                {{-- Already uploaded file --}}
                                <div class="mt-1 flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                                        <svg class="h-6 w-6 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4 flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ basename($document['document_file_path']) }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            Document uploaded
                                        </p>
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        <button 
                                            type="button"
                                            class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                            wire:click="removeFile({{ $index }})"
                                        >
                                            Replace
                                        </button>
                                    </div>
                                </div>
                            @elseif($document['document_file'] && $document['filename'])
                                {{-- Newly selected file, not yet saved --}}
                                <div class="mt-1 flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                                        <svg class="h-6 w-6 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4 flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ $document['filename'] }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            Ready to upload
                                        </p>
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        <button 
                                            type="button"
                                            class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                            wire:click="removeFile({{ $index }})"
                                        >
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            @else
                                {{-- No file selected yet --}}
                                <div 
                                    x-data="{ isUploading: false, progress: 0 }" 
                                    x-on:livewire-upload-start="isUploading = true"
                                    x-on:livewire-upload-finish="isUploading = false"
                                    x-on:livewire-upload-error="isUploading = false"
                                    x-on:livewire-upload-progress="progress = $event.detail.progress"
                                    class="mt-1"
                                >
                                    <label class="block">
                                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                            <div class="space-y-1 text-center">
                                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                                <div class="flex text-sm text-gray-600">
                                                    <label for="file-upload-{{ $index }}" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                        <span>Upload a file</span>
                                                        <input 
                                                            id="file-upload-{{ $index }}" 
                                                            wire:model="documents.{{ $index }}.document_file" 
                                                            type="file" 
                                                            class="sr-only" 
                                                            accept=".pdf,.jpg,.jpeg"
                                                        >
                                                    </label>
                                                    <p class="pl-1">or drag and drop</p>
                                                </div>
                                                <p class="text-xs text-gray-500">
                                                    PDF or JPG up to 5MB
                                                </p>
                                            </div>
                                        </div>
                                    </label>
                                    
                                    <!-- Progress Bar -->
                                    <div x-show="isUploading" class="mt-3">
                                        <div class="bg-gray-200 rounded-full overflow-hidden">
                                            <div 
                                                class="bg-blue-600 h-2 rounded-full transition-all" 
                                                x-bind:style="`width: ${progress}%`"
                                            ></div>
                                        </div>
                                        <div class="text-xs font-semibold text-center mt-1" x-text="`Uploading: ${progress}%`"></div>
                                    </div>
                                </div>
                                
                                @error('documents.'.$index.'.document_file') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
            
            {{-- Navigation --}}
            <div class="flex justify-between">
                <button 
                    type="button"
                    wire:click="stepBack" 
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    Previous
                </button>
                
                <button 
                    type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    wire:loading.attr="disabled"
                    wire:target="saveAndContinue"
                >
                    <span wire:loading.remove wire:target="saveAndContinue">
                        Continue
                        <svg class="w-5 h-5 ml-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    <span wire:loading wire:target="saveAndContinue">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div> 