<div>
    <div class="max-w-4xl mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Review & Submit</h3>
        
        @if ($errors->has('form'))
            <flux:alert variant="danger" class="mb-6">
                {{ $errors->first('form') }}
            </flux:alert>
        @endif
        
        <div class="mb-8">
            <h4 class="text-lg font-medium mb-4">Registration Summary</h4>
            
            {{-- Email & Entity Type --}}
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500">Email</p>
                        <p class="font-medium">{{ $attempt->email }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Entity Type</p>
                        <p class="font-medium capitalize">{{ $attempt->entity_type }}</p>
                    </div>
                    
                    @if(isset($stagesData['entity_type_selection']['distributor_id']) && $stagesData['entity_type_selection']['distributor_id'])
                        <div>
                            <p class="text-sm text-gray-500">Distributor ID</p>
                            <p class="font-medium">{{ $stagesData['entity_type_selection']['distributor_id'] }}</p>
                        </div>
                    @endif
                </div>
            </div>
            
            {{-- Business Information --}}
            @if(isset($stagesData['business_information']))
                <div class="border border-gray-200 rounded-lg mb-6">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h5 class="font-medium">Business Information</h5>
                    </div>
                    <div class="p-4">
                        <p class="mb-4">
                            <span class="text-sm text-gray-500">Business Name</span>
                            <span class="block font-medium">{{ $stagesData['business_information']['entity_name'] }}</span>
                        </p>
                        
                        {{-- Contacts --}}
                        <div class="mb-4">
                            <p class="text-sm text-gray-500 mb-2">Business Contacts</p>
                            <div class="space-y-3">
                                @foreach($stagesData['business_information']['contacts'] as $index => $contact)
                                    <div class="bg-gray-50 p-3 rounded {{ $index === 0 ? 'border-l-4 border-blue-500' : '' }}">
                                        @if($index === 0)
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Primary</span>
                                        @endif
                                        <p class="font-medium">{{ $contact['name'] }}</p>
                                        <p class="text-sm">{{ $contact['phone'] }} {{ $contact['email'] ? '• ' . $contact['email'] : '' }}</p>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                        
                        {{-- Addresses --}}
                        <div>
                            <p class="text-sm text-gray-500 mb-2">Business Addresses</p>
                            <div class="space-y-3">
                                @foreach($stagesData['business_information']['addresses'] as $index => $address)
                                    <div class="bg-gray-50 p-3 rounded {{ $index === 0 ? 'border-l-4 border-blue-500' : '' }}">
                                        @if($index === 0)
                                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Primary</span>
                                        @endif
                                        <p class="font-medium">{{ $address['address_line1'] }}</p>
                                        @if($address['address_line2'])
                                            <p class="text-sm">{{ $address['address_line2'] }}</p>
                                        @endif
                                        <p class="text-sm">{{ $address['city'] }}, {{ $address['state'] }} {{ $address['postal_code'] }}</p>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endif
            
            {{-- Personal Details --}}
            @if(isset($stagesData['personal_details']))
                <div class="border border-gray-200 rounded-lg mb-6">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h5 class="font-medium">Personal Details</h5>
                    </div>
                    <div class="p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-sm text-gray-500">Owner Name</p>
                                <p class="font-medium">{{ $stagesData['personal_details']['owner_name'] }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Owner Phone</p>
                                <p class="font-medium">{{ $stagesData['personal_details']['owner_phone'] }}</p>
                            </div>
                        </div>
                        
                        <p class="text-sm text-gray-500 mb-2">Personal Contact & Address Information</p>
                        <div class="bg-gray-50 p-3 rounded mb-4">
                            <p class="font-medium">{{ $stagesData['personal_details']['personal_contact_name'] }}</p>
                            <p class="text-sm">
                                {{ $stagesData['personal_details']['personal_contact_phone'] }}
                                {{ $stagesData['personal_details']['personal_contact_email'] ? '• ' . $stagesData['personal_details']['personal_contact_email'] : '' }}
                            </p>
                            <hr class="my-2 border-gray-200">
                            <p class="text-sm">{{ $stagesData['personal_details']['personal_address_line1'] }}</p>
                            @if($stagesData['personal_details']['personal_address_line2'])
                                <p class="text-sm">{{ $stagesData['personal_details']['personal_address_line2'] }}</p>
                            @endif
                            <p class="text-sm">
                                {{ $stagesData['personal_details']['personal_city'] }},
                                {{ $stagesData['personal_details']['personal_state'] }}
                                {{ $stagesData['personal_details']['personal_postal_code'] }}
                            </p>
                        </div>
                    </div>
                </div>
            @endif
            
            {{-- Tax Information --}}
            @if(isset($stagesData['tax_information']))
                <div class="border border-gray-200 rounded-lg mb-6">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h5 class="font-medium">Tax Information</h5>
                    </div>
                    <div class="p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">Tax Type</p>
                                <p class="font-medium">{{ $stagesData['tax_information']['tax_type'] }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Tax Identifier</p>
                                <p class="font-medium">{{ $stagesData['tax_information']['tax_identifier'] }}</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">Tax Region</p>
                                <p class="font-medium">{{ $stagesData['tax_information']['tax_region'] }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
            
            {{-- Documents --}}
            @if(isset($stagesData['business_kyc_documents']) || isset($stagesData['additional_documents']) || isset($stagesData['personal_kyc_documents']))
                <div class="border border-gray-200 rounded-lg">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h5 class="font-medium">Submitted Documents</h5>
                    </div>
                    <div class="p-4">
                        @if(isset($stagesData['business_kyc_documents']))
                            <div class="mb-4">
                                <p class="text-sm text-gray-500 mb-2">Business KYC Documents</p>
                                <div class="space-y-2">
                                    @foreach($stagesData['business_kyc_documents'] as $doc)
                                        <div class="bg-gray-50 p-2 rounded flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            <div>
                                                <p class="font-medium">{{ $doc['document_type'] }}</p>
                                                <p class="text-xs text-gray-500">{{ $doc['document_number'] }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        
                        @if(isset($stagesData['personal_kyc_documents']))
                            <div>
                                <p class="text-sm text-gray-500 mb-2">Personal KYC Documents</p>
                                <div class="space-y-2">
                                    @foreach($stagesData['personal_kyc_documents'] as $doc)
                                        <div class="bg-gray-50 p-2 rounded flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            <div>
                                                <p class="font-medium">{{ $doc['document_type'] }}</p>
                                                <p class="text-xs text-gray-500">{{ $doc['document_number'] }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
        
        <form wire:submit.prevent="submitRegistration">
            {{-- Terms & Conditions --}}
            <div class="mb-6">
                <div class="space-y-4">
                    <label class="flex items-start">
                        <flux:checkbox wire:model="termsAccepted" class="mt-1" />
                        <span class="ml-3">
                            <span class="text-sm text-gray-700">
                                I agree to the 
                                <a href="#" class="text-blue-600 hover:underline">Terms and Conditions</a>
                            </span>
                            @error('termsAccepted')
                                <span class="text-red-600 text-xs block">{{ $message }}</span>
                            @enderror
                        </span>
                    </label>
                    
                    <label class="flex items-start">
                        <flux:checkbox wire:model="privacyAccepted" class="mt-1" />
                        <span class="ml-3">
                            <span class="text-sm text-gray-700">
                                I agree to the 
                                <a href="#" class="text-blue-600 hover:underline">Privacy Policy</a>
                            </span>
                            @error('privacyAccepted')
                                <span class="text-red-600 text-xs block">{{ $message }}</span>
                            @enderror
                        </span>
                    </label>
                </div>
            </div>
            
            {{-- Navigation --}}
            <div class="flex justify-between">
                <flux:button 
                    type="button"
                    wire:click="$dispatch('stepBack')" 
                    variant="secondary"
                >
                    <flux:icon.arrow-left class="w-4 h-4 mr-2" />
                    Previous
                </flux:button>
                
                <flux:button 
                    type="submit" 
                    variant="primary"
                    wire:loading.attr="disabled"
                >
                    <flux:icon.loading wire:loading wire:target="submitRegistration" class="mr-2" />
                    Submit Registration
                </flux:button>
            </div>
        </form>
    </div>
</div> 