<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class MagicLinkMail extends Mailable
{
    use Queueable, SerializesModels;

    public $magicLinkUrl;
    public $expiresAt;

    /**
     * Create a new message instance.
     */
    public function __construct(string $magicLinkUrl, $expiresAt)
    {
        $this->magicLinkUrl = $magicLinkUrl;
        $this->expiresAt = $expiresAt;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Your Magic Login Link')
            ->view('emails.magic-link')
            ->with([
                'magicLinkUrl' => $this->magicLinkUrl,
                'expiresAt' => $this->expiresAt,
            ]);
    }
} 