<?php

namespace App\Services;

use App\Models\User;
use App\Models\Entity\Entity;
use App\Models\Auth\RegistrationAttempt;
use App\Models\Information\Address;
use App\Models\Information\Contact;
use App\Models\Information\Kyc;
use App\Models\Information\Tax;
use App\Models\Entity\EntityRelationship;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\OrganizationDepartment;
use App\Models\Rbac\AccessGuardType;
use App\Models\Rbac\SystemPermission;
use App\Models\Rbac\PermissionGrant;
use App\Models\Rbac\UserRoleAssignment;
use App\Models\Rbac\UserDepartmentAssignment;
use App\Models\Rbac\UserGuardAccess;
use App\Mail\Registration\RegistrationCompleteMail;
use App\Mail\Registration\DistributorAlertMail;
use App\CodeGenerator\Facades\CodeGenerator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class RegistrationService
{
    /**
     * Complete the registration process by creating all necessary records
     * 
     * @param int $attemptId Registration attempt ID
     * @return array|bool Results of registration or false on failure
     */
    public function completeRegistration($attemptId)
    {
        $attempt = RegistrationAttempt::findOrFail($attemptId);
        $stagesData = json_decode($attempt->stages_data, true);
        
        try {
            return DB::transaction(function () use ($attempt, $stagesData) {
                // Extract all data
                $entityTypeData = $stagesData['entity_type_selection'] ?? [];
                $businessData = $stagesData['business_information'] ?? [];
                $personalData = $stagesData['personal_details'] ?? [];
                $taxData = $stagesData['tax_information'] ?? [];
                $businessKycData = $stagesData['business_kyc_documents'] ?? [];
                $personalKycData = $stagesData['personal_kyc_documents'] ?? [];
                
                // Get state from primary address for entity_id generation
                $primaryState = $businessData['addresses'][0]['state'] ?? 'XX';
                
                // 1. Create Entity
                $entity = Entity::create([
                    'entity_id' => CodeGenerator::generate($attempt->entity_type, ['location' => $primaryState]),
                    'entity_name' => $businessData['entity_name'],
                    'entity_type' => $attempt->entity_type,
                    'is_goods_provider' => true,
                    'is_service_provider' => false,
                    'is_active' => false,
                    'is_approval_required' => true,
                    'approval_status' => 'pending',
                    'logo' => $businessData['logo'] ?? null,
                    'website' => $businessData['website'] ?? null,
                    'created_by' => 1, // System user temporarily
                    'created_at' => now()
                ]);
                
                // 2. Create User
                $user = User::create([
                    'name' => $personalData['owner_name'],
                    'email' => $attempt->email,
                    'user_id' => CodeGenerator::generate('user'),
                    'phone' => $personalData['owner_phone'],
                    'entity_id' => $entity->id,
                    'email_verified_at' => $attempt->is_email_verified ? now() : null,
                    'is_active' => false,
                    'multi_login' => 1,
                    'is_approval_required' => true,
                    'approval_status' => 'pending',
                    'password' => $personalData['password'], // Already hashed
                    'created_by' => null,
                    'created_at' => now()
                ]);
                
                // Update created_by references
                $entity->update(['created_by' => $user->id]);
                $user->update(['created_by' => $user->id]);
                
                // 3. Create Entity Relationship if dealer with distributor
                if ($attempt->entity_type === 'dealer' && !empty($entityTypeData['distributor_id'])) {
                    $distributor = Entity::where('entity_id', $entityTypeData['distributor_id'])->first();
                    
                    if ($distributor) {
                        EntityRelationship::create([
                            'source_entity_id' => $distributor->id,
                            'target_entity_id' => $entity->id,
                            'relationship_type' => 'sells_to',
                            'description' => 'Dealer registered under distributor',
                            'effective_date' => now(),
                            'expiry_date' => null, // No expiration by default
                            'is_active' => false,
                            'is_approval_required' => true,
                            'approval_status' => 'pending',
                            'created_by' => $user->id
                        ]);
                    }
                }
                
                // 4. Create Contacts
                foreach ($businessData['contacts'] as $index => $contactData) {
                    $contact = Contact::create([
                        'name' => $contactData['name'],
                        'email' => $contactData['email'],
                        'phone' => $contactData['phone'],
                        'title' => $contactData['title'] ?? null,
                        'position' => $contactData['position'] ?? null,
                        'is_active' => false,
                        'created_by' => $user->id,
                        'created_at' => now()
                    ]);
                    
                    // Link to entity
                    DB::table('contactables')->insert([
                        'contact_id' => $contact->id,
                        'contactable_type' => 'App\\Models\\Entity\\Entity',
                        'contactable_id' => $entity->id,
                        'is_primary' => $index === 0,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                
                // Create personal contact for user
                $personalContact = Contact::create([
                    'name' => $personalData['personal_contact_name'],
                    'email' => $personalData['personal_contact_email'],
                    'phone' => $personalData['personal_contact_phone'],
                    'title' => $personalData['personal_contact_title'] ?? null,
                    'position' => $personalData['personal_contact_position'] ?? null,
                    'is_active' => false,
                    'created_by' => $user->id,
                    'created_at' => now()
                ]);
                
                DB::table('contactables')->insert([
                    'contact_id' => $personalContact->id,
                    'contactable_type' => 'App\\Models\\User',
                    'contactable_id' => $user->id,
                    'is_primary' => true,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                
                // 5. Create Addresses
                foreach ($businessData['addresses'] as $index => $addressData) {
                    $address = Address::create([
                        'address_line1' => $addressData['address_line1'],
                        'address_line2' => $addressData['address_line2'],
                        'state' => $addressData['state'],
                        'city' => $addressData['city'],
                        'postal_code' => $addressData['postal_code'],
                        'is_primary' => $index === 0,
                        'is_active' => false,
                        'created_by' => $user->id,
                        'created_at' => now()
                    ]);
                    
                    // Link to entity
                    DB::table('addressables')->insert([
                        'address_id' => $address->id,
                        'addressable_type' => 'App\\Models\\Entity\\Entity',
                        'addressable_id' => $entity->id,
                        'is_primary' => $index === 0,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                
                // Create personal address for user
                $personalAddress = Address::create([
                    'address_line1' => $personalData['personal_address_line1'],
                    'address_line2' => $personalData['personal_address_line2'],
                    'state' => $personalData['personal_state'],
                    'city' => $personalData['personal_city'],
                    'postal_code' => $personalData['personal_postal_code'],
                    'is_primary' => true,
                    'is_active' => false,
                    'created_by' => $user->id,
                    'created_at' => now()
                ]);
                
                DB::table('addressables')->insert([
                    'address_id' => $personalAddress->id,
                    'addressable_type' => 'App\\Models\\User',
                    'addressable_id' => $user->id,
                    'is_primary' => true,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                
                // 6. Create Tax Information
                if (!empty($taxData)) {
                    $tax = Tax::create([
                        'tax_identifier' => $taxData['tax_identifier'],
                        'tax_type' => $taxData['tax_type'],
                        'tax_region' => $taxData['tax_region'],
                        'effective_date' => $taxData['effective_date'] ?? now(),
                        'expiry_date' => $taxData['expiry_date'] ?? null,
                        'is_active' => false,
                        'created_by' => $user->id,
                        'created_at' => now()
                    ]);
                    
                    DB::table('taxables')->insert([
                        'tax_id' => $tax->id,
                        'taxable_type' => 'App\\Models\\Entity\\Entity',
                        'taxable_id' => $entity->id,
                        'is_primary' => true,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                
                // 7. Create KYC Documents
                // Business KYCs
                foreach ($businessKycData as $kycData) {
                    $kyc = Kyc::create([
                        'document_type' => $kycData['document_type'],
                        'document_number' => $kycData['document_number'],
                        'document_file' => $kycData['document_file_path'] ?? null,
                        'expiry_date' => $kycData['expiry_date'] ?? null,
                        'verification_status' => $kycData['verification_status'] ?? 'pending',
                        'is_active' => false,
                        'created_by' => $user->id,
                        'created_at' => now()
                    ]);
                    
                    DB::table('kycables')->insert([
                        'kyc_id' => $kyc->id,
                        'kycable_type' => 'App\\Models\\Entity\\Entity',
                        'kycable_id' => $entity->id,
                        'is_primary' => true,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                
                // Personal KYCs
                foreach ($personalKycData as $kycData) {
                    $kyc = Kyc::create([
                        'document_type' => $kycData['type'] ?? 'other',
                        'document_number' => $kycData['document_number'] ?? 'DOC-' . substr(md5($kycData['filename']), 0, 8),
                        'document_file' => $kycData['filename'] ?? null,
                        'expiry_date' => $kycData['expiry_date'] ?? null,
                        'verification_status' => $kycData['verification_status'] ?? 'pending',
                        'is_active' => false,
                        'created_by' => $user->id,
                        'created_at' => now()
                    ]);
                    
                    DB::table('kycables')->insert([
                        'kyc_id' => $kyc->id,
                        'kycable_type' => 'App\\Models\\User',
                        'kycable_id' => $user->id,
                        'is_primary' => true,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                
                // 8. Create RBAC Structure
                $this->createRBACStructure($entity, $user);
                
                // 9. Mark registration as submitted
                $attempt->update([
                    'is_submitted' => true,
                    'submitted_at' => now(),
                    'user_id' => $user->id,
                    'success' => true
                ]);
                
                // 10. Send emails
                try {
                    Mail::to($user->email)->send(new RegistrationCompleteMail($entity, $user));
                    
                    // Send distributor alert if applicable
                    if ($attempt->entity_type === 'dealer' && !empty($entityTypeData['distributor_id'])) {
                        $distributor = Entity::where('entity_id', $entityTypeData['distributor_id'])->first();
                        if ($distributor) {
                            $this->sendDistributorAlert($distributor, $entity);
                        }
                    }
                } catch (\Exception $e) {
                    // Log::error('Registration email error: ' . $e->getMessage());
                }
                
                return [
                    'entity' => $entity,
                    'user' => $user
                ];
            });
        } catch (\Exception $e) {
            // Log::error('Registration error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create RBAC structure for the new entity and user
     */
    protected function createRBACStructure($entity, $user)
    {
        // Create role
        $role = SystemRole::create([
            'entity_id' => $entity->id,
            'role_name' => 'Administrator Manager',
            'description' => 'Top-level entity role with full access within its own entity.',
            'parent_role_id' => null,
            'hierarchy_level' => 2,
            'guard_type' => 'web',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'notes' => 'One per entity'
        ]);
        
        // Create department
        $department = OrganizationDepartment::create([
            'entity_id' => $entity->id,
            'dept_name' => 'Administrator',
            'dept_code' => 'ADMIN',
            'description' => 'Group of administrators',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'display_order' => 1,
            'created_by' => $user->id
        ]);
        
        // Create guard type
        $guard = AccessGuardType::create([
            'entity_id' => $entity->id,
            'guard_name' => 'web',
            'description' => 'Website access',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending'
        ]);
        
        // Create permission
        $permission = SystemPermission::create([
            'entity_id' => $entity->id,
            'permission_code' => 'all',
            'permission_name' => 'all access',
            'permission_type' => 'global',
            'description' => 'All actions permitted',
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending'
        ]);
        
        // Assign role to user
        UserRoleAssignment::create([
            'entity_id' => $entity->id,
            'user_id' => $user->id,
            'role_id' => $role->id,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_by' => $user->id
        ]);
        
        // Assign department to user
        UserDepartmentAssignment::create([
            'entity_id' => $entity->id,
            'user_id' => $user->id,
            'department_id' => $department->id,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_by' => $user->id
        ]);
        
        // Assign guard access
        UserGuardAccess::create([
            'entity_id' => $entity->id,
            'user_id' => $user->id,
            'guard_id' => $guard->id,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_by' => $user->id
        ]);
        
        // Grant permission to role
        PermissionGrant::create([
            'entity_id' => $entity->id,
            'permission_id' => $permission->id,
            'role_id' => $role->id,
            'guard_id' => $guard->id,
            'department_id' => null,
            'is_active' => false,
            'is_approval_required' => true,
            'approval_status' => 'pending',
            'created_by' => $user->id
        ]);
    }
    
    /**
     * Send alert email to distributor when a dealer registers under them
     */
    protected function sendDistributorAlert($distributor, $newDealer)
    {
        // Get distributor's primary contact
        $distributorContact = DB::table('contactables')
            ->join('contacts', 'contactables.contact_id', '=', 'contacts.id')
            ->where('contactables.contactable_type', 'App\\Models\\Entity\\Entity')
            ->where('contactables.contactable_id', $distributor->id)
            ->where('contactables.is_primary', true)
            ->first();
            
        if ($distributorContact && $distributorContact->email) {
            Mail::to($distributorContact->email)->send(new DistributorAlertMail($distributor, $newDealer));
        }
    }
} 