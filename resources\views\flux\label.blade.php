@props([
    'badge' => null,
    'aside' => null,
])

@php
$classes = 'inline-flex items-center text-sm font-medium text-zinc-800 dark:text-white';
@endphp

<label {{ $attributes->merge(['class' => $classes]) }} data-flux-label>
    {{ $slot }}

    @if (is_string($badge))
        <span class="ml-1.5 text-zinc-800/70 text-xs bg-zinc-800/5 px-1.5 py-1 -my-1 rounded-[4px] dark:bg-white/10 dark:text-zinc-300" aria-hidden="true">
            {{ $badge }}
        </span>
    @elseif ($badge)
        <span class="ml-1.5" aria-hidden="true">
            {{ $badge }}
        </span>
    @endif

    @if ($aside)
        <span class="ml-1.5 text-zinc-800/70 text-xs bg-zinc-800/5 px-1.5 py-1 -my-1 rounded-[4px] dark:bg-white/10 dark:text-zinc-300" aria-hidden="true">
            {{ $aside }}
        </span>
    @endif
</label>
