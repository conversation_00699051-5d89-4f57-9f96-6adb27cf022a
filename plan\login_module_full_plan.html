<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Login Module Full Implementation Plan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 2em;
            background: #f9f9f9;
        }

        h1,
        h2,
        h3,
        h4 {
            color: #2c3e50;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 2em;
        }

        th,
        td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }

        th {
            background: #eaeaea;
        }

        tr:nth-child(even) {
            background: #f5f5f5;
        }

        .section {
            margin-bottom: 2em;
        }

        .issue {
            color: #c0392b;
            font-weight: bold;
        }

        .solution {
            color: #16a085;
        }

        .code {
            background: #f4f4f4;
            border: 1px solid #ddd;
            padding: 6px;
            font-family: monospace;
        }

        ul,
        ol {
            margin-bottom: 1em;
        }
    </style>
</head>

<body>
    <h1>Login Module Full Implementation Plan</h1>
    <p><strong>Date:</strong> 2025-07-14</p>
    <p><strong>Scope:</strong> This document provides a comprehensive, step-by-step plan for implementing a robust,
        secure, and maintainable login module. It covers all required checks, table/field references, sequence,
        modification plan, pseudocode, and next steps. <strong>No detail is skipped.</strong></p>

    <div class="section">
        <h2>I. Sequence of All Checks (Step-by-Step)</h2>
        <ol>
            <li><strong>User Existence and Status</strong>
                <ul>
                    <li>Check if user exists (by email/username).</li>
                    <li>Check if user account is <code>is_active</code> (<span class="code">users.is_active</span>).
                    </li>
                    <li>If <code>is_approval_required</code> (<span class="code">users.is_approval_required</span>) is
                        true, check <code>approval_status</code> is <code>approved</code>.</li>
                    <li>Check if email is verified (<span class="code">users.email_verified_at</span> is not null).</li>
                </ul>
            </li>
            <li><strong>Session/Concurrent Login</strong>
                <ul>
                    <li>Check allowed concurrent sessions (<span class="code">users.multi_login</span>).</li>
                    <li>Count active sessions for user (<span class="code">login_histories.user_id</span>, <span
                            class="code">logout_at</span> is null).</li>
                    <li>If limit exceeded, show session management or block login.</li>
                </ul>
            </li>
            <li><strong>Role Assignment</strong>
                <ul>
                    <li>Fetch all active role assignments for user (<span class="code">user_role_assignments</span>).
                    </li>
                    <li>For each assignment:
                        <ul>
                            <li><span class="code">is_active</span> is true</li>
                            <li><span class="code">assigned_from</span> &le; today (or null)</li>
                            <li><span class="code">assigned_until</span> &ge; today (or null)</li>
                            <li>If <span class="code">is_approval_required</span> is true, <span
                                    class="code">approval_status</span> is <code>approved</code></li>
                        </ul>
                    </li>
                    <li>Check role status in <span class="code">system_roles</span>:
                        <ul>
                            <li><span class="code">is_active</span> is true</li>
                            <li><span class="code">active_from</span> &le; today (or null)</li>
                            <li><span class="code">active_until</span> &ge; today (or null)</li>
                            <li>If <span class="code">is_approval_required</span> is true, <span
                                    class="code">approval_status</span> is <code>approved</code></li>
                        </ul>
                    </li>
                    <li>Check for duplicate active session with same role (<span
                            class="code">login_histories.user_id</span>, <span class="code">role_id</span>, <span
                            class="code">logout_at</span> is null).</li>
                </ul>
            </li>
            <li><strong>Department Assignment</strong>
                <ul>
                    <li>Check user department assignments (<span class="code">user_department_assignments</span>).</li>
                    <li>For each assignment:
                        <ul>
                            <li><span class="code">is_active</span> is true</li>
                            <li><span class="code">assigned_from</span> &le; today (or null)</li>
                            <li><span class="code">assigned_until</span> &ge; today (or null)</li>
                            <li>If <span class="code">is_approval_required</span> is true, <span
                                    class="code">approval_status</span> is <code>approved</code></li>
                        </ul>
                    </li>
                    <li>Check department status in <span class="code">organization_departments</span>:
                        <ul>
                            <li><span class="code">is_active</span> is true</li>
                            <li><span class="code">active_from</span> &le; today (or null)</li>
                            <li><span class="code">active_until</span> &ge; today (or null)</li>
                            <li>If <span class="code">is_approval_required</span> is true, <span
                                    class="code">approval_status</span> is <code>approved</code></li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li><strong>Guard Access</strong>
                <ul>
                    <li>Check user guard access (<span class="code">user_guard_access</span>).</li>
                    <li>For each access:
                        <ul>
                            <li><span class="code">is_active</span> is true</li>
                            <li><span class="code">access_from</span> &le; today (or null)</li>
                            <li><span class="code">access_until</span> &ge; today (or null)</li>
                            <li>If <span class="code">is_approval_required</span> is true, <span
                                    class="code">approval_status</span> is <code>approved</code></li>
                        </ul>
                    </li>
                    <li>Check guard status in <span class="code">access_guard_types</span>:
                        <ul>
                            <li><span class="code">is_active</span> is true</li>
                            <li>If <span class="code">is_approval_required</span> is true, <span
                                    class="code">approval_status</span> is <code>approved</code></li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li><strong>Entity Status</strong>
                <ul>
                    <li>Check entity status for user, role, department, guard (<span
                            class="code">entities.is_active</span>, <span class="code">is_approval_required</span>,
                        <span class="code">approval_status</span>).</li>
                </ul>
            </li>
            <li><strong>Session Context</strong>
                <ul>
                    <li>Set session variables for active role, department, guard, entity.</li>
                </ul>
            </li>
            <li><strong>Finalize Login</strong>
                <ul>
                    <li>Regenerate session.</li>
                    <li>Record login in <span class="code">login_histories</span> (with all context).</li>
                    <li>Redirect to dashboard or intended page.</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="section">
        <h2>II. Table and Field Reference</h2>
        <table>
            <tr>
                <th>Table</th>
                <th>Key Fields</th>
                <th>Purpose/Check</th>
            </tr>
            <tr>
                <td>users</td>
                <td>is_active, is_approval_required, approval_status, email_verified_at, multi_login</td>
                <td>Account status, approval, email, session limit</td>
            </tr>
            <tr>
                <td>login_histories</td>
                <td>user_id, role_id, department_id, guard_id, entity_id, logout_at</td>
                <td>Active session tracking, duplicate session check</td>
            </tr>
            <tr>
                <td>user_role_assignments</td>
                <td>is_active, assigned_from, assigned_until, is_approval_required, approval_status, role_id</td>
                <td>Role assignment validity, approval, time window</td>
            </tr>
            <tr>
                <td>system_roles</td>
                <td>is_active, active_from, active_until, is_approval_required, approval_status</td>
                <td>Role status, approval, time window</td>
            </tr>
            <tr>
                <td>user_department_assignments</td>
                <td>is_active, assigned_from, assigned_until, is_approval_required, approval_status, department_id</td>
                <td>Department assignment validity, approval, time window</td>
            </tr>
            <tr>
                <td>organization_departments</td>
                <td>is_active, active_from, active_until, is_approval_required, approval_status</td>
                <td>Department status, approval, time window</td>
            </tr>
            <tr>
                <td>user_guard_access</td>
                <td>is_active, access_from, access_until, is_approval_required, approval_status, guard_id</td>
                <td>Guard access validity, approval, time window</td>
            </tr>
            <tr>
                <td>access_guard_types</td>
                <td>is_active, is_approval_required, approval_status</td>
                <td>Guard status, approval</td>
            </tr>
            <tr>
                <td>entities</td>
                <td>is_active, is_approval_required, approval_status</td>
                <td>Entity status, approval</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>III. Modification Plan for Login Module</h2>
        <ol>
            <li><strong>Refactor Login Logic</strong>
                <ul>
                    <li>Centralize all checks in a dedicated service or manager class (e.g., <span
                            class="code">LoginValidationService</span>).</li>
                    <li>Each check should be a separate method for clarity and testability.</li>
                    <li>Use transactions where multiple related records are updated.</li>
                </ul>
            </li>
            <li><strong>Update Livewire Login Component</strong>
                <ul>
                    <li>Call the new validation service before authenticating.</li>
                    <li>Provide user-friendly error messages for each failed check.</li>
                </ul>
            </li>
            <li><strong>Update Middleware</strong>
                <ul>
                    <li>Ensure session management and role/department/guard/entity context is enforced on every request.
                    </li>
                    <li>Prevent duplicate active sessions for the same role.</li>
                </ul>
            </li>
            <li><strong>Update Models</strong>
                <ul>
                    <li>Add helper methods/scopes for all status/approval/date checks (if not already present).</li>
                    <li>Ensure relationships are defined for all cross-table checks.</li>
                </ul>
            </li>
            <li><strong>Update Database (if needed)</strong>
                <ul>
                    <li>Ensure all required fields exist and are indexed for performance.</li>
                    <li>Add missing fields or indexes as identified.</li>
                </ul>
            </li>
            <li><strong>Testing</strong>
                <ul>
                    <li>Write feature tests for all edge cases:
                        <ul>
                            <li>Inactive user</li>
                            <li>Unapproved user</li>
                            <li>Unverified email</li>
                            <li>Exceeded session limit</li>
                            <li>Expired/invalid role, department, guard, entity</li>
                            <li>Duplicate session for same role</li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li><strong>Documentation</strong>
                <ul>
                    <li>Document the full login flow and all checks for future maintainers.</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="section">
        <h2>IV. Pseudocode for Login Validation Sequence</h2>
        <pre class="code">function validateLogin($user) {
    if (!$user->is_active) return 'Account inactive';
    if ($user->is_approval_required && $user->approval_status !== 'approved') return 'Account not approved';
    if (!$user->email_verified_at) return 'Email not verified';

    $activeSessions = countActiveSessions($user->id);
    if ($user->multi_login > 0 && $activeSessions >= $user->multi_login) return 'Session limit exceeded';

    $roles = getActiveApprovedRoles($user->id);
    if (empty($roles)) return 'No valid role assignment';

    foreach ($roles as $roleAssignment) {
        if (!$roleAssignment->is_current) continue;
        $role = $roleAssignment->role;
        if (!$role->isOperational()) continue;
        if (hasActiveSessionForRole($user->id, $role->id)) return 'Duplicate session for role';
        // ...repeat for department, guard, entity
    }

    // All checks passed
    return true;
}
</pre>
    </div>

    <div class="section">
        <h2>V. Next Steps</h2>
        <ol>
            <li>Design and implement the <span class="code">LoginValidationService</span> with all checks as described.
            </li>
            <li>Refactor the login component to use this service and handle errors gracefully.</li>
            <li>Update session management to store all relevant context (role, department, guard, entity).</li>
            <li>Expand tests to cover all new logic and edge cases.</li>
            <li>Document the new flow for maintainers and auditors.</li>
        </ol>
    </div>

    <footer style="margin-top: 3em; color: #888; font-size: 0.9em;">
        <p>Generated by AI Login Module Planner &mdash; 2025-07-14</p>
    </footer>
</body>

</html>