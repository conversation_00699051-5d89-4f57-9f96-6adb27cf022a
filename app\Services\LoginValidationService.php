<?php

namespace App\Services;

use App\Models\Auth\LoginHistory;
use App\Models\Entity\Entity;
use App\Models\Rbac\AccessGuardType;
use App\Models\Rbac\OrganizationDepartment;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\UserDepartmentAssignment;
use App\Models\Rbac\UserGuardAccess;
use App\Models\Rbac\UserRoleAssignment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

/**
 * Comprehensive login validation service that performs all required checks
 * for user authentication and authorization.
 */
class LoginValidationService
{
    /**
     * Super Administrator user ID (typically 1)
     */
    const SUPER_ADMIN_USER_ID = 1;
    
    /**
     * Super Administrator role hierarchy level
     */
    const SUPER_ADMIN_HIERARCHY_LEVEL = 1;
    
    /**
     * Administrator Manager role hierarchy level
     */
    const ADMIN_MANAGER_HIERARCHY_LEVEL = 2;
    
    /**
     * Administrator role hierarchy level
     */
    const ADMIN_HIERARCHY_LEVEL = 3;

    /**
     * Validate all aspects of user login
     *
     * @param User $user The user attempting to login
     * @return array|true Returns true if all checks pass, or an array with error details
     */
    public function validateLogin(User $user)
    {
        // Step 1: Check user existence and status
        $userStatusCheck = $this->checkUserStatus($user);
        if ($userStatusCheck !== true) {
            return $userStatusCheck;
        }

        // Step 2: Check session/concurrent login limits
        $sessionCheck = $this->checkSessionLimits($user);
        if ($sessionCheck !== true) {
            return $sessionCheck;
        }

        // Step 3: Check role assignments
        $roleCheck = $this->checkRoleAssignments($user);
        if ($roleCheck !== true) {
            return $roleCheck;
        }

        // Step 4: Check department assignments
        $departmentCheck = $this->checkDepartmentAssignments($user);
        if ($departmentCheck !== true) {
            return $departmentCheck;
        }

        // Step 5: Check guard access
        $guardCheck = $this->checkGuardAccess($user);
        if ($guardCheck !== true) {
            return $guardCheck;
        }

        // Step 6: Check entity status
        $entityCheck = $this->checkEntityStatus($user);
        if ($entityCheck !== true) {
            return $entityCheck;
        }

        // All checks passed
        return true;
    }

    /**
     * Check if user is a Super Administrator
     *
     * @param User $user
     * @return bool
     */
    public function isSuperAdmin(User $user): bool
    {
        return $user->id === self::SUPER_ADMIN_USER_ID;
    }
    
    /**
     * Get user's highest role hierarchy level
     *
     * @param User $user
     * @return int|null
     */
    public function getUserHighestRoleLevel(User $user): ?int
    {
        // Super Administrator always has the highest level
        if ($this->isSuperAdmin($user)) {
            return self::SUPER_ADMIN_HIERARCHY_LEVEL;
        }
        
        // Get all active role assignments for the user
        $roleAssignments = UserRoleAssignment::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_from')
                    ->orWhere('assigned_from', '<=', $today);
            })
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_until')
                    ->orWhere('assigned_until', '>=', $today);
            })
            ->get();
            
        if ($roleAssignments->isEmpty()) {
            return null;
        }
        
        // Get the roles and find the highest level (lowest number)
        $highestLevel = null;
        foreach ($roleAssignments as $assignment) {
            $role = SystemRole::find($assignment->role_id);
            if ($role && $role->is_active) {
                $level = $role->hierarchy_level ?? PHP_INT_MAX;
                if ($highestLevel === null || $level < $highestLevel) {
                    $highestLevel = $level;
                }
            }
        }
        
        return $highestLevel;
    }

    /**
     * Check user account status
     *
     * @param User $user
     * @return array|true
     */
    public function checkUserStatus(User $user)
    {
        // Check if user is active
        if (!$user->isActive()) {
            return [
                'error' => 'account_inactive',
                'message' => 'Your account is not active. Please contact support.'
            ];
        }

        // Check if user requires approval and is approved
        if ($user->is_approval_required && $user->approval_status !== 'approved') {
            return [
                'error' => 'account_not_approved',
                'message' => 'Your account is pending approval. Please contact support.'
            ];
        }

        // Check if email is verified
        if (!$user->email_verified_at) {
            return [
                'error' => 'email_not_verified',
                'message' => 'Please verify your email address before logging in.',
                'redirect' => 'verification.notice'
            ];
        }

        return true;
    }

    /**
     * Check session limits
     *
     * @param User $user
     * @return array|true
     */
    public function checkSessionLimits(User $user)
    {
        // Count active sessions
        $activeSessions = LoginHistory::activeSessions()
            ->where('user_id', $user->id)
            ->count();

        // Super Administrators might have different session limits
        $sessionLimit = $user->multi_login;
        
        // Check if multi-login limit is exceeded
        if ($sessionLimit > 0 && $activeSessions > $sessionLimit) {
            return [
                'error' => 'session_limit_exceeded',
                'message' => 'Maximum session limit reached. Please end another session to continue.',
                'redirect' => 'auth.session-management',
                'sessions' => $activeSessions,
                'limit' => $sessionLimit
            ];
        }

        return true;
    }

    /**
     * Check role assignments
     *
     * @param User $user
     * @param int|null $entityId Optional entity ID for cross-entity access
     * @param int|null $roleId Optional specific role ID to check
     * @return array|true
     */
    public function checkRoleAssignments(User $user, ?int $entityId = null, ?int $roleId = null)
    {
        // Super Administrator can access any role in any entity
        if ($this->isSuperAdmin($user)) {
            // If a specific role is requested, check if it exists
            if ($roleId !== null) {
                $role = SystemRole::find($roleId);
                if (!$role) {
                    return [
                        'error' => 'role_not_found',
                        'message' => 'The requested role does not exist.'
                    ];
                }
                
                // Check if role belongs to the requested entity (if specified)
                if ($entityId !== null && $role->entity_id !== null && $role->entity_id != $entityId) {
                    return [
                        'error' => 'role_not_in_entity',
                        'message' => 'The requested role does not belong to the selected entity.'
                    ];
                }
                
                return true;
            }
            
            return true; // Super Admin can always access
        }
        
        // For regular users, get all active role assignments
        $query = UserRoleAssignment::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_from')
                    ->orWhere('assigned_from', '<=', $today);
            })
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_until')
                    ->orWhere('assigned_until', '>=', $today);
            });
            
        // If entity ID is specified, filter by entity
        if ($entityId !== null) {
            $query->whereHas('role', function($q) use ($entityId) {
                $q->where('entity_id', $entityId);
            });
        }
        
        // If role ID is specified, filter by role
        if ($roleId !== null) {
            $query->where('role_id', $roleId);
        }
        
        $roleAssignments = $query->get();

        // Check if user has any valid role assignments
        if ($roleAssignments->isEmpty()) {
            return [
                'error' => 'no_valid_roles',
                'message' => 'You do not have any active role assignments. Please contact support.'
            ];
        }

        // Check each role's status
        $validRoles = [];
        foreach ($roleAssignments as $assignment) {
            $role = SystemRole::find($assignment->role_id);
            
            if (!$role) {
                continue;
            }

            // Check role status
            if (!$role->is_active) {
                continue;
            }

            // Check role approval status
            if ($role->is_approval_required && $role->approval_status !== 'approved') {
                continue;
            }

            // Check role time window
            $today = Carbon::now()->toDateString();
            if ($role->active_from && $role->active_from > $today) {
                continue;
            }
            if ($role->active_until && $role->active_until < $today) {
                continue;
            }

            // Check for duplicate active session with same role
            $duplicateSession = LoginHistory::activeSessions()
                ->where('user_id', $user->id)
                ->where('role_id', $role->id)
                ->where('session_id', '!=', Session::getId())
                ->exists();

            if ($duplicateSession) {
                continue;
            }

            $validRoles[] = $role;
        }

        if (empty($validRoles)) {
            return [
                'error' => 'no_operational_roles',
                'message' => 'None of your assigned roles are currently operational. Please contact support.'
            ];
        }

        return true;
    }

    /**
     * Check department assignments
     *
     * @param User $user
     * @param int|null $entityId Optional entity ID for cross-entity access
     * @return array|true
     */
    public function checkDepartmentAssignments(User $user, ?int $entityId = null)
    {
        // Super Administrator can access any department in any entity
        if ($this->isSuperAdmin($user)) {
            return true;
        }
        
        // Get all active department assignments for the user
        $query = UserDepartmentAssignment::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_from')
                    ->orWhere('assigned_from', '<=', $today);
            })
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_until')
                    ->orWhere('assigned_until', '>=', $today);
            });
            
        // If entity ID is specified, filter by entity
        if ($entityId !== null) {
            $query->whereHas('department', function($q) use ($entityId) {
                $q->where('entity_id', $entityId);
            });
        }
        
        $departmentAssignments = $query->get();

        // If user has no department assignments, that's fine (not all users need departments)
        if ($departmentAssignments->isEmpty()) {
            return true;
        }

        // Check each department's status
        $validDepartments = [];
        foreach ($departmentAssignments as $assignment) {
            $department = OrganizationDepartment::find($assignment->department_id);
            
            if (!$department) {
                continue;
            }

            // Check department status
            if (!$department->is_active) {
                continue;
            }

            // Check department approval status
            if ($department->is_approval_required && $department->approval_status !== 'approved') {
                continue;
            }

            $validDepartments[] = $department;
        }

        // If user had department assignments but none are valid, that's a problem
        if ($departmentAssignments->isNotEmpty() && empty($validDepartments)) {
            return [
                'error' => 'no_valid_departments',
                'message' => 'None of your assigned departments are currently operational. Please contact support.'
            ];
        }

        return true;
    }

    /**
     * Check guard access
     *
     * @param User $user
     * @return array|true
     */
    public function checkGuardAccess(User $user)
    {
        $currentGuard = config('auth.defaults.guard');
        
        // Super Administrator can access any guard
        if ($this->isSuperAdmin($user)) {
            return true;
        }
        
        // Find guard types that match the current guard
        $guardTypes = AccessGuardType::where('guard_name', $currentGuard)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->where('is_approval_required', false)
                    ->orWhere('approval_status', 'approved');
            })
            ->get();
            
        if ($guardTypes->isEmpty()) {
            return [
                'error' => 'guard_not_available',
                'message' => 'The requested authentication guard is not available.'
            ];
        }
        
        // Get all active guard access for the user
        $guardAccess = UserGuardAccess::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('access_from')
                    ->orWhere('access_from', '<=', $today);
            })
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('access_until')
                    ->orWhere('access_until', '>=', $today);
            })
            ->get();
            
        // If user has no explicit guard access records, we'll allow access by default
        if ($guardAccess->isEmpty()) {
            return true;
        }
        
        // Check if user has access to any of the valid guard types
        $hasAccess = false;
        foreach ($guardAccess as $access) {
            foreach ($guardTypes as $guardType) {
                if ($access->guard_id === $guardType->id) {
                    $hasAccess = true;
                    break 2;
                }
            }
        }
        
        if (!$hasAccess) {
            return [
                'error' => 'no_guard_access',
                'message' => 'You do not have access to this authentication guard. Please contact support.'
            ];
        }

        return true;
    }

    /**
     * Check entity status
     *
     * @param User $user
     * @param string|null $entityId Optional entity ID to check for cross-entity access
     * @return array|true
     */
    public function checkEntityStatus(User $user, ?string $entityId = null)
    {
        // Super Administrator can access any entity
        if ($this->isSuperAdmin($user)) {
            // If a specific entity is requested, check if it exists
            if ($entityId !== null) {
                $entity = Entity::where('entity_id', $entityId)->first();
                if (!$entity) {
                    return [
                        'error' => 'entity_not_found',
                        'message' => 'The requested entity could not be found.'
                    ];
                }
                
                return true;
            }
            
            return true; // Super Admin can access any entity
        }
        
        // For regular users, check their associated entity
        $checkEntityId = $entityId ?? $user->entity_id;
        
        // Check if user is associated with an entity
        if (!$checkEntityId) {
            return true; // No entity association, so no check needed
        }

        $entity = Entity::where('entity_id', $checkEntityId)->first();
        
        if (!$entity) {
            return [
                'error' => 'entity_not_found',
                'message' => 'Your associated entity could not be found. Please contact support.'
            ];
        }

        // Check entity status
        if (!$entity->isActive()) {
            return [
                'error' => 'entity_inactive',
                'message' => 'Your associated entity is not active. Please contact support.'
            ];
        }

        // Check entity approval status
        if ($entity->is_approval_required && $entity->approval_status !== 'approved') {
            return [
                'error' => 'entity_not_approved',
                'message' => 'Your associated entity is pending approval. Please contact support.'
            ];
        }

        return true;
    }

    /**
     * Set session context after successful validation
     *
     * @param User $user
     * @param string|null $roleId Optional specific role ID to set
     * @param string|null $entityId Optional entity ID for cross-entity access
     * @param string|null $sessionLabel Optional label for the session
     * @return void
     */
    public function setSessionContext(User $user, ?string $roleId = null, ?string $entityId = null, ?string $sessionLabel = null)
    {
        \Log::debug('=== LoginValidationService@setSessionContext START ===');
        \Log::debug('User: ' . json_encode($user->toArray()));
        \Log::debug('Role ID: ' . ($roleId ?? 'null'));
        \Log::debug('Entity ID: ' . ($entityId ?? 'null'));
        \Log::debug('Session Label: ' . ($sessionLabel ?? 'null'));
        \Log::debug('Current Session ID: ' . session()->getId());
        \Log::debug('Current Session Data: ' . json_encode(session()->all()));
        
        $isSuperAdmin = $this->isSuperAdmin($user);
        $targetEntityId = $entityId ?? $user->entity_id;
        
        \Log::debug('Is Super Admin: ' . ($isSuperAdmin ? 'Yes' : 'No'));
        \Log::debug('Target Entity ID: ' . ($targetEntityId ?? 'null'));
        
        // Store Super Admin status in session
        if ($isSuperAdmin) {
            Session::put('is_super_admin', true);
            \Log::debug('Set is_super_admin = true in session');
            
            // If accessing another entity, store original entity
            if ($entityId !== null && $entityId != $user->entity_id) {
                Session::put('original_entity_id', $user->entity_id);
                \Log::debug('Set original_entity_id = ' . $user->entity_id . ' in session');
            }
            
            // If Super Admin is logging in without entity selection, mark this in the session
            if ($entityId === null && !$user->entity_id) {
                Session::put('unrestricted_access', true);
                \Log::debug('Set unrestricted_access = true in session');
            }
        }
        
        \Log::debug('Getting valid roles');
        // Get valid roles
        if ($isSuperAdmin && $roleId !== null) {
            \Log::debug('Super Admin with specific role ID: ' . $roleId);
            // Super Admin can use any role
            $role = SystemRole::find($roleId);
            if ($role) {
                $activeRole = $role;
                \Log::debug('Found role: ' . json_encode($role->toArray()));
            } else {
                \Log::debug('Role not found with ID: ' . $roleId);
            }
        } else {
            \Log::debug('Regular user or Super Admin without specific role');
            // Regular users need to have the role assigned
            $roleAssignmentsQuery = UserRoleAssignment::where('user_id', $user->id)
                ->where('is_active', true)
                ->where('approval_status', 'approved')
                ->where(function ($query) {
                    $today = Carbon::now()->toDateString();
                    $query->whereNull('assigned_from')
                        ->orWhere('assigned_from', '<=', $today);
                })
                ->where(function ($query) {
                    $today = Carbon::now()->toDateString();
                    $query->whereNull('assigned_until')
                        ->orWhere('assigned_until', '>=', $today);
                });
                
            \Log::debug('Role Assignments Query: ' . $roleAssignmentsQuery->toSql());
            
            $roleAssignments = $roleAssignmentsQuery->get();
            \Log::debug('Found ' . $roleAssignments->count() . ' role assignments');
                
            $validRoles = [];
            foreach ($roleAssignments as $assignment) {
                $role = SystemRole::find($assignment->role_id);
                if ($role && $role->isOperational()) {
                    $validRoles[$role->id] = $role;
                    \Log::debug('Valid role found: ' . $role->role_name . ' (ID: ' . $role->id . ')');
                }
            }
            
            \Log::debug('Total valid roles: ' . count($validRoles));
            
            // Set active role
            $activeRole = null;
            
            // If specific role ID is provided, use that
            if ($roleId && isset($validRoles[$roleId])) {
                $activeRole = $validRoles[$roleId];
                \Log::debug('Using specified role ID: ' . $roleId);
            } 
            // Otherwise use the first valid role
            elseif (!empty($validRoles)) {
                $activeRole = reset($validRoles);
                \Log::debug('Using first valid role: ' . $activeRole->role_name . ' (ID: ' . $activeRole->id . ')');
            } else {
                \Log::debug('No valid roles found');
            }
        }
        
        // Set role context in session
        if (isset($activeRole)) {
            \Log::debug('Setting active role in session: ' . $activeRole->role_name . ' (ID: ' . $activeRole->id . ')');
            Session::put('active_role_id', $activeRole->id);
            Session::put('active_role_name', $activeRole->role_name);
            Session::put('active_role_level', $activeRole->hierarchy_level);
        } else if ($isSuperAdmin) {
            \Log::debug('Setting Super Admin role level in session');
            // For Super Administrators without a specific role, set the highest level
            Session::put('active_role_level', self::SUPER_ADMIN_HIERARCHY_LEVEL);
        }
        
        // Set entity context in session
        if ($targetEntityId) {
            \Log::debug('Looking up entity with ID: ' . $targetEntityId);
            $entity = Entity::where('entity_id', $targetEntityId)->first();
            if ($entity) {
                \Log::debug('Setting entity in session: ' . $entity->entity_name . ' (ID: ' . $entity->entity_id . ')');
                Session::put('active_entity_id', $entity->entity_id);
                Session::put('active_entity_name', $entity->entity_name);
            } else {
                \Log::debug('Entity not found with ID: ' . $targetEntityId);
            }
        }
        
        // Set department context (if any)
        \Log::debug('Looking up department assignments');
        $departmentAssignmentsQuery = UserDepartmentAssignment::where('user_id', $user->id)
            ->where('is_active', true)
            ->where('approval_status', 'approved')
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_from')
                    ->orWhere('assigned_from', '<=', $today);
            })
            ->where(function ($query) {
                $today = Carbon::now()->toDateString();
                $query->whereNull('assigned_until')
                    ->orWhere('assigned_until', '>=', $today);
            });
            
        \Log::debug('Department Assignments Query: ' . $departmentAssignmentsQuery->toSql());
        
        $departmentAssignments = $departmentAssignmentsQuery->first();
        \Log::debug('Department Assignment Found: ' . ($departmentAssignments ? 'Yes' : 'No'));
            
        if ($departmentAssignments) {
            $department = OrganizationDepartment::find($departmentAssignments->department_id);
            if ($department && $department->is_active) {
                \Log::debug('Setting department in session: ' . $department->dept_name . ' (ID: ' . $department->id . ')');
                Session::put('active_department_id', $department->id);
                Session::put('active_department_name', $department->dept_name);
            } else {
                \Log::debug('Department not found or not active: ' . $departmentAssignments->department_id);
            }
        }
        
        // Set active guard
        $currentGuard = config('auth.defaults.guard');
        \Log::debug('Setting active guard in session: ' . $currentGuard);
        Session::put('active_guard', $currentGuard);
        
        // Set session label if provided
        if ($sessionLabel) {
            \Log::debug('Setting session label in session: ' . $sessionLabel);
            Session::put('session_label', $sessionLabel);
        }
        
        // Set device information
        \Log::debug('Setting device information');
        $this->setDeviceInformation();
        
        // Set last activity timestamp
        \Log::debug('Setting last activity timestamp');
        Session::put('last_activity', time());
        
        \Log::debug('Final Session Data: ' . json_encode(session()->all()));
        \Log::debug('=== LoginValidationService@setSessionContext END ===');
    }
    
    /**
     * Set device information in session
     *
     * @return void
     */
    protected function setDeviceInformation()
    {
        $request = request();
        $userAgent = $request->userAgent();
        $ipAddress = $request->ip();
        
        // Basic device detection
        $deviceType = 'unknown';
        if (preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $userAgent)) {
            $deviceType = 'mobile';
        } elseif (preg_match('/android|ipad|playbook|silk/i', $userAgent)) {
            $deviceType = 'tablet';
        } else {
            $deviceType = 'desktop';
        }
        
        // Browser detection
        $browser = 'unknown';
        if (preg_match('/MSIE/i', $userAgent) || preg_match('/Trident/i', $userAgent)) {
            $browser = 'Internet Explorer';
        } elseif (preg_match('/Firefox/i', $userAgent)) {
            $browser = 'Firefox';
        } elseif (preg_match('/Chrome/i', $userAgent)) {
            $browser = 'Chrome';
        } elseif (preg_match('/Safari/i', $userAgent)) {
            $browser = 'Safari';
        } elseif (preg_match('/Opera/i', $userAgent)) {
            $browser = 'Opera';
        } elseif (preg_match('/Edge/i', $userAgent)) {
            $browser = 'Edge';
        }
        
        // OS detection
        $os = 'unknown';
        if (preg_match('/windows|win32/i', $userAgent)) {
            $os = 'Windows';
        } elseif (preg_match('/macintosh|mac os x/i', $userAgent)) {
            $os = 'macOS';
        } elseif (preg_match('/linux/i', $userAgent)) {
            $os = 'Linux';
        } elseif (preg_match('/android/i', $userAgent)) {
            $os = 'Android';
        } elseif (preg_match('/iphone|ipad|ipod/i', $userAgent)) {
            $os = 'iOS';
        }
        
        // Create fingerprint
        $fingerprint = md5($userAgent . $ipAddress . $browser . $os);
        
        // Store device information in session
        Session::put('device_type', $deviceType);
        Session::put('device_browser', $browser);
        Session::put('device_os', $os);
        Session::put('device_ip', $ipAddress);
        Session::put('device_fingerprint', $fingerprint);
        
        // Generate a default session label if none exists
        if (!Session::has('session_label')) {
            $defaultLabel = ucfirst($deviceType) . ' - ' . $browser . ' on ' . $os;
            Session::put('session_label', $defaultLabel);
        }
    }
    
    /**
     * Transfer session to another device
     *
     * @param string $sessionId Current session ID
     * @param string $transferCode Transfer code
     * @return bool
     */
    public function createSessionTransfer(string $sessionId): string
    {
        // Get configuration values
        $codeLength = config('session_transfer.code_length', 8);
        $codeFormat = config('session_transfer.code_format', 'uppercase');
        $expirationTime = config('session_transfer.expiration_time', 600);
        $cachePrefix = config('session_transfer.cache_key_prefix', 'session_transfer:');
        $cacheStore = config('session_transfer.cache_store');
        
        // Generate a unique transfer code based on format
        $transferCode = '';
        switch ($codeFormat) {
            case 'numeric':
                $transferCode = substr(str_pad(mt_rand(0, 999999999), $codeLength, '0', STR_PAD_LEFT), 0, $codeLength);
                break;
            case 'alphanumeric':
                $transferCode = substr(md5(uniqid(mt_rand(), true)), 0, $codeLength);
                break;
            case 'uppercase':
            default:
                $transferCode = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, $codeLength));
                break;
        }
        
        // Get cross-entity access information from session
        $isCrossEntityAccess = Session::has('is_super_admin') && Session::has('original_entity_id');
        $originalEntityId = Session::get('original_entity_id');
        $originalRoleId = null; // We don't track this currently, but could be added
        
        $cacheData = [
            'session_id' => $sessionId,
            'user_id' => auth()->id(),
            'created_at' => now(),
            'role_id' => Session::get('active_role_id'),
            'entity_id' => Session::get('active_entity_id'),
            'department_id' => Session::get('active_department_id'),
            'attempts' => 0, // Track number of attempts
            'is_cross_entity_access' => $isCrossEntityAccess,
            'original_entity_id' => $originalEntityId,
            'original_role_id' => $originalRoleId,
        ];
        
        // Store transfer information in cache
        if ($cacheStore) {
            // Use specific cache store if configured
            cache()->store($cacheStore)->put($cachePrefix . $transferCode, $cacheData, $expirationTime);
        } else {
            // Use default cache store
            cache()->put($cachePrefix . $transferCode, $cacheData, $expirationTime);
        }
        
        return $transferCode;
    }
    
    /**
     * Complete session transfer
     *
     * @param string $transferCode Transfer code
     * @return array|bool
     */
    public function completeSessionTransfer(string $transferCode)
    {
        // Get configuration values
        $maxAttempts = config('session_transfer.max_attempts', 3);
        $cachePrefix = config('session_transfer.cache_key_prefix', 'session_transfer:');
        $cacheStore = config('session_transfer.cache_store');
        
        // Get transfer information from cache
        $cacheKey = $cachePrefix . $transferCode;
        $transfer = $cacheStore ? 
            cache()->store($cacheStore)->get($cacheKey) : 
            cache()->get($cacheKey);
        
        if (!$transfer) {
            return [
                'error' => 'invalid_transfer_code',
                'message' => 'The transfer code is invalid or has expired.'
            ];
        }
        
        // Increment attempt counter
        $transfer['attempts'] = ($transfer['attempts'] ?? 0) + 1;
        
        // Check if max attempts exceeded
        if ($transfer['attempts'] > $maxAttempts) {
            // Delete the transfer code from cache
            if ($cacheStore) {
                cache()->store($cacheStore)->forget($cacheKey);
            } else {
                cache()->forget($cacheKey);
            }
            
            return [
                'error' => 'max_attempts_exceeded',
                'message' => 'Maximum number of attempts exceeded. Please generate a new transfer code.'
            ];
        }
        
        // Update the attempts count in cache
        if ($cacheStore) {
            cache()->store($cacheStore)->put($cacheKey, $transfer, config('session_transfer.expiration_time', 600));
        } else {
            cache()->put($cacheKey, $transfer, config('session_transfer.expiration_time', 600));
        }
        
        // Check if user is authenticated and matches the transfer
        if (!auth()->check() || auth()->id() != $transfer['user_id']) {
            return [
                'error' => 'unauthorized_transfer',
                'message' => 'You are not authorized to complete this session transfer.'
            ];
        }
        
        // Set session context based on transfer data
        $user = auth()->user();
        $this->setSessionContext(
            $user, 
            $transfer['role_id'], 
            $transfer['entity_id']
        );
        
        // Remove the transfer code from cache
        if ($cacheStore) {
            cache()->store($cacheStore)->forget($cacheKey);
        } else {
            cache()->forget($cacheKey);
        }
        
        // Update login history to mark the transfer
        LoginHistory::where('session_id', session()->getId())
            ->where('user_id', $user->id)
            ->whereNull('logout_at')
            ->update([
                'transferred_from' => $transfer['session_id'],
                'updated_at' => now()
            ]);
        
        return true;
    }
} 