<div>
    <div class="max-w-md mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Select Entity Type</h3>
        
        <form wire:submit.prevent="saveAndContinue">
            <div class="space-y-4 mb-6">
                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 {{ $entityType === 'distributor' ? 'border-blue-500 bg-blue-50' : 'border-gray-300' }}">
                    <input 
                        type="radio" 
                        wire:model.live="entityType" 
                        value="distributor" 
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mr-3"
                    />
                    <div>
                        <div class="font-medium">Distributor</div>
                        <div class="text-sm text-gray-600">Register as a distributor</div>
                    </div>
                </label>
                
                <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 {{ $entityType === 'dealer' ? 'border-blue-500 bg-blue-50' : 'border-gray-300' }}">
                    <input 
                        type="radio" 
                        wire:model.live="entityType" 
                        value="dealer" 
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 mr-3"
                    />
                    <div>
                        <div class="font-medium">Dealer</div>
                        <div class="text-sm text-gray-600">Register as a dealer</div>
                    </div>
                </label>
            </div>
            
            @error('entityType')
                <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">{{ $message }}</p>
                        </div>
                    </div>
                </div>
            @enderror
            
            @if($showDistributorField)
                <div class="mb-4">
                    <label for="distributorId" class="block text-sm font-medium text-gray-700 mb-1">Distributor ID (Optional)</label>
                    <input 
                        wire:model="distributorId" 
                        type="text" 
                        id="distributorId"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="DIS-XX-XXXX"
                    >
                    @error('distributorId') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>
                
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                If you have a distributor ID, enter it to link your account. Leave blank if not applicable.
                            </p>
                        </div>
                    </div>
                </div>
            @endif
            
            <div class="flex justify-between">
                <div></div> <!-- Placeholder for alignment -->
                <button 
                    type="submit" 
                    class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    wire:loading.attr="disabled"
                    {{ !$entityType ? 'disabled' : '' }}
                >
                    Continue
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div> 