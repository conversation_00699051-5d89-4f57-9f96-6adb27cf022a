<nav aria-label="{{ __('Admin Navigation') }}">
    <flux:navlist variant="outline">
        <!-- Role Management Section -->
        <flux:navlist.group :heading="__('Role Management')" class="grid">
            <flux:navlist.item
                icon="shield-check"
                :href="route('admin.rbac.roles.index')"
                :current="request()->routeIs('admin.rbac.roles.*')"
                wire:navigate>
                {{ __('Roles') }}
            </flux:navlist.item>

            <flux:navlist.item
                icon="users"
                :href="route('admin.rbac.users.index')"
                :current="request()->routeIs('admin.rbac.users.*')"
                wire:navigate>
                {{ __('User Assignments') }}
            </flux:navlist.item>
        </flux:navlist.group>

        <!-- Permission Management Section -->
        <flux:navlist.group :heading="__('Permissions')" class="grid">
            <flux:navlist.item
                icon="key"
                href="#"
                disabled>
                {{ __('Permissions') }} <span class="text-xs">(Coming Soon)</span>
            </flux:navlist.item>

            <flux:navlist.item
                icon="building-office"
                href="#"
                disabled>
                {{ __('Departments') }} <span class="text-xs">(Coming Soon)</span>
            </flux:navlist.item>
        </flux:navlist.group>

        <!-- Audit & Reports Section -->
        <flux:navlist.group :heading="__('Audit & Reports')" class="grid">
            <flux:navlist.item
                icon="document-magnifying-glass"
                href="#"
                disabled>
                {{ __('Access Audit') }} <span class="text-xs">(Coming Soon)</span>
            </flux:navlist.item>
        </flux:navlist.group>
    </flux:navlist>
</nav>
