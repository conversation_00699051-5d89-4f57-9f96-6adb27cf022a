<div>
    <div class="max-w-2xl mx-auto">
        <h3 class="text-2xl font-semibold mb-6">Tax Information</h3>
        
        <form wire:submit.prevent="saveAndContinue">
            <div class="bg-white rounded-lg border border-gray-300 p-6 mb-8">
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tax Type</label>
                    
                    <div class="space-y-2">
                        @foreach($this->taxTypeOptions as $value => $label)
                            <label class="flex items-center">
                                <input 
                                    type="radio" 
                                    wire:model.live="taxType" 
                                    value="{{ $value }}" 
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                />
                                <span class="ml-2 text-sm text-gray-700">{{ $label }}</span>
                            </label>
                        @endforeach
                    </div>
                    
                    @error('taxType') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>
                
                <div class="mb-6">
                    <label for="taxIdentifier" class="block text-sm font-medium text-gray-700 mb-1">Tax Identifier</label>
                    <input 
                        wire:model="taxIdentifier" 
                        id="taxIdentifier"
                        type="text"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="{{ $this->formatHint }}"
                    >
                    
                    @if($this->formatHint)
                        <p class="mt-1 text-xs text-gray-500">Format: {{ $this->formatHint }}</p>
                    @endif
                    
                    @error('taxIdentifier') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label for="effectiveDate" class="block text-sm font-medium text-gray-700 mb-1">Effective Date (Optional)</label>
                        <input 
                            wire:model="effectiveDate" 
                            id="effectiveDate"
                            type="date"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('effectiveDate') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                    
                    <div>
                        <label for="expiryDate" class="block text-sm font-medium text-gray-700 mb-1">Expiry Date (Optional)</label>
                        <input 
                            wire:model="expiryDate" 
                            id="expiryDate"
                            type="date"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                        @error('expiryDate') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tax Region</label>
                    
                    <div class="space-y-2">
                        @foreach($this->taxRegionOptions as $value => $label)
                            <label class="flex items-center">
                                <input 
                                    type="radio" 
                                    wire:model="taxRegion" 
                                    value="{{ $value }}" 
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                />
                                <span class="ml-2 text-sm text-gray-700">{{ $label }}</span>
                            </label>
                        @endforeach
                    </div>
                    
                    @error('taxRegion') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>
                
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                Tax information is required for legal compliance and will be verified. Please ensure the details are accurate and match your official records.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            {{-- Navigation --}}
            <div class="flex justify-between">
                <button 
                    type="button"
                    wire:click="stepBack" 
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                    Previous
                </button>
                
                <button 
                    type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    Continue
                    <svg class="w-5 h-5 ml-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div> 