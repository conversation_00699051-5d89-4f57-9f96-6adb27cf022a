<?php
// app/Models/Entity/EntityRelationship.php
namespace App\Models\Entity;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * Class EntityRelationship
 *
 * Represents business relationships between entities in the system.
 * Tracks how entities interact with each other (supplier-distributor, distributor-dealer, etc.)
 *
 * This model handles complex business relationship mapping including supply chains,
 * service relationships, hierarchical structures, and generic partnerships.
 * Each relationship has direction (source → target) and can be bidirectional when needed.
 *
 * @property int $id
 * @property int $source_entity_id Entity initiating the relationship
 * @property int $target_entity_id Entity receiving the relationship
 * @property string $relationship_type Type of relationship
 * @property string|null $description Optional relationship notes
 * @property \Carbon\Carbon|null $effective_date Date when the relationship becomes effective
 * @property \Carbon\Carbon|null $expiry_date Date when the relationship expires
 * @property bool $is_active Whether this relationship is currently active
 * @property bool $is_approval_required Flag indicating if approval is needed
 * @property string $approval_status Current approval status
 * @property int $created_by User ID who created this record
 * @property int|null $updated_by User ID who last updated this record
 * @property int|null $deleted_by User ID who deleted this relationship
 * @property int|null $restored_by User ID who restored this relationship
 * @property \Carbon\Carbon|null $restored_at Restoration timestamp
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder approved()
 * @method static \Illuminate\Database\Eloquent\Builder operational()
 * @method static \Illuminate\Database\Eloquent\Builder ofType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder involvingEntity(int $entityId)
 * @method static \Illuminate\Database\Eloquent\Builder supplyChain()
 * @method static \Illuminate\Database\Eloquent\Builder hierarchical()
 * @method static \Illuminate\Database\Eloquent\Builder pendingApproval()
 */
class EntityRelationship extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'entity_relationships';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'source_entity_id',
        'target_entity_id',
        'relationship_type',
        'description',
        'effective_date',
        'expiry_date',
        'is_active',
        'is_approval_required',
        'approval_status',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
        'effective_date' => 'date',
        'expiry_date' => 'date',
        'restored_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'relationship_description',
        'inverse_relationship_type',
        'is_operational',
        'is_supply_chain',
        'is_hierarchical',
        'approval_status_label'
    ];

    /**
     * Available relationship types with descriptions.
     *
     * @var array<string, string>
     */
    const RELATIONSHIP_TYPES = [
        'supplies_to' => 'Supplier → Distributor or Distributor → Dealer',
        'service_to' => 'Service Provider → Distributor or Dealer',
        'sells_to' => 'Distributor → Dealer or Dealer → Customer',
        'reports_to' => 'Dealer → Distributor',
        'parent_of' => 'Parent → Child (ownership/structure)',
        'linked_to' => 'Generic bidirectional link'
    ];

    /**
     * Approval status constants.
     *
     * @var array<string, string>
     */
    const APPROVAL_STATUSES = [
        'pending' => 'Pending Approval',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'expired' => 'Expired',
        'cancelled' => 'Cancelled',
        'on_hold' => 'On Hold'
    ];

    /**
     * Supply chain relationship types.
     *
     * @var array<string>
     */
    const SUPPLY_CHAIN_TYPES = ['supplies_to', 'sells_to'];

    /**
     * Hierarchical relationship types.
     *
     * @var array<string>
     */
    const HIERARCHICAL_TYPES = ['parent_of', 'reports_to'];

    /**
     * Bidirectional relationship types.
     *
     * @var array<string>
     */
    const BIDIRECTIONAL_TYPES = ['linked_to'];

    // ===== ACCESSORS =====

    /**
     * Get human-readable relationship type description.
     *
     * @return string
     */
    public function getRelationshipDescriptionAttribute(): string
    {
        return self::RELATIONSHIP_TYPES[$this->relationship_type] ?? 'Unknown relationship';
    }

    /**
     * Get human-readable approval status.
     *
     * @return string
     */
    public function getApprovalStatusLabelAttribute(): string
    {
        return self::APPROVAL_STATUSES[$this->approval_status] ?? 'Unknown';
    }

    /**
     * Check if relationship is operational (active and approved).
     *
     * @return bool
     */
    public function getIsOperationalAttribute(): bool
    {
        return $this->isOperational();
    }

    /**
     * Check if this is a supply chain relationship.
     *
     * @return bool
     */
    public function getIsSupplyChainAttribute(): bool
    {
        return $this->isSupplyChain();
    }

    /**
     * Check if this is a hierarchical relationship.
     *
     * @return bool
     */
    public function getIsHierarchicalAttribute(): bool
    {
        return $this->isHierarchical();
    }

    /**
     * Get the inverse relationship type.
     *
     * @return string|null
     */
    public function getInverseRelationshipTypeAttribute(): ?string
    {
        return $this->getInverseRelationshipType();
    }

    // ===== BUSINESS LOGIC METHODS =====

    /**
     * Check if relationship is operational (active and approved).
     *
     * @return bool
     */
    public function isOperational(): bool
    {
        return $this->is_active && $this->approval_status === 'approved';
    }

    /**
     * Check if this is a supply chain relationship.
     *
     * @return bool
     */
    public function isSupplyChain(): bool
    {
        return in_array($this->relationship_type, self::SUPPLY_CHAIN_TYPES);
    }

    /**
     * Check if this is a hierarchical relationship.
     *
     * @return bool
     */
    public function isHierarchical(): bool
    {
        return in_array($this->relationship_type, self::HIERARCHICAL_TYPES);
    }

    /**
     * Check if this is a bidirectional relationship.
     *
     * @return bool
     */
    public function isBidirectional(): bool
    {
        return in_array($this->relationship_type, self::BIDIRECTIONAL_TYPES);
    }

    /**
     * Check if this is a service relationship.
     *
     * @return bool
     */
    public function isServiceRelationship(): bool
    {
        return $this->relationship_type === 'service_to';
    }

    /**
     * Get the inverse relationship type.
     *
     * @return string|null
     */
    public function getInverseRelationshipType(): ?string
    {
        return match ($this->relationship_type) {
            'supplies_to' => 'supplied_by',
            'sells_to' => 'buys_from',
            'service_to' => 'serviced_by',
            'reports_to' => 'manages',
            'parent_of' => 'child_of',
            'linked_to' => 'linked_to', // Bidirectional
            default => null
        };
    }

    /**
     * Check if this relationship involves a specific entity.
     *
     * @param int $entityId
     * @return bool
     */
    public function involvesEntity(int $entityId): bool
    {
        return $this->source_entity_id === $entityId || $this->target_entity_id === $entityId;
    }

    /**
     * Get the other entity in this relationship.
     *
     * @param int $entityId
     * @return Entity|null
     */
    public function getOtherEntity(int $entityId): ?Entity
    {
        if ($this->source_entity_id === $entityId) {
            return $this->targetEntity;
        } elseif ($this->target_entity_id === $entityId) {
            return $this->sourceEntity;
        }

        return null;
    }

    /**
     * Get relationship perspective from a specific entity's viewpoint.
     *
     * @param int $entityId
     * @return array|null Returns ['type' => string, 'direction' => 'outgoing'|'incoming', 'other_entity' => Entity]
     */
    public function getPerspective(int $entityId): ?array
    {
        if ($this->source_entity_id === $entityId) {
            return [
                'type' => $this->relationship_type,
                'direction' => 'outgoing',
                'other_entity' => $this->targetEntity
            ];
        } elseif ($this->target_entity_id === $entityId) {
            return [
                'type' => $this->inverse_relationship_type ?? $this->relationship_type,
                'direction' => 'incoming',
                'other_entity' => $this->sourceEntity
            ];
        }

        return null;
    }

    // ===== ENTITY RELATIONSHIPS =====

    /**
     * Get the source entity (initiator of the relationship).
     *
     * @return BelongsTo
     */
    public function sourceEntity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'source_entity_id');
    }

    /**
     * Get the target entity (receiver of the relationship).
     *
     * @return BelongsTo
     */
    public function targetEntity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'target_entity_id');
    }

    // ===== AUDIT RELATIONSHIPS =====

    /**
     * Get the user who created this relationship.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this relationship.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted this relationship.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored this relationship.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to only active relationships.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to only approved relationships.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope to operational relationships (active and approved).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOperational($query)
    {
        return $query->active()->approved();
    }

    /**
     * Scope to relationships of a specific type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('relationship_type', $type);
    }

    /**
     * Scope to relationships involving a specific entity (as source or target).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $entityId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInvolvingEntity($query, int $entityId)
    {
        return $query->where(function ($q) use ($entityId) {
            $q->where('source_entity_id', $entityId)
                ->orWhere('target_entity_id', $entityId);
        });
    }

    /**
     * Scope to relationships where entity is the source.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $entityId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSourcedBy($query, int $entityId)
    {
        return $query->where('source_entity_id', $entityId);
    }

    /**
     * Scope to relationships where entity is the target.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $entityId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTargetedAt($query, int $entityId)
    {
        return $query->where('target_entity_id', $entityId);
    }

    /**
     * Scope to supply chain relationships.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSupplyChain($query)
    {
        return $query->whereIn('relationship_type', self::SUPPLY_CHAIN_TYPES);
    }

    /**
     * Scope to hierarchical relationships.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHierarchical($query)
    {
        return $query->whereIn('relationship_type', self::HIERARCHICAL_TYPES);
    }

    /**
     * Scope to bidirectional relationships.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBidirectional($query)
    {
        return $query->whereIn('relationship_type', self::BIDIRECTIONAL_TYPES);
    }

    /**
     * Scope to relationships pending approval.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePendingApproval($query)
    {
        return $query->where('is_approval_required', true)
            ->where('approval_status', 'pending');
    }

    /**
     * Scope to search relationships by description.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('description', 'like', "%{$search}%")
                ->orWhere('relationship_type', 'like', "%{$search}%");
        });
    }

    // ===== STATIC METHODS =====

    /**
     * Create a bidirectional relationship between two entities.
     *
     * @param int $entityId1
     * @param int $entityId2
     * @param string $relationshipType
     * @param string|null $description
     * @param array $options Additional options
     * @return array Array of created relationships
     */
    public static function createBidirectional(
        int $entityId1,
        int $entityId2,
        string $relationshipType = 'linked_to',
        ?string $description = null,
        array $options = []
    ): array {
        $relationships = [];
        $createdBy = auth()->id() ?? $options['created_by'] ?? 0;

        $relationshipData = array_merge([
            'relationship_type' => $relationshipType,
            'description' => $description,
            'created_by' => $createdBy,
            'is_active' => true,
            'approval_status' => 'approved',
            'effective_date' => $options['effective_date'] ?? now(),
            'expiry_date' => $options['expiry_date'] ?? null,
        ], $options);

        // Create forward relationship
        $relationships[] = self::create(array_merge($relationshipData, [
            'source_entity_id' => $entityId1,
            'target_entity_id' => $entityId2,
        ]));

        // Create reverse relationship if bidirectional type
        if (in_array($relationshipType, self::BIDIRECTIONAL_TYPES)) {
            $relationships[] = self::create(array_merge($relationshipData, [
                'source_entity_id' => $entityId2,
                'target_entity_id' => $entityId1,
            ]));
        }

        return $relationships;
    }

    /**
     * Check if a relationship exists between two entities.
     *
     * @param int $sourceId
     * @param int $targetId
     * @param string|null $type
     * @param bool $checkBidirectional
     * @return bool
     */
    public static function exists(
        int $sourceId, 
        int $targetId, 
        ?string $type = null, 
        bool $checkBidirectional = false
    ): bool {
        $query = self::where('source_entity_id', $sourceId)
            ->where('target_entity_id', $targetId)
            ->operational();

        if ($type) {
            $query->where('relationship_type', $type);
        }

        $exists = $query->exists();

        // Also check reverse direction if bidirectional check is enabled
        if (!$exists && $checkBidirectional) {
            $reverseQuery = self::where('source_entity_id', $targetId)
                ->where('target_entity_id', $sourceId)
                ->operational();

            if ($type) {
                $reverseQuery->where('relationship_type', $type);
            }

            $exists = $reverseQuery->exists();
        }

        return $exists;
    }

    /**
     * Get all relationships between two entities.
     *
     * @param int $entityId1
     * @param int $entityId2
     * @param bool $operationalOnly
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getBetweenEntities(
        int $entityId1, 
        int $entityId2, 
        bool $operationalOnly = true
    ) {
        $query = self::where(function ($q) use ($entityId1, $entityId2) {
            $q->where(function ($subQ) use ($entityId1, $entityId2) {
                $subQ->where('source_entity_id', $entityId1)
                    ->where('target_entity_id', $entityId2);
            })->orWhere(function ($subQ) use ($entityId1, $entityId2) {
                $subQ->where('source_entity_id', $entityId2)
                    ->where('target_entity_id', $entityId1);
            });
        });

        if ($operationalOnly) {
            $query->operational();
        }

        return $query->with(['sourceEntity', 'targetEntity'])->get();
    }

    /**
     * Get available relationship types for dropdown.
     *
     * @return array
     */
    public static function getRelationshipTypes(): array
    {
        return self::RELATIONSHIP_TYPES;
    }

    /**
     * Get available approval statuses for dropdown.
     *
     * @return array
     */
    public static function getApprovalStatuses(): array
    {
        return self::APPROVAL_STATUSES;
    }

    /**
     * Bulk approve relationships.
     *
     * @param array $relationshipIds
     * @param int|null $approvedBy
     * @return int Number of updated relationships
     */
    public static function bulkApprove(array $relationshipIds, ?int $approvedBy = null): int
    {
        return self::whereIn('id', $relationshipIds)
            ->where('approval_status', 'pending')
            ->update([
                'approval_status' => 'approved',
                'updated_by' => $approvedBy ?? auth()->id(),
                'updated_at' => now()
            ]);
    }

    /**
     * Bulk reject relationships.
     *
     * @param array $relationshipIds
     * @param int|null $rejectedBy
     * @return int Number of updated relationships
     */
    public static function bulkReject(array $relationshipIds, ?int $rejectedBy = null): int
    {
        return self::whereIn('id', $relationshipIds)
            ->where('approval_status', 'pending')
            ->update([
                'approval_status' => 'rejected',
                'updated_by' => $rejectedBy ?? auth()->id(),
                'updated_at' => now()
            ]);
    }
}