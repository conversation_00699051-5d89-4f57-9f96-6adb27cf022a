@php
    $heading = $heading ?? __('Create New Role');
@endphp
<form wire:submit="save" class="space-y-6">
    <!-- Basic Information -->
    <flux:fieldset>
        <flux:legend>{{ __('Basic Information') }}</flux:legend>

        <div class="grid gap-6 md:grid-cols-2">
            <flux:field>
                <flux:label>{{ __('Role Name') }}</flux:label>
                <flux:input wire:model="role_name" placeholder="{{ __('Enter role name') }}" />
                <flux:error name="role_name" />
            </flux:field>

            <flux:field>
                <flux:label>{{ __('Hierarchy Level') }}</flux:label>
                <flux:select wire:model.live="hierarchy_level">
                    @for($i = 3; $i <= 10; $i++)
                        <flux:option value="{{ $i }}">
                            {{ __('Level :level', ['level' => $i]) }}
                            @if($i === 3) ({{ __('Administrator') }}) @endif
                        </flux:option>
                    @endfor
                </flux:select>
                <flux:description>{{ __('Lower numbers indicate higher privileges') }}</flux:description>
                <flux:error name="hierarchy_level" />
            </flux:field>
        </div>

        <flux:field>
            <flux:label>{{ __('Description') }}</flux:label>
            <flux:textarea 
                wire:model="description" 
                placeholder="{{ __('Describe the role responsibilities and scope') }}"
                rows="3" />
            <flux:error name="description" />
        </flux:field>
    </flux:fieldset>

    <!-- Role Hierarchy -->
    <flux:fieldset>
        <flux:legend>{{ __('Role Hierarchy') }}</flux:legend>

        <flux:field>
            <flux:label>{{ __('Parent Role') }}</flux:label>
            <flux:select wire:model="parent_role_id" placeholder="{{ __('Select parent role (optional)') }}">
                @foreach($availableParentRoles as $parentRole)
                    <flux:option value="{{ $parentRole->id }}">
                        {{ $parentRole->role_name }} ({{ __('Level :level', ['level' => $parentRole->hierarchy_level]) }})
                    </flux:option>
                @endforeach
            </flux:select>
            <flux:description>{{ __('Parent role defines inheritance relationships') }}</flux:description>
            <flux:error name="parent_role_id" />
        </flux:field>
    </flux:fieldset>

    <!-- Access Configuration -->
    <flux:fieldset>
        <flux:legend>{{ __('Access Configuration') }}</flux:legend>

        <div class="grid gap-6 md:grid-cols-3">
            <flux:field>
                <flux:label>{{ __('Guard Type') }}</flux:label>
                <flux:select wire:model="guard_type">
                    <flux:option value="web">{{ __('Web Interface') }}</flux:option>
                    <flux:option value="api">{{ __('API Access') }}</flux:option>
                    <flux:option value="mobile">{{ __('Mobile App') }}</flux:option>
                </flux:select>
                <flux:error name="guard_type" />
            </flux:field>

            <flux:field>
                <flux:label>{{ __('Active From') }}</flux:label>
                <flux:input type="date" wire:model="active_from" />
                <flux:error name="active_from" />
            </flux:field>

            <flux:field>
                <flux:label>{{ __('Active Until') }}</flux:label>
                <flux:input type="date" wire:model="active_until" />
                <flux:description>{{ __('Leave empty for no expiration') }}</flux:description>
                <flux:error name="active_until" />
            </flux:field>
        </div>
    </flux:fieldset>

    <!-- Form Actions -->
    <div class="flex items-center justify-between pt-6 border-t border-zinc-200 dark:border-zinc-700">
        <flux:button 
            variant="ghost" 
            href="{{ route('admin.rbac.roles.index') }}"
            wire:navigate>
            {{ __('Cancel') }}
        </flux:button>

        <flux:button type="submit" variant="primary">
            {{ __('Create Role') }}
        </flux:button>
    </div>
</form>
