<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;

class DatabaseChangeLogger
{
    /**
     * Log changes to a model's attributes
     *
     * @param Model $model
     * @param string $action
     * @param array|null $before
     * @param array|null $after
     * @return void
     */
    public function logChanges(Model $model, string $action, ?array $before = null, ?array $after = null): void
    {
        $modelName = get_class($model);
        $modelId = $model->id ?? 'new';
        
        // Get the user ID if authenticated
        $userId = auth()->id() ?? 'system';
        
        // Format basic info
        $logData = [
            'timestamp' => now()->format('Y-m-d H:i:s.u'),
            'user_id' => $userId,
            'ip_address' => request()->ip() ?? 'unknown',
            'action' => $action,
            'table' => $model->getTable(),
            'model' => $modelName,
            'id' => $modelId,
        ];
        
        // For create or update actions, log the changes
        if ($action === 'updated' && !empty($before) && !empty($after)) {
            $changes = [];
            
            foreach ($after as $key => $value) {
                // Skip unchanged fields
                if (!array_key_exists($key, $before) || $before[$key] === $value) {
                    continue;
                }
                
                // Handle serialized fields
                if (isset($model->casts[$key]) && $model->casts[$key] === 'json') {
                    $changes[$key] = [
                        'from' => $this->formatJsonValue($before[$key]),
                        'to' => $this->formatJsonValue($value),
                    ];
                    continue;
                }
                
                // Format the change
                $changes[$key] = [
                    'from' => $before[$key],
                    'to' => $value,
                ];
            }
            
            // Only log if there were actual changes
            if (!empty($changes)) {
                $logData['changes'] = $changes;
            } else {
                // No changes detected, don't log
                return;
            }
        }
        
        // For create, log all attributes
        if ($action === 'created' && !empty($after)) {
            $logData['attributes'] = $after;
        }
        
        // For delete, log the attributes being deleted
        if ($action === 'deleted' && !empty($before)) {
            $logData['attributes'] = $before;
        }
        
        // Convert to JSON for cleaner logging
        $logMessage = json_encode($logData, JSON_PRETTY_PRINT);
        
        // Log to the custom channel
        Log::channel('logdb')->info($logMessage);
    }
    
    /**
     * Format JSON values for better logging
     *
     * @param mixed $value
     * @return mixed
     */
    private function formatJsonValue($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }
        }
        return $value;
    }
} 