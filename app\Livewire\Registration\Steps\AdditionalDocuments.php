<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Auth\RegistrationAttempt;

class AdditionalDocuments extends Component
{
    use WithFileUploads;

    public $attempt;
    public $documents = [];
    public $existingDocuments = [];
    public $otherDocument;
    public $otherDocumentDescription;

    protected $rules = [
        'otherDocument' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
        'otherDocumentDescription' => 'nullable|string|max:255',
    ];

    public function mount()
    {
        if (session()->has('registration_attempt_id')) {
            $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
            
            // Load existing documents if available
            if ($this->attempt && $this->attempt->additional_documents) {
                $this->existingDocuments = $this->attempt->additional_documents;
            }
        }
    }

    public function uploadDocument()
    {
        $this->validate();

        if ($this->otherDocument) {
            $filename = $this->otherDocument->store('registration/additional-documents', 'public');
            
            $document = [
                'filename' => $filename,
                'original_name' => $this->otherDocument->getClientOriginalName(),
                'description' => $this->otherDocumentDescription ?? 'Additional Document',
                'uploaded_at' => now()->toDateTimeString(),
            ];

            // Add to existing documents
            $documents = $this->attempt->additional_documents ?? [];
            $documents[] = $document;

            // Update registration attempt
            $this->attempt->update([
                'additional_documents' => $documents,
            ]);

            // Refresh existing documents
            $this->existingDocuments = $documents;

            // Reset form
            $this->reset(['otherDocument', 'otherDocumentDescription']);
            
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Document uploaded successfully!'
            ]);
        }
    }

    public function removeDocument($index)
    {
        $documents = $this->attempt->additional_documents;
        
        if (isset($documents[$index])) {
            // Remove the file from storage if needed
            // Storage::disk('public')->delete($documents[$index]['filename']);
            
            // Remove from array
            unset($documents[$index]);
            
            // Reindex array
            $documents = array_values($documents);
            
            // Update registration attempt
            $this->attempt->update([
                'additional_documents' => $documents,
            ]);
            
            // Refresh existing documents
            $this->existingDocuments = $documents;
            
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Document removed successfully!'
            ]);
        }
    }

    public function completeStep()
    {
        // Update registration attempt
        $this->attempt->update([
            'current_stage' => 'personal_kyc_documents',
            'completed_stages' => array_merge($this->attempt->completed_stages ?? [], ['additional_documents']),
        ]);

        $this->dispatch('stepCompleted', 'additional_documents');
    }

    public function goBack()
    {
        $this->dispatch('stepBack');
    }

    public function render()
    {
        return view('livewire.registration.steps.additional-documents');
    }
} 