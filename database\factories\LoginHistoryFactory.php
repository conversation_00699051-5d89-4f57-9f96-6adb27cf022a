<?php

namespace Database\Factories;

use App\Models\Auth\LoginHistory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Auth\LoginHistory>
 */
class LoginHistoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = LoginHistory::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'ip_address' => $this->faker->ipv4,
            'user_agent' => $this->faker->userAgent,
            'device_type' => $this->faker->randomElement(['desktop', 'mobile', 'tablet']),
            'platform' => $this->faker->randomElement(['Windows', 'macOS', 'Linux', 'iOS', 'Android']),
            'browser' => $this->faker->randomElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
            'session_id' => $this->faker->uuid,
            'login_at' => now(),
            'login_hour' => now()->hour,
            'login_day' => now()->format('l'),
            'login_successful' => true,
            'is_bot' => false,
            'is_proxy' => false,
            'new_device' => false,
            'suspicious_location' => false,
        ];
    }
    
    /**
     * Indicate that the login was unsuccessful.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function failed()
    {
        return $this->state(function (array $attributes) {
            return [
                'login_successful' => false,
                'failure_reason' => $this->faker->randomElement([
                    'Invalid credentials',
                    'Account inactive',
                    'Email not verified',
                    'Session limit exceeded'
                ]),
            ];
        });
    }
    
    /**
     * Indicate that the session has ended.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function ended()
    {
        return $this->state(function (array $attributes) {
            $loginAt = now()->subHours(rand(1, 5));
            $logoutAt = $loginAt->copy()->addMinutes(rand(5, 120));
            
            return [
                'login_at' => $loginAt,
                'logout_at' => $logoutAt,
                'logout_reason' => $this->faker->randomElement([
                    'user', 'timeout', 'terminated_by_user', 'system'
                ]),
                'duration_seconds' => $logoutAt->diffInSeconds($loginAt),
            ];
        });
    }
} 