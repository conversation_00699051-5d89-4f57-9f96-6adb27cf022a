<?php

namespace App\Mail\Registration;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Entity\Entity;

class DistributorAlertMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * The distributor entity instance.
     *
     * @var \App\Models\Entity\Entity
     */
    public $distributor;

    /**
     * The dealer entity instance.
     *
     * @var \App\Models\Entity\Entity
     */
    public $dealer;

    /**
     * Create a new message instance.
     */
    public function __construct(Entity $distributor, Entity $dealer)
    {
        $this->distributor = $distributor;
        $this->dealer = $dealer;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Dealer Registration Alert',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.registration.distributor-alert',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
} 