<div>
    <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100 dark:bg-gray-900">
        <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white dark:bg-gray-800 shadow-md overflow-hidden sm:rounded-lg">
            <div class="mb-4 text-sm text-gray-600 dark:text-gray-400">
                <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    {{ __('Select Your Role') }}
                </h2>
                <p class="mb-4">
                    {{ __('You have multiple roles in the system. Please select which role you would like to use for this session:') }}
                </p>
            </div>

            @if(isset($roles) && count($roles) > 0)
                <div class="space-y-2 mb-4">
                    <form action="{{ route('auth.select-role') }}" method="post">
                        @csrf
                        
                        <div class="space-y-2">
                            @foreach($roles as $roleAssignment)
                                <div class="p-3 border rounded-md bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
                                    <label class="flex items-center">
                                        <input type="radio" name="role_id" value="{{ $roleAssignment->role_id }}" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600" required>
                                        
                                        <div class="ml-3">
                                            <span class="block font-medium text-gray-700 dark:text-gray-300">
                                                {{ $roleAssignment->role->role_name }}
                                            </span>
                                            
                                            @if($roleAssignment->role->description)
                                                <span class="block text-sm text-gray-500 dark:text-gray-400">
                                                    {{ $roleAssignment->role->description }}
                                                </span>
                                            @endif
                                            
                                            @if($roleAssignment->role->entity)
                                                <span class="block text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    {{ __('Entity:') }} {{ $roleAssignment->role->entity->name }}
                                                </span>
                                            @endif
                                            
                                            @if($roleAssignment->assigned_until)
                                                <span class="block text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    {{ __('Valid until:') }} {{ $roleAssignment->assigned_until->format('M d, Y') }}
                                                </span>
                                            @endif
                                        </div>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                {{ __('Continue with Selected Role') }}
                            </button>
                        </div>
                    </form>
                </div>
            @else
                <div class="p-4 border rounded bg-yellow-50 border-yellow-200 text-yellow-700 mb-4">
                    {{ __('No roles are available for your account. Please contact your administrator.') }}
                </div>
            @endif

            <div class="mt-4">
                <form action="{{ route('logout') }}" method="post">
                    @csrf
                    <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        {{ __('Logout') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div> 