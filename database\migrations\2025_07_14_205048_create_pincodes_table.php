<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Creates the 'pincodes' table to store area, pincode, district, and state information.
        Schema::create('pincodes', function (Blueprint $table) {
            $table->id(); // Primary key for the table.
            $table->string('area'); // To store the area name.
            $table->integer('pincode'); // To store the 6-digit pincode.
            $table->string('district'); // To store the district name.
            $table->string('state_name'); // To store the state name.
            $table->timestamps(); // Adds 'created_at' and 'updated_at' columns.
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drops the 'pincodes' table if the migration is rolled back.
        Schema::dropIfExists('pincodes');
    }
};
