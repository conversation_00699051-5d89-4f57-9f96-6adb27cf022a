{{-- resources/views/components/flux/card/header.blade.php --}}
@props([
'title' => null,
'subtitle' => null,
'class' => '',
])

<div {{ $attributes->merge(['class' => "border-b border-zinc-100 dark:border-zinc-700 px-6 py-4 flex items-center justify-between gap-4 $class"]) }}>
    <div>
        @if($title)
        <h3 class="text-lg font-semibold text-zinc-800 dark:text-zinc-100">{{ $title }}</h3>
        @endif
        @if($subtitle)
        <p class="mt-1 text-sm text-zinc-500 dark:text-zinc-400">{{ $subtitle }}</p>
        @endif
    </div>

    @if(isset($actions))
    <div class="flex items-center gap-2">
        {{ $actions }}
    </div>
    @endif

    {{ $slot }}
</div>