<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\Auth\RegistrationAttempt;

class TaxInformation extends Component
{
    public $attempt;
    
    public $taxType = 'gst';  // Default to GST
    public $taxIdentifier = '';
    public $taxRegion = '';
    public $effectiveDate = null;
    public $expiryDate = null;
    
    protected $taxTypeOptions = [
        'gst' => 'Goods and Services Tax (GST)',
        'pan' => 'Permanent Account Number (PAN)',
        'tin' => 'Tax Identification Number (TIN)',
        'other' => 'Other Tax Identifier'
    ];
    
    protected $taxRegionOptions = [
        'national' => 'National',
        'state' => 'State/UT Level',
        'international' => 'International'
    ];
    
    protected function rules()
    {
        $rules = [
            'taxType' => 'required|string|in:gst,pan,tin,other',
            'taxRegion' => 'required|string|in:national,state,international',
            'effectiveDate' => 'nullable|date',
            'expiryDate' => 'nullable|date|after:effectiveDate'
        ];
        
        // Add conditional validation for tax identifier based on type
        switch ($this->taxType) {
            case 'gst':
                $rules['taxIdentifier'] = [
                    'required', 
                    'regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/'
                ];
                break;
                
            case 'pan':
                $rules['taxIdentifier'] = [
                    'required', 
                    'regex:/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/'
                ];
                break;
                
            case 'tin':
                $rules['taxIdentifier'] = [
                    'required', 
                    'regex:/^[0-9]{11}$/'
                ];
                break;
                
            default:
                $rules['taxIdentifier'] = 'required|string|min:5|max:30';
                break;
        }
        
        return $rules;
    }
    
    protected $messages = [
        'taxIdentifier.required' => 'Tax identifier is required',
        'taxIdentifier.regex' => 'Invalid format for the selected tax type',
        'taxType.required' => 'Please select a tax type',
        'taxRegion.required' => 'Please select a tax region',
        'effectiveDate.date' => 'Effective date must be a valid date',
        'expiryDate.date' => 'Expiry date must be a valid date',
        'expiryDate.after' => 'Expiry date must be after effective date'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        if (!$this->attempt) {
            return redirect()->route('register');
        }
        
        // Load saved data if exists
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        if (isset($stagesData['tax_information'])) {
            $data = $stagesData['tax_information'];
            $this->taxType = $data['tax_type'] ?? 'gst';
            $this->taxIdentifier = $data['tax_identifier'] ?? '';
            $this->taxRegion = $data['tax_region'] ?? 'national';
            $this->effectiveDate = $data['effective_date'] ?? null;
            $this->expiryDate = $data['expiry_date'] ?? null;
        }
        
        // Try to get state from business address for pre-selecting region
        if (empty($this->taxRegion) && isset($stagesData['business_information']['addresses'][0]['state'])) {
            $this->taxRegion = 'state';
        }
    }
    
    public function getTaxTypeOptionsProperty()
    {
        return $this->taxTypeOptions;
    }
    
    public function getTaxRegionOptionsProperty()
    {
        return $this->taxRegionOptions;
    }
    
    public function getFormatHintProperty()
    {
        switch ($this->taxType) {
            case 'gst':
                return '22**********1Z5 (15 characters)';
            case 'pan':
                return '********** (10 characters)';
            case 'tin':
                return '*********** (11 digits)';
            default:
                return '';
        }
    }
    
    public function saveAndContinue()
    {
        $this->validate();
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['tax_information'] = [
            'tax_type' => $this->taxType,
            'tax_identifier' => $this->taxIdentifier,
            'tax_region' => $this->taxRegion,
            'effective_date' => $this->effectiveDate ?? null,
            'expiry_date' => $this->expiryDate ?? null
        ];
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('tax_information', $stagesCompleted)) {
            $stagesCompleted[] = 'tax_information';
        }
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'business_kyc_documents'
        ]);
        
        $this->dispatch('stepCompleted', 'tax_information');
    }
    
    public function stepBack()
    {
        $this->dispatch('stepBack');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.tax-information');
    }
} 