here's the breakdown of the three primary roles in this system:

Super Administrator:
System-wide role with unrestricted access
Reserved for the platform owner (User ID 1)
Hierarchy level: 1 (highest privilege)
Has automatic access to all permissions and features.
Can manage all roles, users, and entities in the system without restrictions

Administrator Manager:
Top-level entity role with full access within its own entity
Hierarchy level: 2
Can create new roles within their entity.
Can manage roles with lower privilege levels (higher hierarchy numbers).
Can view and manage user role assignments within their entity.
Can only manage roles belonging to their own entity.
Limited to one per entity.

Administrator:
Entity admin with access to all operational modules
Hierarchy level: 3
Can view roles and role assignments within their entity
Can create role assignments but cannot create new roles
Can manage role assignments for users with lower privilege levels
Can only manage users and assignments within their own entity
Multiple administrators can exist per entity


The role system is hierarchical, with each role having a specific hierarchy level (lower number = higher privilege). The system enforces that users can only manage roles and users at lower privilege levels than their own. The policies enforce entity-level isolation, where users can only manage resources within their own entity.

Role-Specific Session Policies - Configure different session timeout durations based on role sensitivity (shorter timeouts for higher-privilege roles).
