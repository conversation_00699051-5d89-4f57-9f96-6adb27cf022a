<div>
    <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100 dark:bg-gray-900">
        <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-white dark:bg-gray-800 shadow-md overflow-hidden sm:rounded-lg">
            <div class="mb-4 text-sm text-gray-600 dark:text-gray-400">
                <h2 class="text-lg font-medium text-red-600 dark:text-red-400 mb-4">
                    {{ __('Access Restricted') }}
                </h2>
                
                <div class="p-4 mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800 dark:text-red-200">
                                {{ $message ?? __('Your access has been restricted due to suspicious activity.') }}
                            </p>
                        </div>
                    </div>
                </div>
                
                <p class="mb-4">
                    {{ __('Our security system has detected unusual activity from your IP address.') }}
                </p>
                
                @if(isset($ip))
                <p class="text-xs text-gray-500 dark:text-gray-400 mb-4">
                    {{ __('IP Address:') }} {{ $ip }}
                </p>
                @endif
                
                <p class="mb-4">
                    {{ __('If you believe this is an error, please contact our support team for assistance.') }}
                </p>
            </div>

            <div class="mt-4 flex justify-between">
                <a href="{{ url('/') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ __('Return to Home') }}
                </a>
                
                <a href="mailto:{{ config('app.support_email', '<EMAIL>') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ __('Contact Support') }}
                </a>
            </div>
        </div>
    </div>
</div> 