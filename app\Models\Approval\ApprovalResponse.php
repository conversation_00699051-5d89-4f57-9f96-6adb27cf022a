<?php
// app\Models\Approval\ApprovalResponse.php
namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Models\Rbac\SystemRole;
use App\Traits\HasAuditFields;

/**
 * Class ApprovalResponse
 *
 * Records individual approver decisions on approval requests.
 * Tracks who approved/rejected, when, and with what comments.
 *
 * @property int $id
 * @property string $request_id UUID of approval request
 * @property int $step_number Which workflow step
 * @property int $approver_id User who responded
 * @property int $role_id Role under which approval was given
 * @property string $response_type Response (approved|rejected|deferred)
 * @property string|null $comments Approver's notes
 * @property array|null $attachments Document references
 * @property \Carbon\Carbon $responded_at When response was made
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class ApprovalResponse extends Model
{
    use SoftDeletes, HasAuditFields;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'request_id',
        'step_number',
        'approver_id',
        'role_id',
        'response_type',
        'comments',
        'attachments',
        'responded_at',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'step_number' => 'integer',
        'attachments' => 'array',
        'responded_at' => 'datetime',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    /**
     * Response types.
     */
    const RESPONSE_TYPES = [
        'approved' => 'Request approved',
        'rejected' => 'Request rejected',
        'deferred' => 'Decision deferred',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = ['is_approval', 'is_rejection', 'has_comments', 'has_attachments'];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->responded_at) {
                $model->responded_at = now();
            }
        });

        static::created(function ($model) {
            // Update the approval request
            $model->updateApprovalRequest();

            // Log the response
            $model->logResponse();
        });
    }

    /**
     * Check if this is an approval.
     *
     * @return bool
     */
    public function getIsApprovalAttribute(): bool
    {
        return $this->response_type === 'approved';
    }

    /**
     * Check if this is a rejection.
     *
     * @return bool
     */
    public function getIsRejectionAttribute(): bool
    {
        return $this->response_type === 'rejected';
    }

    /**
     * Check if response has comments.
     *
     * @return bool
     */
    public function getHasCommentsAttribute(): bool
    {
        return !empty($this->comments);
    }

    /**
     * Check if response has attachments.
     *
     * @return bool
     */
    public function getHasAttachmentsAttribute(): bool
    {
        return !empty($this->attachments);
    }

    /**
     * Get response type label.
     *
     * @return string
     */
    public function getResponseTypeLabelAttribute(): string
    {
        return self::RESPONSE_TYPES[$this->response_type] ?? 'Unknown';
    }

    /**
     * Get response duration from request submission.
     *
     * @return \Carbon\CarbonInterval|null
     */
    public function getResponseDurationAttribute()
    {
        if (!$this->request || !$this->request->submitted_at) {
            return null;
        }

        return $this->request->submitted_at->diffAsCarbonInterval($this->responded_at);
    }

    /**
     * Update the approval request after response.
     */
    protected function updateApprovalRequest(): void
    {
        $this->request->processStepResponse(
            $this->step_number,
            $this->response_type,
            $this->approver_id
        );
    }

    /**
     * Log the response action.
     */
    protected function logResponse(): void
    {
        ApprovalLog::create([
            'request_id' => $this->request_id,
            'user_id' => $this->approver_id,
            'action' => $this->response_type,
            'details' => $this->comments,
        ]);
    }

    /**
     * Add attachment reference.
     *
     * @param string $attachmentId
     * @param array $metadata
     * @return bool
     */
    public function addAttachment(string $attachmentId, array $metadata = []): bool
    {
        $attachments = $this->attachments ?? [];

        $attachments[] = array_merge([
            'id' => $attachmentId,
            'added_at' => now()->toIso8601String(),
        ], $metadata);

        $this->attachments = $attachments;
        return $this->save();
    }

    /**
     * Remove attachment reference.
     *
     * @param string $attachmentId
     * @return bool
     */
    public function removeAttachment(string $attachmentId): bool
    {
        $attachments = collect($this->attachments ?? [])
            ->reject(fn($att) => $att['id'] === $attachmentId)
            ->values()
            ->toArray();

        $this->attachments = $attachments;
        return $this->save();
    }

    // ===== RELATIONSHIPS =====

    /**
     * Get the approval request this response belongs to.
     *
     * @return BelongsTo
     */
    public function request(): BelongsTo
    {
        return $this->belongsTo(ApprovalRequest::class, 'request_id');
    }

    /**
     * Get the user who made this response.
     *
     * @return BelongsTo
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    /**
     * Get the role under which approval was given.
     *
     * @return BelongsTo
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(SystemRole::class, 'role_id');
    }

    /**
     * Get the workflow step this response is for.
     *
     * @return BelongsTo
     */
    public function step(): BelongsTo
    {
        return $this->belongsTo(ApprovalStep::class, 'step_number', 'step_number')
            ->where('workflow_id', $this->request->workflow_id);
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to approvals only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApprovals($query)
    {
        return $query->where('response_type', 'approved');
    }

    /**
     * Scope to rejections only.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRejections($query)
    {
        return $query->where('response_type', 'rejected');
    }

    /**
     * Scope to responses by a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByApprover($query, int $userId)
    {
        return $query->where('approver_id', $userId);
    }

    /**
     * Scope to responses for a specific role.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $roleId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForRole($query, int $roleId)
    {
        return $query->where('role_id', $roleId);
    }

    /**
     * Scope to responses with comments.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithComments($query)
    {
        return $query->whereNotNull('comments')
            ->where('comments', '!=', '');
    }

    /**
     * Scope to recent responses.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $hours
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('responded_at', '>', now()->subHours($hours));
    }

    // ===== STATIC METHODS =====

    /**
     * Create an approval response.
     *
     * @param ApprovalRequest $request
     * @param int $stepNumber
     * @param User $approver
     * @param array $data
     * @return self
     */
    public static function approve(
        ApprovalRequest $request,
        int $stepNumber,
        User $approver,
        array $data = []
    ): self {
        return self::create(array_merge([
            'request_id' => $request->id,
            'step_number' => $stepNumber,
            'approver_id' => $approver->id,
            'role_id' => $data['role_id'] ?? $approver->roles->first()?->id,
            'response_type' => 'approved',
        ], $data));
    }

    /**
     * Create a rejection response.
     *
     * @param ApprovalRequest $request
     * @param int $stepNumber
     * @param User $approver
     * @param string $reason
     * @param array $data
     * @return self
     */
    public static function reject(
        ApprovalRequest $request,
        int $stepNumber,
        User $approver,
        string $reason,
        array $data = []
    ): self {
        return self::create(array_merge([
            'request_id' => $request->id,
            'step_number' => $stepNumber,
            'approver_id' => $approver->id,
            'role_id' => $data['role_id'] ?? $approver->roles->first()?->id,
            'response_type' => 'rejected',
            'comments' => $reason,
        ], $data));
    }

    /**
     * Get response statistics for a user.
     *
     * @param int $userId
     * @param \Carbon\Carbon|null $from
     * @param \Carbon\Carbon|null $to
     * @return array
     */
    public static function getUserStatistics(
        int $userId,
        ?\Carbon\Carbon $from = null,
        ?\Carbon\Carbon $to = null
    ): array {
        $query = self::byApprover($userId);

        if ($from) {
            $query->where('responded_at', '>=', $from);
        }

        if ($to) {
            $query->where('responded_at', '<=', $to);
        }

        $responses = $query->get();

        return [
            'total_responses' => $responses->count(),
            'approvals' => $responses->where('response_type', 'approved')->count(),
            'rejections' => $responses->where('response_type', 'rejected')->count(),
            'deferrals' => $responses->where('response_type', 'deferred')->count(),
            'average_response_time' => $responses->avg(function ($response) {
                return $response->response_duration?->totalHours ?? 0;
            }),
            'with_comments' => $responses->where('has_comments', true)->count(),
        ];
    }
}
