<?php
// app/Models/SiteSetting/Notification.php
namespace App\Models\SiteSetting;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Entity\Entity;
use App\Models\User;

/**
 * Class Notification
 *
 * Represents notification preferences that can be associated with both entities and users.
 * Uses polymorphic many-to-many relationships via the notifiables pivot table.
 *
 * Notification preferences control how users and entities receive system notifications
 * across various channels: email, SMS, WhatsApp, mobile app, and push notifications.
 * Each preference set can be shared across multiple entities/users or be unique.
 *
 * @property int $id
 * @property bool $via_email Receive notifications via email
 * @property bool $via_sms Receive notifications via SMS
 * @property bool $via_whatsapp Receive notifications via WhatsApp
 * @property string|null $whatsapp_number Phone number for WhatsApp notifications
 * @property bool $via_mobile_app Receive notifications via mobile app
 * @property string|null $mobile_app_id Device/user ID for mobile app notifications
 * @property bool $via_push Receive browser/app push notifications
 * @property string|null $push_token Push notification token
 * @property bool $is_active Status: true=active, false=inactive
 * @property int $created_by User ID who created this record
 * @property int|null $updated_by User ID who last updated this record
 * @property int|null $deleted_by User ID who soft deleted this record
 * @property int|null $restored_by User ID who restored this record
 * @property \Carbon\Carbon|null $restored_at When the record was restored
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder withEmail()
 * @method static \Illuminate\Database\Eloquent\Builder withSms()
 * @method static \Illuminate\Database\Eloquent\Builder withWhatsapp()
 * @method static \Illuminate\Database\Eloquent\Builder withMobileApp()
 * @method static \Illuminate\Database\Eloquent\Builder withPush()
 */
class Notification extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'notifications';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'via_email',
        'via_sms',
        'via_whatsapp',
        'whatsapp_number',
        'via_mobile_app',
        'mobile_app_id',
        'via_push',
        'push_token',
        'is_active',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'via_email' => 'boolean',
        'via_sms' => 'boolean',
        'via_whatsapp' => 'boolean',
        'via_mobile_app' => 'boolean',
        'via_push' => 'boolean',
        'is_active' => 'boolean',
        'restored_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'enabled_channels',
        'channel_count',
        'has_mobile_channels',
        'is_fully_configured'
    ];

    /**
     * Available notification channels.
     *
     * @var array<string, string>
     */
    const CHANNELS = [
        'email' => 'Email',
        'sms' => 'SMS',
        'whatsapp' => 'WhatsApp',
        'mobile_app' => 'Mobile App',
        'push' => 'Push Notifications'
    ];

    // ===== ACCESSORS =====

    /**
     * Get list of enabled notification channels.
     *
     * @return array
     */
    public function getEnabledChannelsAttribute(): array
    {
        $enabled = [];

        if ($this->via_email) $enabled[] = 'email';
        if ($this->via_sms) $enabled[] = 'sms';
        if ($this->via_whatsapp) $enabled[] = 'whatsapp';
        if ($this->via_mobile_app) $enabled[] = 'mobile_app';
        if ($this->via_push) $enabled[] = 'push';

        return $enabled;
    }

    /**
     * Get count of enabled channels.
     *
     * @return int
     */
    public function getChannelCountAttribute(): int
    {
        return count($this->enabled_channels);
    }

    /**
     * Check if mobile channels (SMS, WhatsApp, Mobile App) are enabled.
     *
     * @return bool
     */
    public function getHasMobileChannelsAttribute(): bool
    {
        return $this->via_sms || $this->via_whatsapp || $this->via_mobile_app;
    }

    /**
     * Check if notification preferences are fully configured.
     *
     * @return bool
     */
    public function getIsFullyConfiguredAttribute(): bool
    {
        return $this->isFullyConfigured();
    }

    // ===== HELPER METHODS =====

    /**
     * Check if notification preferences are fully configured.
     * Considers required tokens/IDs for enabled channels.
     *
     * @return bool
     */
    public function isFullyConfigured(): bool
    {
        // Check WhatsApp configuration
        if ($this->via_whatsapp && empty($this->whatsapp_number)) {
            return false;
        }

        // Check mobile app configuration
        if ($this->via_mobile_app && empty($this->mobile_app_id)) {
            return false;
        }

        // Check push notification configuration
        if ($this->via_push && empty($this->push_token)) {
            return false;
        }

        // At least one channel should be enabled
        return $this->channel_count > 0;
    }

    /**
     * Check if a specific channel is enabled.
     *
     * @param string $channel
     * @return bool
     */
    public function isChannelEnabled(string $channel): bool
    {
        return match ($channel) {
            'email' => $this->via_email,
            'sms' => $this->via_sms,
            'whatsapp' => $this->via_whatsapp,
            'mobile_app' => $this->via_mobile_app,
            'push' => $this->via_push,
            default => false
        };
    }

    /**
     * Enable a specific notification channel.
     *
     * @param string $channel
     * @param array $config Additional configuration for the channel
     * @return bool
     */
    public function enableChannel(string $channel, array $config = []): bool
    {
        $updates = [];

        switch ($channel) {
            case 'email':
                $updates['via_email'] = true;
                break;
            case 'sms':
                $updates['via_sms'] = true;
                break;
            case 'whatsapp':
                $updates['via_whatsapp'] = true;
                if (isset($config['whatsapp_number'])) {
                    $updates['whatsapp_number'] = $config['whatsapp_number'];
                }
                break;
            case 'mobile_app':
                $updates['via_mobile_app'] = true;
                if (isset($config['mobile_app_id'])) {
                    $updates['mobile_app_id'] = $config['mobile_app_id'];
                }
                break;
            case 'push':
                $updates['via_push'] = true;
                if (isset($config['push_token'])) {
                    $updates['push_token'] = $config['push_token'];
                }
                break;
            default:
                return false;
        }

        return $this->update($updates);
    }

    /**
     * Disable a specific notification channel.
     *
     * @param string $channel
     * @return bool
     */
    public function disableChannel(string $channel): bool
    {
        $updates = [];

        switch ($channel) {
            case 'email':
                $updates['via_email'] = false;
                break;
            case 'sms':
                $updates['via_sms'] = false;
                break;
            case 'whatsapp':
                $updates['via_whatsapp'] = false;
                $updates['whatsapp_number'] = null;
                break;
            case 'mobile_app':
                $updates['via_mobile_app'] = false;
                $updates['mobile_app_id'] = null;
                break;
            case 'push':
                $updates['via_push'] = false;
                $updates['push_token'] = null;
                break;
            default:
                return false;
        }

        return $this->update($updates);
    }

    /**
     * Get formatted WhatsApp number.
     *
     * @return string|null
     */
    public function getFormattedWhatsappNumber(): ?string
    {
        if (!$this->whatsapp_number) {
            return null;
        }

        // Remove non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $this->whatsapp_number);

        // Format based on length (assuming Indian format)
        if (strlen($number) === 10) {
            return '+91 ' . substr($number, 0, 5) . ' ' . substr($number, 5);
        }

        return $this->whatsapp_number;
    }

    /**
     * Check if push token is still valid (basic check).
     *
     * @return bool
     */
    public function hasPushToken(): bool
    {
        return !empty($this->push_token) && strlen($this->push_token) > 10;
    }

    // ===== POLYMORPHIC RELATIONSHIPS =====

    /**
     * Get all entities that have these notification preferences.
     *
     * @return MorphToMany
     */
    public function entities(): MorphToMany
    {
        return $this->morphedByMany(Entity::class, 'notifiable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get all users that have these notification preferences.
     *
     * @return MorphToMany
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'notifiable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get all models (entities and users) that have these notification preferences.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAllNotifiables()
    {
        return collect()
            ->merge($this->entities)
            ->merge($this->users);
    }

    /**
     * Check if these notification preferences are primary for any model.
     *
     * @return bool
     */
    public function isPrimaryForAny(): bool
    {
        $entityPrimary = $this->entities()->wherePivot('is_primary', true)->exists();
        $userPrimary = $this->users()->wherePivot('is_primary', true)->exists();

        return $entityPrimary || $userPrimary;
    }

    // ===== AUDIT RELATIONSHIPS =====

    /**
     * Get the user who created these notification preferences.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated these notification preferences.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who soft deleted these notification preferences.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored these notification preferences.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to only active notification preferences.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to notification preferences with email enabled.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithEmail($query)
    {
        return $query->where('via_email', true);
    }

    /**
     * Scope to notification preferences with SMS enabled.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithSms($query)
    {
        return $query->where('via_sms', true);
    }

    /**
     * Scope to notification preferences with WhatsApp enabled.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithWhatsapp($query)
    {
        return $query->where('via_whatsapp', true);
    }

    /**
     * Scope to notification preferences with mobile app enabled.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithMobileApp($query)
    {
        return $query->where('via_mobile_app', true);
    }

    /**
     * Scope to notification preferences with push notifications enabled.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithPush($query)
    {
        return $query->where('via_push', true);
    }

    /**
     * Scope to fully configured notification preferences.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFullyConfigured($query)
    {
        return $query->where(function ($q) {
            $q->where('via_email', true)
                ->orWhere('via_sms', true)
                ->orWhere(function ($subQ) {
                    $subQ->where('via_whatsapp', true)
                        ->whereNotNull('whatsapp_number');
                })
                ->orWhere(function ($subQ) {
                    $subQ->where('via_mobile_app', true)
                        ->whereNotNull('mobile_app_id');
                })
                ->orWhere(function ($subQ) {
                    $subQ->where('via_push', true)
                        ->whereNotNull('push_token');
                });
        });
    }

    /**
     * Scope to notification preferences with mobile channels.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithMobileChannels($query)
    {
        return $query->where(function ($q) {
            $q->where('via_sms', true)
                ->orWhere('via_whatsapp', true)
                ->orWhere('via_mobile_app', true);
        });
    }

    // ===== STATIC METHODS =====

    /**
     * Create default notification preferences.
     *
     * @param array $overrides Override default settings
     * @return self
     */
    public static function createDefault(array $overrides = []): self
    {
        $defaults = [
            'via_email' => true,
            'via_sms' => false,
            'via_whatsapp' => false,
            'via_mobile_app' => false,
            'via_push' => false,
            'is_active' => true,
            'created_by' => auth()->id() ?? 0,
        ];

        return self::create(array_merge($defaults, $overrides));
    }

    /**
     * Find notification preferences by push token.
     *
     * @param string $pushToken
     * @return self|null
     */
    public static function findByPushToken(string $pushToken): ?self
    {
        return self::where('push_token', $pushToken)
            ->where('via_push', true)
            ->first();
    }

    /**
     * Find notification preferences by mobile app ID.
     *
     * @param string $mobileAppId
     * @return self|null
     */
    public static function findByMobileAppId(string $mobileAppId): ?self
    {
        return self::where('mobile_app_id', $mobileAppId)
            ->where('via_mobile_app', true)
            ->first();
    }

    /**
     * Find notification preferences by WhatsApp number.
     *
     * @param string $whatsappNumber
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findByWhatsappNumber(string $whatsappNumber)
    {
        return self::where('whatsapp_number', $whatsappNumber)
            ->where('via_whatsapp', true)
            ->get();
    }

    /**
     * Get available channels for dropdown.
     *
     * @return array
     */
    public static function getAvailableChannels(): array
    {
        return self::CHANNELS;
    }
}