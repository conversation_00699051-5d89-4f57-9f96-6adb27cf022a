<?php

// app/Models/Rbac/SystemPermission.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Entity\Entity;
use App\Traits\HasAuditFields;

/**
 * Class SystemPermission
 *
 * Represents individual permissions in the system.
 *
 * @property int $id
 * @property int|null $entity_id Associated entity identifier
 * @property string $permission_code Machine-readable permission code
 * @property string $permission_name Human-readable permission name
 * @property string $permission_type Type of permission (e.g., "create", "read", "update", "delete")
 * @property string $description Human-readable permission description
 * @property bool $is_active Activation status
 * @property bool $is_approval_required Flag indicating if approval is needed
 * @property string $approval_status Current approval status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 */
class SystemPermission extends Model
{
    use HasAuditFields;

    protected $fillable = [
        'entity_id',
        'permission_code',
        'permission_name',
        'permission_type',
        'description',
        'is_active',
        'is_approval_required',
        'approval_status',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
    ];

    protected $appends = ['category', 'action'];

    /**
     * Get permission category from code.
     *
     * @return string
     */
    public function getCategoryAttribute(): string
    {
        return explode('.', $this->permission_code)[0] ?? 'unknown';
    }

    /**
     * Get permission action from code.
     *
     * @return string
     */
    public function getActionAttribute(): string
    {
        return explode('.', $this->permission_code)[1] ?? 'unknown';
    }

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function grants(): HasMany
    {
        return $this->hasMany(PermissionGrant::class, 'permission_id');
    }

    // ===== QUERY SCOPES =====

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('permission_code', 'like', $category . '.%');
    }

    public function scopeForEntity($query, string $entityId)
    {
        return $query->where('entity_id', $entityId);
    }
}
