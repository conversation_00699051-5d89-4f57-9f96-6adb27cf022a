<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('magic_links', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->index()->comment('User ID');
            $table->string('email')->index()->comment('Email address');
            $table->string('token', 64)->unique()->comment('Unique token for the magic link');
            $table->integer('validity_in_minutes')->default(10)->comment('Validity in minutes');
            $table->timestamp('expires_at')->comment('Expiration date and time');
            $table->timestamp('used_at')->nullable()->comment('Timestamp when the magic link was used');
            $table->unsignedBigInteger('history_id')->nullable()->comment('History ID from history table');
            $table->timestamp('cancelled_at')->nullable()->comment('Timestamp when the magic link was cancelled');
            $table->ipAddress('created_ip')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('magic_links');
    }
}; 