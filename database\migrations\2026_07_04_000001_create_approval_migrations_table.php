<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Creates the core workflow definition table.
     */
    public function up(): void
    {
        Schema::create('approval_workflows', function (Blueprint $table) {
            $table->id();
            $table->string('entity_id', 25)->nullable()->index()->comment('Business entity identifier. Null means global fallback rule');
            $table->string('approvable_type')->index()->comment('The model class this workflow applies to');
            $table->string('action_type')->index()->comment('The specific action triggering this workflow');
            $table->string('name')->comment('Human-readable workflow name');
            $table->boolean('is_active')->default(true)->index()->comment('Set to false to temporarily disable this workflow');
            $table->enum('strategy', ['single', 'sequential', 'parallel'])->default('sequential')->comment('single=1 approver, sequential=all steps in order, parallel=any X of Y');
            $table->unsignedInteger('required_approvals')->default(1)->comment('For parallel strategy: how many approvals are needed');
            $table->unsignedInteger('max_rejections')->default(1)->comment('How many rejections will cancel the request');
            $table->boolean('allow_self_approval')->default(false)->comment('If false, requester cannot approve their own request');
            $table->json('custom_conditions')->nullable()->comment('Special rules like amount thresholds, department checks, etc');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->unique(['entity_id', 'approvable_type', 'action_type'], 'workflow_definition_unique');
        });

        Schema::create('approval_steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_id')->constrained('approval_workflows')->cascadeOnDelete()->comment('Which workflow this step belongs to');
            $table->unsignedInteger('step_number')->index()->comment('Order of this step in the workflow');
            $table->foreignId('role_id')->constrained('system_roles')->comment('Role that can approve this step');
            $table->boolean('is_required')->default(true)->comment('If false, this step can be skipped in parallel approvals');
            $table->boolean('notify_approver')->default(true)->comment('Send notification when this step is pending');
            $table->json('approval_criteria')->nullable()->comment('JSON conditions like department matching, amount limits, etc.');
            // For conditional steps. Stores a rule, e.g., {"field": "amount", "operator": ">", "value": 50000}
            $table->json('condition')->nullable()->comment('JSON rule that must be met for this step to be active.');
            // For escalations. Defines the Service Level Agreement (SLA) in hours.
            $table->unsignedInteger('escalation_after_hours')->nullable()->comment('Escalate this step if not approved after this many hours.');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');

            $table->unique(['workflow_id', 'step_number'], 'step_number_unique');
        });

        Schema::create('approval_requests', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignId('workflow_id')->constrained('approval_workflows')->cascadeOnDelete()->comment('Which workflow this request follows');
            $table->uuidMorphs('approvable');
            $table->string('entity_id', 25)->nullable()->index()->comment('Business entity where this request originated');
            $table->string('action_type')->comment('create/update/delete/activate/etc.');
            $table->foreignId('requester_id')->constrained('users')->comment('User who requested the action');
            $table->string('status')->default('pending')->index()->comment('Current state of the approval process');
            $table->json('request_data')->comment('Original data submitted for approval');
            $table->json('approval_data')->nullable()->comment('Generated approval steps with current progress');
            $table->timestamp('submitted_at')->nullable()->comment('When request was formally submitted');
            $table->timestamp('completed_at')->nullable()->comment('When final approval/rejection occurred');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
        });

        Schema::create('approval_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('request_id')->constrained('approval_requests')->cascadeOnDelete()->comment('Which request this response is for');
            $table->unsignedInteger('step_number')->comment('Which workflow step this response applies to');
            $table->foreignId('approver_id')->constrained('users')->comment('User who approved/rejected');
            $table->foreignId('role_id')->constrained('system_roles')->comment('Role under which approval was given');
            $table->enum('response_type', ['approved', 'rejected', 'deferred'])->comment('The action taken by the approver');
            $table->text('comments')->nullable()->comment('Approver notes about their decision');
            $table->json('attachments')->nullable()->comment('Array of attachment IDs from your document system');
            $table->timestamp('responded_at')->comment('Timestamp of approval/rejection');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
        });

        Schema::create('approval_escalations', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('request_id')->constrained('approval_requests')->cascadeOnDelete();
            $table->integer('step_number');
            $table->foreignId('escalated_to_user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->text('reason');
            $table->string('status')->default('pending'); // pending, acknowledged, resolved
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
        });

        Schema::create('approval_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('request_id')->constrained('approval_requests')->cascadeOnDelete();
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete()->comment('User who performed the action, null for system actions.');
            $table->string('action')->index()->comment('e.g., created, approved, rejected, escalated, commented');
            $table->text('details')->nullable()->comment('Contextual details, like comments or system messages.');
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('approval_workflows');
        Schema::dropIfExists('approval_steps');
        Schema::dropIfExists('approval_requests');
        Schema::dropIfExists('approval_responses');
        Schema::dropIfExists('approval_escalations');
        Schema::dropIfExists('approval_logs');
    }
};
