@props([
    'href' => '#',
    'active' => false,
    'icon' => null,
    'badge' => null,
])

@php
    $classes = 'flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors';
    
    if ($active) {
        $classes .= ' bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-white';
    } else {
        $classes .= ' text-zinc-600 hover:bg-zinc-50 hover:text-zinc-900 dark:text-zinc-400 dark:hover:bg-zinc-800/50 dark:hover:text-white';
    }
@endphp

<a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
    @if($icon)
        <span class="flex-shrink-0 w-5 h-5">
            @include('flux.icon.' . $icon)
        </span>
    @endif
    
    <span class="truncate">{{ $slot }}</span>
    
    @if($badge)
        <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-zinc-100 text-zinc-800 dark:bg-zinc-700 dark:text-zinc-300">
            {{ $badge }}
        </span>
    @endif
</a> 