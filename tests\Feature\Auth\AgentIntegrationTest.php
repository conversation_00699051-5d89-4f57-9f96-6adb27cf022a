<?php

namespace Tests\Feature\Auth;

use App\Agent\Facades\Agent;
use App\Livewire\Auth\Login;
use App\Models\Auth\LoginHistory;
use App\Models\User;
use App\Services\LoginTrackingService;
use App\Services\LoginValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Livewire\Livewire;
use Mockery;
use Tests\TestCase;

class AgentIntegrationTest extends TestCase
{
    use RefreshDatabase;
    
    public function test_agent_detects_browser_on_login()
    {
        // Set up a mock for Agent facade
        $this->mockAgent('Chrome', '115.0.0.0', 'Windows', '10.0', 'Desktop');
        
        // Create a user
        $user = User::factory()->create([
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        
        // Mock the login tracking service
        $loginTracker = Mockery::mock(LoginTrackingService::class);
        $loginTracker->shouldReceive('recordSuccessfulLogin')
            ->once()
            ->andReturn(new LoginHistory([
                'user_id' => $user->id,
                'login_successful' => true,
                'browser' => 'Chrome',
                'browser_version' => '115.0.0.0',
                'platform' => 'Windows',
                'platform_version' => '10.0',
                'device_type' => 'Desktop',
            ]));

        // Mock the login validation service
        $loginValidator = Mockery::mock(LoginValidationService::class);
        $loginValidator->shouldReceive('validateLogin')
            ->once()
            ->andReturn(true);
        $loginValidator->shouldReceive('isSuperAdmin')
            ->once()
            ->andReturn(false);
        $loginValidator->shouldReceive('setSessionContext')
            ->once();

        // Bind the mocks to the container
        $this->app->instance(LoginTrackingService::class, $loginTracker);
        $this->app->instance(LoginValidationService::class, $loginValidator);
        
        // Login
        Livewire::test(Login::class)
            ->set('email', $user->email)
            ->set('password', 'password')
            ->call('login')
            ->assertRedirect(route('dashboard'));
        
        $this->assertAuthenticated();
    }
    
    public function test_agent_detects_mobile_device()
    {
        // Set up a mock for Agent facade to simulate mobile device
        $this->mockAgent('Safari', '16.0', 'iOS', '16.5.1', 'Mobile', true);
        
        // Create a user
        $user = User::factory()->create([
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        
        // Mock the login tracking service
        $loginTracker = Mockery::mock(LoginTrackingService::class);
        $loginTracker->shouldReceive('recordSuccessfulLogin')
            ->once()
            ->andReturn(new LoginHistory([
                'user_id' => $user->id,
                'login_successful' => true,
                'browser' => 'Safari',
                'platform' => 'iOS',
                'device_type' => 'Mobile',
            ]));

        // Mock the login validation service
        $loginValidator = Mockery::mock(LoginValidationService::class);
        $loginValidator->shouldReceive('validateLogin')
            ->once()
            ->andReturn(true);
        $loginValidator->shouldReceive('isSuperAdmin')
            ->once()
            ->andReturn(false);
        $loginValidator->shouldReceive('setSessionContext')
            ->once();

        // Bind the mocks to the container
        $this->app->instance(LoginTrackingService::class, $loginTracker);
        $this->app->instance(LoginValidationService::class, $loginValidator);
        
        // Login
        Livewire::test(Login::class)
            ->set('email', $user->email)
            ->set('password', 'password')
            ->call('login')
            ->assertRedirect(route('dashboard'));
        
        $this->assertAuthenticated();
    }
    
    public function test_agent_detects_suspicious_login()
    {
        // Set up a mock for Agent facade to simulate a headless browser
        $this->mockAgent('Chrome', '115.0.0.0', 'Linux', '5.15.0', 'Desktop', false, true);
        
        // Create a user
        $user = User::factory()->create([
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        
        // Mock the login tracking service
        $loginHistory = new LoginHistory([
            'user_id' => $user->id,
            'login_successful' => true,
            'is_headless' => true,
        ]);
        
        $loginTracker = Mockery::mock(LoginTrackingService::class);
        $loginTracker->shouldReceive('recordSuccessfulLogin')
            ->once()
            ->andReturn($loginHistory);

        // Mock the login validation service
        $loginValidator = Mockery::mock(LoginValidationService::class);
        $loginValidator->shouldReceive('validateLogin')
            ->once()
            ->andReturn(true);
        $loginValidator->shouldReceive('isSuperAdmin')
            ->once()
            ->andReturn(false);
        $loginValidator->shouldReceive('setSessionContext')
            ->once();

        // Bind the mocks to the container
        $this->app->instance(LoginTrackingService::class, $loginTracker);
        $this->app->instance(LoginValidationService::class, $loginValidator);
        
        // Login
        Livewire::test(Login::class)
            ->set('email', $user->email)
            ->set('password', 'password')
            ->call('login')
            ->assertRedirect(route('dashboard'));
        
        $this->assertAuthenticated();
        
        // Verify the login history has the headless flag
        $this->assertTrue($loginHistory->is_headless);
    }
    
    public function test_agent_detects_bot()
    {
        // Set up a mock for Agent facade to simulate a bot
        $this->mockAgent('Googlebot', '2.1', 'Unknown', '', 'Bot', false, false, true);
        
        // Create a user
        $user = User::factory()->create([
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        
        // Mock the login tracking service
        $loginTracker = Mockery::mock(LoginTrackingService::class);
        $loginTracker->shouldReceive('recordSuccessfulLogin')
            ->once()
            ->andReturn(new LoginHistory([
                'user_id' => $user->id,
                'login_successful' => true,
                'is_bot' => true,
            ]));

        // Mock the login validation service
        $loginValidator = Mockery::mock(LoginValidationService::class);
        $loginValidator->shouldReceive('validateLogin')
            ->once()
            ->andReturn(true);
        $loginValidator->shouldReceive('isSuperAdmin')
            ->once()
            ->andReturn(false);
        $loginValidator->shouldReceive('setSessionContext')
            ->once();

        // Bind the mocks to the container
        $this->app->instance(LoginTrackingService::class, $loginTracker);
        $this->app->instance(LoginValidationService::class, $loginValidator);
        
        // Login
        Livewire::test(Login::class)
            ->set('email', $user->email)
            ->set('password', 'password')
            ->call('login')
            ->assertRedirect(route('dashboard'));
        
        $this->assertAuthenticated();
    }
    
    /**
     * Helper method to mock the Agent facade for testing
     */
    private function mockAgent(
        string $browser, 
        string $browserVersion, 
        string $platform, 
        string $platformVersion, 
        string $deviceType,
        bool $isMobile = false,
        bool $isHeadless = false,
        bool $isBot = false
    ) {
        Agent::shouldReceive('browser')->andReturn($browser);
        Agent::shouldReceive('browserVersion')->andReturn($browserVersion);
        Agent::shouldReceive('platform')->andReturn($platform);
        Agent::shouldReceive('platformVersion')->andReturn($platformVersion);
        Agent::shouldReceive('deviceType')->andReturn($deviceType);
        Agent::shouldReceive('deviceBrand')->andReturn('Generic');
        Agent::shouldReceive('deviceModel')->andReturn('Generic Model');
        Agent::shouldReceive('isMobile')->andReturn($isMobile);
        Agent::shouldReceive('isBot')->andReturn($isBot);
        Agent::shouldReceive('isHeadless')->andReturn($isHeadless);
    }
} 