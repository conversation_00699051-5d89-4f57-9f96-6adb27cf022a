# Login and Session Management Tables Analysis

This document provides a comprehensive analysis of database tables related to login and session management in the system.

## Table: `users`

The core user table with authentication-specific fields:

| Field | Purpose |
|-------|---------|
| `id` | Primary key for the user record |
| `email` | User email address (unique) for login |
| `password` | Hashed password for authentication |
| `remember_token` | Token for "remember me" functionality |
| `email_verified_at` | Timestamp when email was verified |
| `is_active` | Whether the user account is active (enabled/disabled) |
| `multi_login` | Number of allowed simultaneous logins (0 = unlimited) |
| `last_login_at` | Timestamp of the last successful login |
| `activated_by` | ID of the user who activated this user |
| `activated_at` | Timestamp when the user was activated |
| `inactivated_by` | ID of the user who inactivated this user |
| `inactivated_at` | Timestamp when the user was inactivated |

## Table: `sessions`

Lara<PERSON>'s native session table:

| Field | Purpose |
|-------|---------|
| `id` | Unique session identifier (primary key) |
| `user_id` | Foreign key to users table (nullable for guest sessions) |
| `ip_address` | IP address of the user (IPv4 or IPv6) |
| `user_agent` | Browser/device user agent string |
| `payload` | Encrypted session data payload |
| `last_activity` | Unix timestamp of last session activity |

## Table: `login_histories`

Detailed record of every login attempt with extensive metadata:

| Field | Purpose |
|-------|---------|
| `id` | Primary key |
| `user_id` | Reference to users table (nullable for failed logins) |
| `role_id` | Active role for this session (RBAC) |
| `department_id` | Active department for this session (RBAC) |
| `entity_id` | Active entity identifier for this session |
| `is_cross_entity_access` | Whether this session involves cross-entity access (Super Admin) |
| `original_entity_id` | Original entity ID for cross-entity access |
| `original_role_id` | Original role ID for cross-entity access |
| `ip_address` | IPv4 or IPv6 address of login source |
| `asn` | Autonomous System Number (identifies the network) |
| `isp` | Internet Service Provider name |
| `connection_type` | Connection type: cellular, broadband, corporate, etc. |
| `is_proxy` | True if connection uses proxy/VPN/Tor |
| `user_agent` | Raw HTTP User-Agent header |
| `device_type` | Device category: mobile, tablet, desktop, etc. |
| `device_brand` | Device manufacturer: Apple, Samsung, etc. |
| `device_model` | Specific device model |
| `is_bot` | True if automated bot detected |
| `is_headless` | True if headless browser detected |
| `platform` | Operating system: Windows, macOS, Android, iOS, Linux |
| `platform_version` | OS version number |
| `browser` | Browser name: Chrome, Firefox, Safari, Edge |
| `browser_version` | Browser version number |
| `session_id` | Laravel session identifier (foreign key to sessions) |
| `session_label` | User-defined label for this session (e.g., "Work Laptop") |
| `transferred_from` | Session ID this session was transferred from |
| `transferred_to` | Session ID this session was transferred to |
| `is_transferred` | Whether this session has been transferred |
| `device_fingerprint` | Unique fingerprint for device identification |
| `fingerprint_changed` | Whether fingerprint changed during session (potential hijacking) |
| `country` | Country name from IP geolocation |
| `country_code` | ISO 3166-1 alpha-2 country code |
| `region` | State/region name |
| `city` | City name |
| `timezone` | Timezone identifier |
| `latitude` | GPS latitude coordinate |
| `longitude` | GPS longitude coordinate |
| `suspicious_location` | True if login location differs from user patterns |
| `new_device` | True if device signature not previously seen for user |
| `unusual_timing` | True if login outside user's normal activity hours |
| `threat_level` | Calculated risk: low, medium, high, critical |
| `risk_factors` | Array (JSON) of detected risk indicators |
| `risk_score` | Calculated risk score (0-100) based on login factors |
| `login_at` | Authentication timestamp |
| `login_hour` | Hour of day (0-23) for analytics |
| `login_day` | Weekday name for analytics |
| `login_origin` | URL or service that initiated login |
| `logout_at` | Session termination timestamp |
| `duration_seconds` | Active session duration in seconds |
| `logout_reason` | Termination reason: user, timeout, forced, etc. |
| `login_method` | Authentication method: password, oauth, magic_link |
| `guard` | Laravel auth guard used |
| `login_successful` | Authentication result status (true/false) |
| `failure_reason` | Description of authentication failure |
| `mfa_used` | Whether multi-factor auth was employed |
| `security_action` | System response: none, challenge, block, etc. |
| `created_at` | Record creation timestamp |
| `updated_at` | Record last update timestamp |
| `deleted_at` | Soft delete timestamp |
| `deleted_by` | User who soft deleted the record |
| `restored_at` | Timestamp when the record was restored |
| `restored_by` | User who restored the record |

## Table: `user_login_stats`

Daily aggregated metrics per user for analytics and reporting:

| Field | Purpose |
|-------|---------|
| `id` | Primary key |
| `user_id` | Reference to users table |
| `login_date` | Date for aggregated statistics |
| `total_logins` | Total login attempts |
| `successful_logins` | Successful authentications |
| `failed_logins` | Failed attempts |
| `suspicious_logins` | Logins with risk flags |
| `new_device_logins` | Logins from unrecognized devices |
| `unique_ip_count` | Distinct IP addresses used |
| `unique_device_count` | Distinct device fingerprints |
| `unique_location_count` | Distinct geographic locations |
| `avg_session_duration` | Mean session length in seconds |
| `median_session_duration` | Median session length in seconds |
| `created_at` | Record creation timestamp |
| `updated_at` | Record last update timestamp |
| `deleted_at` | Soft delete timestamp |
| `deleted_by` | User who soft deleted the record |
| `restored_at` | Timestamp when record was restored |
| `restored_by` | User who restored the record |

## Table: `banned_attempts`

Records of banned IP addresses and related information:

| Field | Purpose |
|-------|---------|
| `id` | Primary key |
| `ip_address` | IPv4 or IPv6 address (indexed) |
| `email_attempted` | Email address that was attempted (if available) |
| `user_agent` | Browser user agent of the banned request |
| `reason` | Reason for the ban |
| `notes` | Additional notes about the ban |
| `banned_at` | When the ban was created |
| `banned_until` | When the ban expires (null for permanent bans) |
| `expires_at` | Alternative field for ban expiry (for backward compatibility) |
| `ban_count` | Number of times this IP/user has been banned |
| `banned_by` | User who created the ban |
| `created_at` | Record creation timestamp |
| `updated_at` | Record last update timestamp |
| `deleted_at` | Soft delete timestamp |
| `deleted_by` | User who soft deleted the record |
| `restored_at` | Timestamp when the record was restored |
| `restored_by` | User who restored the record |

## Table: `password_reset_tokens`

Manages password reset functionality:

| Field | Purpose |
|-------|---------|
| `email` | User email (primary key) |
| `token` | The password reset token |
| `created_at` | When the reset token was created |

## Table: `cache` and `cache_locks`

Used by Laravel for caching, which can affect session handling:

### Table: `cache`
| Field | Purpose |
|-------|---------|
| `key` | Cache key (primary) |
| `value` | Cached value |
| `expiration` | Expiration time |

### Table: `cache_locks`
| Field | Purpose |
|-------|---------|
| `key` | Lock key (primary) |
| `owner` | Lock owner |
| `expiration` | Lock expiration time |

## Table: `session_module_export`

Appears to be a placeholder table for exporting session data:

| Field | Purpose |
|-------|---------|
| `id` | Primary key |
| `created_at` | Record creation timestamp |
| `updated_at` | Record last update timestamp | 