@php
    $heading = $heading ?? __('Role Management');
@endphp
<div class="space-y-6">
    <!-- Header Actions -->
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
            <!-- Search -->
            <flux:input 
                wire:model.live.debounce.300ms="search"
                placeholder="{{ __('Search roles...') }}"
                class="w-64">
                <x-slot name="iconTrailing">
                    <flux:icon name="magnifying-glass" class="h-4 w-4" />
                </x-slot>
            </flux:input>

            <!-- Filters -->
            <flux:select wire:model.live="filterHierarchy" placeholder="{{ __('All Levels') }}">
                <flux:option value="1">{{ __('Level 1 - Super Admin') }}</flux:option>
                <flux:option value="2">{{ __('Level 2 - Admin Manager') }}</flux:option>
                <flux:option value="3">{{ __('Level 3 - Administrator') }}</flux:option>
            </flux:select>

            <flux:select wire:model.live="filterStatus" placeholder="{{ __('All Status') }}">
                <flux:option value="active">{{ __('Active') }}</flux:option>
                <flux:option value="inactive">{{ __('Inactive') }}</flux:option>
            </flux:select>
        </div>

        <!-- Create Button -->
        <flux:button 
            variant="primary" 
            href="{{ route('admin.rbac.roles.create') }}"
            wire:navigate>
            <flux:icon name="plus" class="h-4 w-4" />
            {{ __('Create Role') }}
        </flux:button>
    </div>

    <!-- Roles Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-700">
        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
            <thead class="bg-zinc-50 dark:bg-zinc-800">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Role') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Hierarchy') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Users') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Status') }}
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Actions') }}
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-900 divide-y divide-zinc-200 dark:divide-zinc-700">
                @forelse($roles as $role)
                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-800">
                    <td class="px-6 py-4">
                        <div>
                            <flux:text class="font-medium">{{ $role->role_name }}</flux:text>
                            @if($role->description)
                            <flux:text class="text-sm text-zinc-500">{{ $role->description }}</flux:text>
                            @endif
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <x-admin.hierarchy-indicator :level="$role->hierarchy_level" />
                    </td>
                    <td class="px-6 py-4">
                        <flux:badge variant="outline">
                            {{ $role->userAssignments->count() }} {{ __('users') }}
                        </flux:badge>
                    </td>
                    <td class="px-6 py-4">
                        @if($role->isOperational())
                            <flux:badge variant="solid" color="success">{{ __('Active') }}</flux:badge>
                        @else
                            <flux:badge variant="solid" color="danger">{{ __('Inactive') }}</flux:badge>
                        @endif
                    </td>
                    <td class="px-6 py-4 text-right">
                        <div class="flex items-center justify-end gap-2">
                            <flux:button
                                variant="ghost"
                                size="sm"
                                href="{{ route('admin.rbac.roles.edit', $role) }}"
                                wire:navigate>
                                {{ __('Edit') }}
                            </flux:button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="5" class="px-6 py-12 text-center">
                        <flux:text class="text-zinc-500">{{ __('No roles found.') }}</flux:text>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $roles->links() }}
    </div>
</div>
