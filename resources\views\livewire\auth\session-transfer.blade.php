<div class="flex flex-col gap-6">
    <x-auth-header :title="__('Transfer Your Session')" :description="__('Continue your session on this device')" />

    @if($error)
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ $error }}</span>
        </div>
    @endif

    <form wire:submit="validateTransferCode" class="flex flex-col gap-6" x-data="{ autoSubmit: {{ !empty($transferCode) ? 'true' : 'false' }} }" x-init="if(autoSubmit) $el.requestSubmit()">
        <!-- Transfer Code -->
        <flux:input
            wire:model="transferCode"
            :label="__('Transfer Code')"
            type="text"
            required
            autofocus
            placeholder="Enter 8-character code" />

        <div class="flex items-center justify-end">
            <flux:button variant="primary" type="submit" class="w-full">{{ __('Transfer Session') }}</flux:button>
        </div>
    </form>
    
    <div class="space-x-1 rtl:space-x-reverse text-center text-sm text-zinc-600 dark:text-zinc-400">
        <span>{{ __('Don\'t have a transfer code?') }}</span>
        <flux:link :href="route('login')" wire:navigate>{{ __('Log in') }}</flux:link>
    </div>
</div> 