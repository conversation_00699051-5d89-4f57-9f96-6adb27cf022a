{{-- resources/views/livewire/settings/password.blade.php - Enhanced Version --}}
<section class="w-full">
    @include('partials.settings-heading')

    <x-settings.layout :heading="__('Update password')" :subheading="__('Ensure your account is using a long, random password to stay secure')">
        <form wire:submit="updatePassword" class="mt-6 space-y-6 relative">
            <!-- Loading overlay for this form -->
            <div wire:loading.flex wire:target="updatePassword"
                class="absolute inset-0 z-10 bg-white/80 dark:bg-zinc-900/80 backdrop-blur-sm rounded-lg">
                <div class="flex h-full items-center justify-center">
                    <x-loading-spinner type="spinner" size="md" text="{{ __('Updating password...') }}" />
                </div>
            </div>

            <flux:input
                wire:model="current_password"
                :label="__('Current password')"
                type="password"
                required
                autocomplete="current-password"
                wire:loading.attr="disabled"
                wire:target="updatePassword" />

            <flux:input
                wire:model="password"
                :label="__('New password')"
                type="password"
                required
                autocomplete="new-password"
                wire:loading.attr="disabled"
                wire:target="updatePassword" />

            <flux:input
                wire:model="password_confirmation"
                :label="__('Confirm Password')"
                type="password"
                required
                autocomplete="new-password"
                wire:loading.attr="disabled"
                wire:target="updatePassword" />

            <!-- Password Strength Indicator -->
            <div class="space-y-2">
                <label class="text-sm font-medium text-zinc-700 dark:text-zinc-300">
                    {{ __('Password Requirements') }}
                </label>
                <div class="space-y-1 text-xs">
                    <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                        <div class="h-1.5 w-1.5 rounded-full bg-zinc-300 dark:bg-zinc-600"></div>
                        {{ __('At least 8 characters') }}
                    </div>
                    <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                        <div class="h-1.5 w-1.5 rounded-full bg-zinc-300 dark:bg-zinc-600"></div>
                        {{ __('Contains uppercase and lowercase letters') }}
                    </div>
                    <div class="flex items-center gap-2 text-zinc-600 dark:text-zinc-400">
                        <div class="h-1.5 w-1.5 rounded-full bg-zinc-300 dark:bg-zinc-600"></div>
                        {{ __('Contains at least one number') }}
                    </div>
                </div>
            </div>

            <div class="flex items-center gap-4">
                <div class="flex items-center justify-end">
                    <flux:button
                        variant="primary"
                        type="submit"
                        class="w-full"
                        wire:loading.attr="disabled"
                        wire:target="updatePassword">
                        <span wire:loading.remove wire:target="updatePassword">
                            {{ __('Save') }}
                        </span>
                        <span wire:loading wire:target="updatePassword" class="flex items-center gap-2">
                            <x-loading-spinner type="inline" />
                            {{ __('Updating...') }}
                        </span>
                    </flux:button>
                </div>

                <x-action-message class="me-3" on="password-updated">
                    <div class="flex items-center gap-2 text-green-600 dark:text-green-400">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {{ __('Saved.') }}
                    </div>
                </x-action-message>
            </div>
        </form>
    </x-settings.layout>
</section>