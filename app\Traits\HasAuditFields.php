<?php
// app\Traits\HasAuditFields.php
namespace App\Traits;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * Trait HasAuditFields
 *
 * Provides consistent audit field relationships across all models.
 * This trait should be used by any model that has audit fields.
 *
 * Required database columns:
 * - created_by (int, nullable)
 * - updated_by (int, nullable)
 * - deleted_by (int, nullable)
 * - restored_by (int, nullable)
 * - restored_at (timestamp, nullable)
 *
 * @package App\Traits
 */
trait HasAuditFields
{
    /**
     * Boot the trait and set up model events.
     */
    public static function bootHasAuditFields()
    {
        // Automatically set created_by when creating
        static::creating(function ($model) {
            if (!$model->created_by && auth()->check()) {
                $model->created_by = auth()->id();
            }
        });

        // Automatically set updated_by when updating
        static::updating(function ($model) {
            if (auth()->check()) {
                $model->updated_by = auth()->id();
            }
        });

        // Set deleted_by when soft deleting
        static::deleting(function ($model) {
            if (auth()->check() && $model->usesSoftDeletes()) {
                $model->deleted_by = auth()->id();
                $model->saveQuietly(); // Save without triggering events
            }
        });

        // Set restored_by and restored_at when restoring
        if (method_exists(static::class, 'restored')) {
            static::restored(function ($model) {
                if (auth()->check()) {
                    $model->restored_by = auth()->id();
                    $model->restored_at = now();
                    $model->saveQuietly();
                }
            });
        }
    }

    /**
     * Get the user who created this record.
     *
     * @return BelongsTo
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this record.
     *
     * @return BelongsTo
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who soft deleted this record.
     *
     * @return BelongsTo
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the user who restored this record.
     *
     * @return BelongsTo
     */
    public function restoredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'restored_by');
    }

    /**
     * Get the creator's name.
     *
     * @return string|null
     */
    public function getCreatedByNameAttribute(): ?string
    {
        return $this->createdBy?->name;
    }

    /**
     * Get the updater's name.
     *
     * @return string|null
     */
    public function getUpdatedByNameAttribute(): ?string
    {
        return $this->updatedBy?->name;
    }

    /**
     * Get the deleter's name.
     *
     * @return string|null
     */
    public function getDeletedByNameAttribute(): ?string
    {
        return $this->deletedBy?->name;
    }

    /**
     * Get the restorer's name.
     *
     * @return string|null
     */
    public function getRestoredByNameAttribute(): ?string
    {
        return $this->restoredBy?->name;
    }

    /**
     * Check if the model uses soft deletes.
     *
     * @return bool
     */
    protected function usesSoftDeletes(): bool
    {
        return in_array('Illuminate\Database\Eloquent\SoftDeletes', class_uses_recursive($this));
    }

    /**
     * Get audit trail for this record.
     * Returns a collection of audit events.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAuditTrail()
    {
        $trail = collect();

        // Created event
        if ($this->created_at && $this->created_by) {
            $trail->push([
                'action' => 'created',
                'user_id' => $this->created_by,
                'user_name' => $this->created_by_name,
                'timestamp' => $this->created_at,
            ]);
        }

        // Updated event
        if ($this->updated_at && $this->updated_by && $this->updated_at != $this->created_at) {
            $trail->push([
                'action' => 'updated',
                'user_id' => $this->updated_by,
                'user_name' => $this->updated_by_name,
                'timestamp' => $this->updated_at,
            ]);
        }

        // Deleted event
        if ($this->deleted_at && $this->deleted_by) {
            $trail->push([
                'action' => 'deleted',
                'user_id' => $this->deleted_by,
                'user_name' => $this->deleted_by_name,
                'timestamp' => $this->deleted_at,
            ]);
        }

        // Restored event
        if ($this->restored_at && $this->restored_by) {
            $trail->push([
                'action' => 'restored',
                'user_id' => $this->restored_by,
                'user_name' => $this->restored_by_name,
                'timestamp' => $this->restored_at,
            ]);
        }

        return $trail->sortBy('timestamp');
    }

    /**
     * Scope to records created by a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCreatedBy($query, int $userId)
    {
        return $query->where('created_by', $userId);
    }

    /**
     * Scope to records updated by a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUpdatedBy($query, int $userId)
    {
        return $query->where('updated_by', $userId);
    }

    /**
     * Scope to records deleted by a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDeletedBy($query, int $userId)
    {
        return $query->where('deleted_by', $userId);
    }

    /**
     * Scope to records restored by a specific user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRestoredBy($query, int $userId)
    {
        return $query->where('restored_by', $userId);
    }

    /**
     * Scope to records that have been restored.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWasRestored($query)
    {
        return $query->whereNotNull('restored_at');
    }
}

/**
 * USAGE EXAMPLE:
 *
 * class Entity extends Model
 * {
 *     use SoftDeletes, HasAuditFields;
 *
 *     // The trait will automatically handle:
 *     // - Setting created_by on create
 *     // - Setting updated_by on update
 *     // - Setting deleted_by on soft delete
 *     // - Setting restored_by and restored_at on restore
 *     // - Providing all audit relationships
 *     // - Providing audit trail methods
 * }
 */
