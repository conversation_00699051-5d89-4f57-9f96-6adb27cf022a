<?php
// app/Models/CodeSequence.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use App\Traits\HasAuditFields;

/**
 * Class CodeSequence
 *
 * Manages sequential code generation for various entity types.
 * Ensures unique, sequential codes are generated even under concurrent access.
 *
 * @property int $id
 * @property string $key Unique key for the sequence (e.g., "DIST_2025", "DLR_2025")
 * @property int $sequence Last used sequence number
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at Soft delete timestamp
 * @property int|null $deleted_by User who soft deleted the record
 * @property \Carbon\Carbon|null $restored_at Timestamp when the record was restored
 * @property int|null $restored_by User who restored the record
 */
class CodeSequence extends Model
{
    use SoftDeletes, HasAuditFields;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'key',
        'sequence',
        'deleted_by',
        'restored_at',
        'restored_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sequence' => 'integer',
        'deleted_at' => 'datetime',
        'restored_at' => 'datetime',
    ];

    /**
     * Get the next sequence number for a given key.
     * This method is thread-safe using database locks.
     *
     * @param string $key The sequence key
     * @return int The next sequence number
     */
    public static function getNextSequence(string $key): int
    {
        return DB::transaction(function () use ($key) {
            // Lock the row for update to prevent concurrent access
            $sequence = self::where('key', $key)->lockForUpdate()->first();

            if (!$sequence) {
                // Create new sequence starting at 1
                $sequence = self::create([
                    'key' => $key,
                    'sequence' => 1,
                ]);
                return 1;
            }

            // Increment and save
            $sequence->increment('sequence');
            return $sequence->sequence;
        });
    }

    /**
     * Get the current sequence number without incrementing.
     *
     * @param string $key The sequence key
     * @return int The current sequence number (0 if not exists)
     */
    public static function getCurrentSequence(string $key): int
    {
        $sequence = self::where('key', $key)->first();
        return $sequence ? $sequence->sequence : 0;
    }

    /**
     * Reset a sequence to a specific value.
     * WARNING: This can cause duplicate codes if not used carefully.
     *
     * @param string $key The sequence key
     * @param int $value The new sequence value
     * @return bool
     */
    public static function resetSequence(string $key, int $value = 0): bool
    {
        $sequence = self::where('key', $key)->first();

        if ($sequence) {
            $sequence->sequence = $value;
            return $sequence->save();
        }

        // Create new sequence with specified value
        self::create([
            'key' => $key,
            'sequence' => $value,
        ]);

        return true;
    }

    /**
     * Bulk get next sequences for multiple keys.
     * Useful for generating multiple codes in one transaction.
     *
     * @param array $keys Array of sequence keys
     * @return array Associative array of key => next_sequence
     */
    public static function getNextSequences(array $keys): array
    {
        return DB::transaction(function () use ($keys) {
            $results = [];

            foreach ($keys as $key) {
                $results[$key] = self::getNextSequence($key);
            }

            return $results;
        });
    }

    /**
     * Get all sequences matching a pattern.
     *
     * @param string $pattern SQL LIKE pattern (e.g., "DIST_%")
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSequencesByPattern(string $pattern)
    {
        return self::where('key', 'like', $pattern)
            ->orderBy('key')
            ->get();
    }

    /**
     * Generate a formatted code using the sequence.
     *
     * @param string $prefix The code prefix (e.g., "DIST", "DLR")
     * @param string $year The year component
     * @param int $padding Number of digits to pad the sequence
     * @return string Formatted code (e.g., "DIST-2025-0001")
     */
    public static function generateCode(string $prefix, string $year, int $padding = 4): string
    {
        $key = "{$prefix}_{$year}";
        $sequence = self::getNextSequence($key);

        return sprintf(
            '%s-%s-%0' . $padding . 'd',
            $prefix,
            $year,
            $sequence
        );
    }

    /**
     * Get sequence statistics for reporting.
     *
     * @param string|null $prefix Filter by prefix (optional)
     * @return array
     */
    public static function getStatistics(?string $prefix = null): array
    {
        $query = self::query();

        if ($prefix) {
            $query->where('key', 'like', $prefix . '%');
        }

        $sequences = $query->get();

        return [
            'total_sequences' => $sequences->count(),
            'total_codes_generated' => $sequences->sum('sequence'),
            'sequences_by_type' => $sequences->groupBy(function ($item) {
                return explode('_', $item->key)[0];
            })->map->count(),
            'highest_sequence' => $sequences->max('sequence'),
            'most_used_type' => $sequences->sortByDesc('sequence')->first()?->key,
        ];
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to sequences for a specific year.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $year
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForYear($query, string $year)
    {
        return $query->where('key', 'like', '%_' . $year);
    }

    /**
     * Scope to sequences for a specific prefix.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $prefix
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForPrefix($query, string $prefix)
    {
        return $query->where('key', 'like', $prefix . '_%');
    }

    /**
     * Scope to active sequences (with generated codes).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('sequence', '>', 0);
    }

    // ===== INSTANCE METHODS =====

    /**
     * Get the prefix from the sequence key.
     *
     * @return string
     */
    public function getPrefixAttribute(): string
    {
        return explode('_', $this->key)[0] ?? '';
    }

    /**
     * Get the year from the sequence key.
     *
     * @return string|null
     */
    public function getYearAttribute(): ?string
    {
        $parts = explode('_', $this->key);
        return $parts[1] ?? null;
    }

    /**
     * Check if this sequence is for the current year.
     *
     * @return bool
     */
    public function isCurrentYear(): bool
    {
        return $this->year === date('Y');
    }

    /**
     * Get the next code that would be generated.
     *
     * @param int $padding Number of digits to pad
     * @return string
     */
    public function getNextCode(int $padding = 4): string
    {
        $nextSequence = $this->sequence + 1;

        return sprintf(
            '%s-%s-%0' . $padding . 'd',
            $this->prefix,
            $this->year,
            $nextSequence
        );
    }
}
