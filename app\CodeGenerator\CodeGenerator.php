<?php
// app/CodeGenerator/CodeGenerator.php

namespace App\CodeGenerator;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class CodeGenerator
{
    protected $config;

    public function __construct()
    {
        $this->config = config('codegenerator');
    }

    public function generate(string $type, array $overrides = []): string
    {
        $config = $this->getMergedConfig($type, $overrides);
        $sequence = $this->getNextSequence($type, $config);
        return $this->buildCode($config, $sequence);
    }

    protected function getMergedConfig(string $type, array $overrides): array
    {
        $typeConfig = $this->config['types'][$type] ?? [];
        return array_merge($this->config['defaults'], $typeConfig, $overrides);
    }

    protected function getNextSequence(string $type, array $config): string
    {
        $key = $this->getSequenceKey($type, $config);

        if ($config['use_buffer'] ?? false) {
            return $this->getBufferedSequence($key, $config);
        }

        return $this->getDatabaseSequence($key, $config);
    }

    protected function getBufferedSequence(string $key, array $config): string
    {
        $cacheKey = "codegen:buffer:$key";

        $buffer = Cache::remember($cacheKey, $this->config['service']['cache']['ttl'], function () use ($key, $config) {
            $sequence = $this->getDatabaseSequence($key, $config, $this->config['service']['default_buffer_size']);
            return [
                'current' => $sequence,
                'remaining' => $this->config['service']['default_buffer_size']
            ];
        });

        if ($buffer['remaining'] <= 0) {
            $sequence = $this->getDatabaseSequence($key, $config, $this->config['service']['default_buffer_size']);
            $buffer = [
                'current' => $sequence,
                'remaining' => $this->config['service']['default_buffer_size']
            ];
        }

        $sequence = $buffer['current'] - $buffer['remaining'] + 1;
        $buffer['remaining']--;
        Cache::put($cacheKey, $buffer, $this->config['service']['cache']['ttl']);

        return str_pad($sequence, $config['sequence_length'], '0', STR_PAD_LEFT);
    }

    protected function getDatabaseSequence(string $key, array $config, int $increment = 1): string
    {
        return DB::transaction(function () use ($key, $config, $increment) {
            $sequence = DB::table('code_sequences')
                ->where('key', $key)
                ->lockForUpdate()
                ->value('sequence') ?? 0;

            DB::table('code_sequences')
                ->updateOrInsert(
                    ['key' => $key],
                    ['sequence' => $sequence + $increment]
                );

            return str_pad($sequence + 1, $config['sequence_length'], '0', STR_PAD_LEFT);
        });
    }

    protected function buildCode(array $config, string $sequence): string
    {
        $replacements = [
            '{prefix}' => $config['prefix'],
            '{location}' => $config['location'],
            '{date_format}' => now()->format($config['date_format'] ?? ''),
            '{time_format}' => now()->format($config['time_format'] ?? ''),
            '{sequence}' => $sequence,
        ];

        return str_replace(
            array_keys($replacements),
            array_values($replacements),
            $config['pattern']
        );
    }

    protected function getSequenceKey(string $type, array $config): string
    {
        $parts = [$type];

        if (!empty($config['location'])) {
            $parts[] = $config['location'];
        }

        if (!empty($config['reset_period'])) {
            $parts[] = now()->format($this->config['reset_periods'][$config['reset_period']]['format']);
        }

        return implode('_', $parts);
    }
}
