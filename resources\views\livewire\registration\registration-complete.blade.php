<div class="min-h-screen bg-gray-50 dark:bg-zinc-900 py-8">
    <div class="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Logo -->
        <div class="text-center mb-6">
            <a href="/" class="inline-flex justify-center">
                <span class="flex h-12 w-12 mb-1 items-center justify-center">
                    <x-app-logo-icon class="h-full w-full fill-current text-black dark:text-white" />
                </span>
            </a>
        </div>
        
        <flux:card>
            <div class="text-center">
                <div class="flex justify-center mb-6">
                    <div class="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/30">
                        <flux:icon.check-circle class="h-10 w-10 text-green-600 dark:text-green-500" />
                    </div>
                </div>
                
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Registration Complete!</h2>
                <p class="text-gray-600 dark:text-gray-400 mb-6">Thank you for registering with our platform</p>
                
                <flux:alert variant="info" class="mb-6">
                    <p class="font-medium">Your registration is now pending approval.</p>
                    <p class="text-sm mt-1">You will receive an email notification once your account has been reviewed and approved.</p>
                </flux:alert>
                
                <div class="bg-gray-50 dark:bg-zinc-800/50 rounded-lg p-4 border border-gray-100 dark:border-zinc-700 mb-6">
                    <h3 class="font-medium text-gray-900 dark:text-white mb-3">What happens next?</h3>
                    <ul class="space-y-3 text-left">
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mr-3">
                                <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                                    <span class="text-xs font-medium text-blue-800 dark:text-blue-500">1</span>
                                </div>
                            </div>
                            <span class="text-gray-600 dark:text-gray-400">Our team will review your registration details</span>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mr-3">
                                <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                                    <span class="text-xs font-medium text-blue-800 dark:text-blue-500">2</span>
                                </div>
                            </div>
                            <span class="text-gray-600 dark:text-gray-400">You'll receive an email notification with the approval status</span>
                        </li>
                        <li class="flex items-start">
                            <div class="flex-shrink-0 mr-3">
                                <div class="flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30">
                                    <span class="text-xs font-medium text-blue-800 dark:text-blue-500">3</span>
                                </div>
                            </div>
                            <span class="text-gray-600 dark:text-gray-400">Once approved, you can log in using your registered email and password</span>
                        </li>
                    </ul>
                </div>
                
                <a href="/login" class="inline-flex items-center justify-center px-5 py-2 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <flux:icon.arrow-left class="h-5 w-5 mr-2" />
                    Go to Login
                </a>
            </div>
        </flux:card>
        
        <!-- Help Section -->
        <div class="text-center text-sm text-gray-500 dark:text-gray-400 mt-6">
            <p>Need assistance? <a href="#" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">Contact support</a></p>
        </div>
    </div>
</div>