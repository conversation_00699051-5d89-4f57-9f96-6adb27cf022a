<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // ---------------------- Contacts Table ----------------------
        Schema::create('contacts', function (Blueprint $table) {
            $table->id()->comment('Primary key: Unique contact ID');
            $table->string('name')->comment('Full name of the contact person');
            $table->string('title', 20)->nullable()->comment('Formal title (Mr., Ms., Dr., etc.)');
            $table->string('position', 100)->nullable()->comment('Job title or role');
            $table->string('email')->nullable()->comment('Email address of the contact');
            $table->string('phone')->nullable()->comment('Phone number of the contact');

            // Audit fields
            $table->boolean('is_active')->default(true)->comment('Status: true=active, false=inactive');
            $table->unsignedBigInteger('created_by')->comment('User ID who created this record');
            $table->timestamp('created_at')->useCurrent()->comment('Timestamp when the record was created');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last updated this record');
            $table->timestamp('updated_at')->nullable()->comment('Timestamp when the record was last updated');
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored the record');
        });

        // ---------------------- Addresses Table ----------------------
        Schema::create('addresses', function (Blueprint $table) {
            $table->id()->comment('Primary key: Unique address ID');
            $table->string('address_line1')->comment('Street address or main address line');
            $table->string('address_line2')->nullable()->comment('Additional address info (e.g., apartment, suite)');
            $table->string('city')->comment('City name');
            $table->string('state')->comment('State or province');
            $table->string('postal_code')->comment('ZIP or postal code');
            $table->boolean('is_primary')->default(false)->comment('Whether this is the primary address');
            $table->decimal('latitude', 10, 7)->nullable()->comment('GPS latitude of the address');
            $table->decimal('longitude', 10, 7)->nullable()->comment('GPS longitude of the address');

            // Audit fields
            $table->boolean('is_active')->default(true)->comment('Status: true=active, false=inactive');
            $table->unsignedBigInteger('created_by')->comment('User ID who created this record');
            $table->timestamp('created_at')->useCurrent()->comment('Timestamp when the record was created');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last updated this record');
            $table->timestamp('updated_at')->nullable()->comment('Timestamp when the record was last updated');
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored the record');
        });

        // ---------------------- KYCs Table ----------------------
        Schema::create('kycs', function (Blueprint $table) {
            $table->id()->comment('Primary key: Unique KYC record ID');
            $table->string('document_type')->comment('Type of document (e.g., Passport, Driver License)');
            $table->string('document_number')->comment('Unique number of the document');
            $table->string('document_file')->nullable()->comment('Path to uploaded document copy');
            $table->date('expiry_date')->nullable()->comment('Date when the document expires');
            $table->enum('verification_status', ['pending', 'verified', 'rejected', 'expired'])->default('pending')->comment('Status of document verification');

            // Audit fields
            $table->boolean('is_active')->default(true)->comment('Status: true=active, false=inactive');
            $table->unsignedBigInteger('created_by')->comment('User ID who created this record');
            $table->timestamp('created_at')->useCurrent()->comment('Timestamp when the record was created');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last updated this record');
            $table->timestamp('updated_at')->nullable()->comment('Timestamp when the record was last updated');
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored the record');
        });

        // ---------------------- Taxes Table ----------------------
        Schema::create('taxes', function (Blueprint $table) {
            $table->id()->comment('Primary key: Unique tax record ID');
            $table->string('tax_identifier')->comment('Unique identifier for the tax (e.g., GSTIN, VAT ID)');
            $table->string('tax_type')->comment('Type of tax (e.g., GST, VAT, Income Tax)');
            $table->string('tax_region')->nullable()->comment('Region or jurisdiction where the tax applies');
            $table->date('effective_date')->nullable()->comment('Date when the tax registration becomes effective');
            $table->date('expiry_date')->nullable()->comment('Date when the tax registration expires');

            // Audit fields
            $table->boolean('is_active')->default(true)->comment('Status: true=active, false=inactive');
            $table->unsignedBigInteger('created_by')->comment('User ID who created this record');
            $table->timestamp('created_at')->useCurrent()->comment('Timestamp when the record was created');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last updated this record');
            $table->timestamp('updated_at')->nullable()->comment('Timestamp when the record was last updated');
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored the record');
        });

        // ---------------------- Notifications Table ----------------------
        Schema::create('notifications', function (Blueprint $table) {
            $table->id()->comment('Primary key: Unique notification preference ID');
            $table->boolean('via_email')->default(true)->comment('Receive notifications via email');
            $table->boolean('via_sms')->default(false)->comment('Receive notifications via SMS');
            $table->boolean('via_whatsapp')->default(false)->comment('Receive notifications via WhatsApp');
            $table->string('whatsapp_number', 20)->nullable()->comment('Phone number used for WhatsApp notifications');
            $table->boolean('via_mobile_app')->default(false)->comment('Receive notifications via the mobile application');
            $table->string('mobile_app_id', 50)->nullable()->comment('Device or user ID for mobile app notifications');
            $table->boolean('via_push')->default(false)->comment('Receive browser or app push notifications');
            $table->string('push_token', 255)->nullable()->comment('Push notification token');

            // Audit fields
            $table->boolean('is_active')->default(true)->comment('Status: true=active, false=inactive');
            $table->unsignedBigInteger('created_by')->comment('User ID who created this record');
            $table->timestamp('created_at')->useCurrent()->comment('Timestamp when the record was created');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('User ID who last updated this record');
            $table->timestamp('updated_at')->nullable()->comment('Timestamp when the record was last updated');
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User ID who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User ID who restored the record');
        });

        /**
         * Table: registration_attempts
         * Purpose: Log every registration/login attempt with metadata for monitoring, auditing, and brute-force protection.
         */
        Schema::create('registration_attempts', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('user_id')->nullable()->comment('User ID being used to attempt login (if known)');
            $table->string('entity_code')->nullable()->comment('Entity code used in the attempt (if applicable)');
            $table->string('email')->nullable()->comment('Email used in the registration/login attempt');
            $table->string('ip_address')->comment('IP address of the client making the attempt');
            $table->string('user_agent')->nullable()->comment('User agent string (browser/device details)');
            $table->string('source', 50)->nullable()->comment('Source of registration (web, mobile, API)');
            $table->boolean('success')->default(false)->comment('Flag to indicate if the attempt was successful');
            $table->timestamp('attempted_at')->useCurrent()->comment('Timestamp of the login/registration attempt');

            // Optional foreign key constraint (uncomment if you want DB integrity)
            // $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

            // Indexes for faster lookups
            $table->index(['user_id', 'ip_address'], 'idx_user_ip');
            $table->index(['entity_code', 'ip_address'], 'idx_entity_ip');
            $table->index(['email', 'ip_address'], 'idx_email_ip');
            $table->index(['ip_address', 'attempted_at'], 'idx_ip_time');
        });

        /**
         * Table: banned_attempts
         * Purpose: Log IP addresses that are temporarily or permanently banned due to abuse.
         */
        Schema::create('banned_attempts', function (Blueprint $table) {
            // Primary Key
            $table->id()->comment('Unique ban record identifier');

            // IP Information
            $table->string('ip_address', 45)->comment('IP address that is banned');
            $table->text('user_agent')->nullable()->comment('User agent/Browser info during ban');
            $table->string('email_attempted')->nullable()->comment('Email used in failed attempt');

            // Ban Details
            $table->text('reason')->nullable()->comment('Reason for banning this IP');
            $table->timestamp('banned_at')->comment('When the ban was initially imposed');
            $table->timestamp('expires_at')->nullable()->comment('When the ban will auto-expire');
            $table->timestamp('banned_until')->comment('DEPRECATED: Replaced by expires_at - will be removed in v3.0');
            $table->text('notes')->nullable()->comment('Additional admin notes about the ban');
            $table->unsignedInteger('ban_count')->default(1)->comment('Number of times this IP has been banned');

            // Admin Tracking
            $table->unsignedBigInteger('banned_by')->nullable()->comment('Admin user ID who imposed ban');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('Admin user ID who lifted ban');

            // Timestamps
            $table->timestamps();

            // Soft delete functionality (restored_by already exists)
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            // restored_by already exists above

            // Indexes
            $table->index('ip_address', 'idx_banned_ip');
            $table->index('banned_at', 'idx_ban_date');
            $table->index('expires_at', 'idx_ban_expiry');
            $table->index('banned_until', 'idx_legacy_ban_expiry'); // Legacy support
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('taxes');
        Schema::dropIfExists('kycs');
        Schema::dropIfExists('addresses');
        Schema::dropIfExists('contacts');
        Schema::dropIfExists('banned_attempts');
        Schema::dropIfExists('registration_attempts');
    }
};
