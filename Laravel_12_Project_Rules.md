# Laravel 12 Project Rules and Architecture Guidelines

**Project:** SAIMS (Supplier, Agent, Inventory Management System)  
**Framework:** Laravel 12.x  
**Analysis Date:** 2025-07-24  
**Created By:** Augment Agent

---

## Executive Summary

This document establishes comprehensive rules and guidelines for the SAIMS project based on Laravel 12's architecture. Laravel 12 introduces significant structural changes from previous versions, most notably the removal of the Kernel.php file and streamlined application bootstrapping.

---

## 1. Laravel 12 Major Architectural Changes

### 1.1 Removed Components (vs Laravel 10/11)
- ❌ **No `app/Http/Kernel.php`** - Middleware configuration moved to `bootstrap/app.php`
- ❌ **No `app/Console/Kernel.php`** - Console configuration moved to `bootstrap/app.php`
- ❌ **No `config/app.php` providers array** - Service providers moved to `bootstrap/providers.php`
- ❌ **No RouteServiceProvider** - Routing configuration moved to `bootstrap/app.php`

### 1.2 New/Modified Components
- ✅ **`bootstrap/app.php`** - Central application configuration hub
- ✅ **`bootstrap/providers.php`** - Service provider registration
- ✅ **Streamlined middleware registration** via `withMiddleware()` closure
- ✅ **Simplified routing configuration** via `withRouting()` closure
- ✅ **Exception handling** via `withExceptions()` closure

---

## 2. Application Bootstrap Structure

### 2.1 Bootstrap/app.php Pattern
```php
<?php
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/health',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Middleware configuration
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Exception handling
    })
    ->create();
```

### 2.2 Service Provider Registration
**File:** `bootstrap/providers.php`
```php
<?php
return [
    App\Providers\AppServiceProvider::class,
    App\CodeGenerator\CodeGeneratorServiceProvider::class,
    App\Agent\AgentServiceProvider::class,
];
```

---

## 3. Middleware Configuration Rules

### 3.1 Global Middleware Registration
```php
->withMiddleware(function (Middleware $middleware) {
    // Add to end of global middleware stack
    $middleware->append(CustomMiddleware::class);
    
    // Add to beginning of global middleware stack
    $middleware->prepend(CustomMiddleware::class);
    
    // Register middleware aliases
    $middleware->alias([
        'custom' => CustomMiddleware::class,
    ]);
})
```

### 3.2 Middleware Groups
```php
->withMiddleware(function (Middleware $middleware) {
    // Append to web middleware group
    $middleware->web(append: [CustomMiddleware::class]);
    
    // Prepend to api middleware group
    $middleware->api(prepend: [CustomMiddleware::class]);
    
    // Create custom middleware group
    $middleware->appendToGroup('custom-group', [
        FirstMiddleware::class,
        SecondMiddleware::class,
    ]);
})
```

### 3.3 Current SAIMS Middleware Configuration
```php
->withMiddleware(function (Middleware $middleware) {
    // Global middleware
    $middleware->web(LoginRateLimiter::class);
    $middleware->web(UpdateLastActivity::class);
    $middleware->web(EnforceMagicLinkValidity::class);
    
    // Middleware aliases
    $middleware->alias([
        'active' => EnsureUserIsActive::class,
        'check.sessions' => CheckActiveSessions::class,
        'check.roles' => CheckUserHasAnyRole::class,
        'update.activity' => UpdateLastActivity::class,
    ]);
})
```

---

## 4. Routing Configuration Rules

### 4.1 Route File Registration
```php
->withRouting(
    web: __DIR__.'/../routes/web.php',
    api: __DIR__.'/../routes/api.php',        // Optional
    commands: __DIR__.'/../routes/console.php',
    channels: __DIR__.'/../routes/channels.php', // Optional
    health: '/health',
)
```

### 4.2 Current SAIMS Routing Structure
- **`routes/web.php`** - Main web routes
- **`routes/auth.php`** - Authentication routes (included via require)
- **`routes/entity-registration.php`** - Registration routes (included via require)
- **`routes/console.php`** - Console commands

---

## 5. Service Provider Rules

### 5.1 Service Provider Registration
- **Register in:** `bootstrap/providers.php`
- **Not in:** `config/app.php` (Laravel 12 doesn't use this)

### 5.2 Current SAIMS Service Providers
```php
return [
    App\Providers\AppServiceProvider::class,
    App\CodeGenerator\CodeGeneratorServiceProvider::class,
    App\Agent\AgentServiceProvider::class,
];
```

### 5.3 Service Provider Structure
```php
class AppServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // Register services in container
        $this->app->singleton(ServiceClass::class);
    }

    public function boot(): void
    {
        // Bootstrap application services
        View::addNamespace('custom', resource_path('views/custom'));
    }
}
```

---

## 6. Directory Structure Rules

### 6.1 Required Directories
```
app/
├── Http/
│   ├── Controllers/
│   └── Middleware/          # Custom middleware
├── Models/                  # Eloquent models
├── Providers/              # Service providers
├── Services/               # Business logic services
├── Livewire/              # Livewire components
└── [Other custom directories]

bootstrap/
├── app.php                 # Application bootstrap
├── providers.php           # Service provider registration
└── cache/                  # Framework cache

config/                     # Configuration files
routes/                     # Route definitions
resources/                  # Views, assets
database/                   # Migrations, seeders
```

### 6.2 Removed Directories (vs older Laravel)
- ❌ No `app/Http/Kernel.php`
- ❌ No `app/Console/Kernel.php`
- ❌ No `app/Providers/RouteServiceProvider.php` (by default)

---

## 7. Configuration Management Rules

### 7.1 Configuration Files
- **Standard config files** remain in `config/` directory
- **Custom configurations** (like `agent.php`, `codegenerator.php`) in `config/`
- **No provider registration** in `config/app.php`

### 7.2 Environment Configuration
- **`.env` file** for environment-specific settings
- **Configuration caching** via `php artisan config:cache`

---

## 8. SAIMS-Specific Architecture Rules

### 8.1 Custom Components
- **Agent Detection System** - `app/Agent/` directory with service provider
- **Code Generator System** - `app/CodeGenerator/` directory with service provider
- **Custom Services** - `app/Services/` for business logic
- **Custom Traits** - `app/Traits/` for shared functionality

### 8.2 Livewire Integration
- **Livewire components** in `app/Livewire/`
- **Flux UI components** for consistent design
- **Component organization** by feature (Auth, Registration, Settings)

### 8.3 Database Architecture
- **Domain-driven model organization** in `app/Models/`
- **Polymorphic relationships** for shared information
- **Comprehensive audit trails** via traits
- **Soft deletes** with user attribution

---

## 9. Development Guidelines

### 9.1 Code Organization
- **Follow PSR-4 autoloading** standards
- **Use dependency injection** in constructors
- **Implement service layer** for complex business logic
- **Use traits** for cross-cutting concerns

### 9.2 Naming Conventions
- **Models:** PascalCase (e.g., `UserRoleAssignment`)
- **Controllers:** PascalCase with Controller suffix
- **Middleware:** PascalCase with descriptive names
- **Services:** PascalCase with Service suffix
- **Livewire:** PascalCase, organized by feature

### 9.3 Testing Structure
- **Feature tests** in `tests/Feature/`
- **Unit tests** in `tests/Unit/`
- **Use Pest** as the testing framework
- **Database testing** with factories and seeders

---

## 10. Package Management Rules

### 10.1 Dependency Management
- **Use Composer** for PHP dependencies
- **Use NPM** for frontend dependencies
- **Never manually edit** `composer.json` for package installation
- **Use appropriate package manager commands**

### 10.2 Current SAIMS Dependencies
```json
{
    "require": {
        "php": "^8.2",
        "laravel/framework": "^12.0",
        "laravel/tinker": "^2.10.1",
        "livewire/flux": "^2.1.1",
        "livewire/volt": "^1.7.0"
    }
}
```

---

## 11. Security and Performance Rules

### 11.1 Security Practices
- **Use middleware** for authentication and authorization
- **Implement CSRF protection** on forms
- **Use rate limiting** for API endpoints
- **Validate all inputs** using Form Requests
- **Use secure session configuration**

### 11.2 Performance Optimization
- **Use caching** for expensive operations
- **Implement eager loading** for relationships
- **Use database indexing** appropriately
- **Optimize queries** with proper relationships

---

## 12. Deployment and Environment Rules

### 12.1 Production Deployment
- **Cache configurations** via `php artisan config:cache`
- **Cache routes** via `php artisan route:cache`
- **Optimize autoloader** via `composer install --optimize-autoloader`
- **Use proper environment variables**

### 12.2 Development Environment
- **Use `php artisan serve`** for local development
- **Use database migrations** for schema changes
- **Use seeders** for test data
- **Use proper debugging tools**

---

## Conclusion

These rules ensure consistent development practices aligned with Laravel 12's architecture. The removal of Kernel.php files and centralization of configuration in `bootstrap/app.php` represents Laravel's move toward a more streamlined and intuitive application structure.

**Key Takeaways:**
1. **All middleware configuration** goes in `bootstrap/app.php`
2. **Service providers** are registered in `bootstrap/providers.php`
3. **Routing configuration** is handled in `bootstrap/app.php`
4. **No more Kernel.php files** - everything is centralized
5. **Maintain existing SAIMS architecture** while following Laravel 12 patterns

---

*This document should be referenced for all development decisions and updated as the project evolves.*
