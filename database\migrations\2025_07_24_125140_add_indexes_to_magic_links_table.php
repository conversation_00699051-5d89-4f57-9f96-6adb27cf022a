<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('magic_links', function (Blueprint $table) {
            $table->index(['email', 'expires_at']);
            $table->index(['user_id', 'expires_at']);
            $table->index('cancelled_at');
            $table->index('used_at');
            $table->index('history_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('magic_links', function (Blueprint $table) {
            $table->dropIndex(['email', 'expires_at']);
            $table->dropIndex(['user_id', 'expires_at']);
            $table->dropIndex(['cancelled_at']);
            $table->dropIndex(['used_at']);
            $table->dropIndex(['history_id']);
        });
    }
};
