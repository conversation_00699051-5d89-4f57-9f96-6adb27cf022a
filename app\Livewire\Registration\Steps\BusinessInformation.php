<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\Auth\RegistrationAttempt;

class BusinessInformation extends Component
{
    public $entityName = '';
    public $logo = null;
    public $website = '';
    public $contacts = [];
    public $addresses = [];
    public $attempt;
    
    // Reference data
    public $states = [];
    public $citiesData = [];
    
    protected function rules()
    {
        $rules = [
            'entityName' => 'required|string|max:100',
            'website' => 'nullable|url|max:255',
            'contacts' => 'required|array|min:1|max:4',
            'contacts.*.name' => 'required|string|max:255',
            'contacts.*.email' => 'nullable|email|max:255',
            'contacts.*.phone' => 'required|regex:/^[6-9]\d{9}$/',
            'contacts.*.title' => 'nullable|string|max:20',
            'contacts.*.position' => 'nullable|string|max:100',
            'addresses' => 'required|array|min:1|max:4',
            'addresses.*.address_line1' => 'required|string|max:255',
            'addresses.*.address_line2' => 'nullable|string|max:255',
            'addresses.*.state' => 'required|string|size:2',
            'addresses.*.city' => 'required|string|max:100',
            'addresses.*.postal_code' => 'required|regex:/^[1-9][0-9]{5}$/',
            'addresses.*.is_primary' => 'boolean'
        ];
        
        return $rules;
    }
    
    protected $messages = [
        'entityName.required' => 'Business name is required',
        'website.url' => 'Please enter a valid website URL',
        'contacts.required' => 'At least one contact is required',
        'contacts.min' => 'At least one contact is required',
        'contacts.max' => 'Maximum 4 contacts allowed',
        'contacts.*.name.required' => 'Contact name is required',
        'contacts.*.phone.required' => 'Contact phone is required',
        'contacts.*.phone.regex' => 'Please enter a valid Indian mobile number',
        'contacts.*.title.max' => 'Title cannot exceed 20 characters',
        'contacts.*.position.max' => 'Position cannot exceed 100 characters',
        'addresses.required' => 'At least one address is required',
        'addresses.min' => 'At least one address is required',
        'addresses.max' => 'Maximum 4 addresses allowed',
        'addresses.*.address_line1.required' => 'Address line 1 is required',
        'addresses.*.state.required' => 'State is required',
        'addresses.*.city.required' => 'City is required',
        'addresses.*.postal_code.required' => 'Postal code is required',
        'addresses.*.postal_code.regex' => 'Please enter a valid 6-digit postal code'
    ];
    
    public function mount()
    {
        $this->attempt = RegistrationAttempt::find(session('registration_attempt_id'));
        
        if (!$this->attempt) {
            return redirect()->route('register');
        }
        
        // Load Indian states
        $this->loadStates();
        
        // Load saved data if exists
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        if (isset($stagesData['business_information'])) {
            $data = $stagesData['business_information'];
            $this->entityName = $data['entity_name'] ?? '';
            $this->logo = $data['logo'] ?? null;
            $this->website = $data['website'] ?? '';
            $this->contacts = $data['contacts'] ?? [['name' => '', 'email' => '', 'phone' => '', 'title' => '', 'position' => '']];
            $this->addresses = $data['addresses'] ?? [['address_line1' => '', 'address_line2' => '', 'state' => '', 'city' => '', 'postal_code' => '', 'is_primary' => true]];
            
            // Load cities for saved addresses
            foreach ($this->addresses as $index => $address) {
                if ($address['state']) {
                    $this->loadCities($address['state'], $index);
                }
            }
        } else {
            // Initialize with one empty contact and address
            $this->contacts = [['name' => '', 'email' => '', 'phone' => '', 'title' => '', 'position' => '']];
            $this->addresses = [['address_line1' => '', 'address_line2' => '', 'state' => '', 'city' => '', 'postal_code' => '', 'is_primary' => true]];
        }
    }
    
    public function loadStates()
    {
        $this->states = [
            ['code' => 'AN', 'name' => 'Andaman and Nicobar Islands'],
            ['code' => 'AP', 'name' => 'Andhra Pradesh'],
            ['code' => 'AR', 'name' => 'Arunachal Pradesh'],
            ['code' => 'AS', 'name' => 'Assam'],
            ['code' => 'BR', 'name' => 'Bihar'],
            ['code' => 'CH', 'name' => 'Chandigarh'],
            ['code' => 'CT', 'name' => 'Chhattisgarh'],
            ['code' => 'DN', 'name' => 'Dadra and Nagar Haveli and Daman and Diu'],
            ['code' => 'DL', 'name' => 'Delhi'],
            ['code' => 'GA', 'name' => 'Goa'],
            ['code' => 'GJ', 'name' => 'Gujarat'],
            ['code' => 'HR', 'name' => 'Haryana'],
            ['code' => 'HP', 'name' => 'Himachal Pradesh'],
            ['code' => 'JK', 'name' => 'Jammu and Kashmir'],
            ['code' => 'JH', 'name' => 'Jharkhand'],
            ['code' => 'KA', 'name' => 'Karnataka'],
            ['code' => 'KL', 'name' => 'Kerala'],
            ['code' => 'LA', 'name' => 'Ladakh'],
            ['code' => 'LD', 'name' => 'Lakshadweep'],
            ['code' => 'MP', 'name' => 'Madhya Pradesh'],
            ['code' => 'MH', 'name' => 'Maharashtra'],
            ['code' => 'MN', 'name' => 'Manipur'],
            ['code' => 'ML', 'name' => 'Meghalaya'],
            ['code' => 'MZ', 'name' => 'Mizoram'],
            ['code' => 'NL', 'name' => 'Nagaland'],
            ['code' => 'OR', 'name' => 'Odisha'],
            ['code' => 'PY', 'name' => 'Puducherry'],
            ['code' => 'PB', 'name' => 'Punjab'],
            ['code' => 'RJ', 'name' => 'Rajasthan'],
            ['code' => 'SK', 'name' => 'Sikkim'],
            ['code' => 'TN', 'name' => 'Tamil Nadu'],
            ['code' => 'TG', 'name' => 'Telangana'],
            ['code' => 'TR', 'name' => 'Tripura'],
            ['code' => 'UP', 'name' => 'Uttar Pradesh'],
            ['code' => 'UT', 'name' => 'Uttarakhand'],
            ['code' => 'WB', 'name' => 'West Bengal']
        ];
    }
    
    public function loadCities($stateCode, $addressIndex)
    {
        // In production, this would fetch from database
        // For now, simplified example with limited cities
        $cities = [
            'MH' => ['Mumbai', 'Pune', 'Nagpur', 'Thane', 'Nashik', 'Aurangabad'],
            'KA' => ['Bengaluru', 'Mysuru', 'Mangaluru', 'Hubballi', 'Belagavi'],
            'DL' => ['New Delhi', 'Central Delhi', 'East Delhi', 'North Delhi', 'South Delhi', 'West Delhi'],
            'GJ' => ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Gandhinagar'],
            'TN' => ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem'],
        ];
        
        $this->citiesData[$addressIndex] = $cities[$stateCode] ?? [];
    }
    
    public function updatedAddresses($value, $key)
    {
        // Extract index and field from the key (e.g., "0.state")
        if (preg_match('/^(\d+)\.state$/', $key, $matches)) {
            $index = $matches[1];
            $this->loadCities($value, $index);
            // Reset city when state changes
            $this->addresses[$index]['city'] = '';
        }
    }
    
    public function addContact()
    {
        if (count($this->contacts) < 4) {
            $this->contacts[] = ['name' => '', 'email' => '', 'phone' => ''];
        }
    }
    
    public function removeContact($index)
    {
        if (count($this->contacts) > 1) {
            array_splice($this->contacts, $index, 1);
            $this->contacts = array_values($this->contacts);
        }
    }
    
    public function addAddress()
    {
        if (count($this->addresses) < 4) {
            $this->addresses[] = ['address_line1' => '', 'address_line2' => '', 'state' => '', 'city' => '', 'postal_code' => ''];
        }
    }
    
    public function removeAddress($index)
    {
        if (count($this->addresses) > 1) {
            array_splice($this->addresses, $index, 1);
            $this->addresses = array_values($this->addresses);
            
            // Clean up cities data
            unset($this->citiesData[$index]);
            $this->citiesData = array_values($this->citiesData);
        }
    }
    
    public function saveAndContinue()
    {
        $this->validate();
        
        // Save stage data
        $stagesData = json_decode($this->attempt->stages_data, true) ?? [];
        $stagesData['business_information'] = [
            'entity_name' => $this->entityName,
            'logo' => $this->logo,
            'website' => $this->website,
            'contacts' => $this->contacts,
            'addresses' => $this->addresses
        ];
        
        $stagesCompleted = json_decode($this->attempt->stages_completed, true) ?? [];
        if (!in_array('business_information', $stagesCompleted)) {
            $stagesCompleted[] = 'business_information';
        }
        
        $this->attempt->update([
            'stages_data' => json_encode($stagesData),
            'stages_completed' => json_encode($stagesCompleted),
            'current_stage' => 'personal_details'
        ]);
        
        $this->dispatch('stepCompleted', 'business_information');
    }
    
    public function stepBack()
    {
        $this->dispatch('stepBack');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.business-information');
    }
} 