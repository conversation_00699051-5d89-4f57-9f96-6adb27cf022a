<?php
// app/Models/Entity/Entity.php
namespace App\Models\Entity;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\HasAuditFields;
use App\Traits\HasActivation;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use App\Models\Information\Contact;
use App\Models\Information\Address;
use App\Models\Information\Kyc;
use App\Models\Information\Tax;
use App\Models\SiteSetting\Notification;
use App\Models\User;
use App\Models\CodeGenerationAudit;

/**
 * Class Entity
 *
 * Represents a business entity in the system (supplier, distributor, dealer).
 * Entities can have hierarchical relationships (parent-child) and business relationships
 * (supply chain, service provision). They use polymorphic relationships for shared
 * information like addresses, contacts, KYC documents, tax info, and notification preferences.
 *
 * Key Features:
 * - Hierarchical structure via parent_id
 * - Business relationships via EntityRelationship model
 * - Polymorphic many-to-many relationships for shared information
 * - Approval workflow support
 * - Comprehensive audit trail
 * - Activation/deactivation lifecycle
 *
 * @property int $id Auto-incrementing primary key
 * @property string $entity_id Unique business identifier (25 chars)
 * @property string $entity_name Legal name of the entity (100 chars)
 * @property string|null $logo Path to entity logo image
 * @property string|null $website Entity website URL
 * @property string $entity_type Type: supplier|distributor|dealer
 * @property bool $is_goods_provider Whether entity provides goods
 * @property bool $is_service_provider Whether entity provides services
 * @property int|null $parent_id Reference to parent entity
 * @property bool $is_active Whether entity is currently active
 * @property bool $is_approval_required Whether approval is needed
 * @property string $approval_status Current approval status
 * @property int $created_by User ID who created this record
 * @property int|null $updated_by User ID who last updated this record
 * @property int|null $deleted_by User ID who soft deleted this record
 * @property int|null $restored_by User ID who restored this record
 * @property int|null $inactivated_by User ID who inactivated this record
 * @property \Carbon\Carbon|null $restored_at When record was restored
 * @property \Carbon\Carbon|null $inactivated_at When record was inactivated
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder active()
 * @method static \Illuminate\Database\Eloquent\Builder approved()
 * @method static \Illuminate\Database\Eloquent\Builder operational()
 * @method static \Illuminate\Database\Eloquent\Builder providers()
 * @method static \Illuminate\Database\Eloquent\Builder byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder pendingApproval()
 */
class Entity extends Model
{
    use SoftDeletes, HasAuditFields, HasActivation;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'entities';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'entity_id',
        'entity_name',
        'logo',
        'website',
        'entity_type',
        'is_goods_provider',
        'is_service_provider',
        'parent_id',
        'is_active',
        'is_approval_required',
        'approval_status',
        'created_by',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
        'inactivated_by',
        'inactivated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_goods_provider' => 'boolean',
        'is_service_provider' => 'boolean',
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
        'restored_at' => 'datetime',
        'inactivated_at' => 'datetime',
    ];

    /**
     * The attributes that should be treated as dates.
     *
     * @var array<string>
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
        'restored_at',
        'inactivated_at',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<string>
     */
    protected $appends = [
        'is_operational',
        'is_provider',
        'entity_type_label',
        'approval_status_label'
    ];

    /**
     * Entity type constants.
     *
     * @var array<string, string>
     */
    const ENTITY_TYPES = [
        'supplier' => 'Supplier',
        'distributor' => 'Distributor',
        'dealer' => 'Dealer'
    ];

    /**
     * Approval status constants.
     *
     * @var array<string, string>
     */
    const APPROVAL_STATUSES = [
        'pending' => 'Pending Approval',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'expired' => 'Expired',
        'cancelled' => 'Cancelled',
        'on_hold' => 'On Hold'
    ];

    // ===== ACCESSORS =====

    /**
     * Get human-readable entity type.
     *
     * @return string
     */
    public function getEntityTypeLabelAttribute(): string
    {
        return self::ENTITY_TYPES[$this->entity_type] ?? 'Unknown';
    }

    /**
     * Get human-readable approval status.
     *
     * @return string
     */
    public function getApprovalStatusLabelAttribute(): string
    {
        return self::APPROVAL_STATUSES[$this->approval_status] ?? 'Unknown';
    }

    /**
     * Check if the entity is operationally active.
     *
     * @return bool
     */
    public function getIsOperationalAttribute(): bool
    {
        return $this->isOperational();
    }

    /**
     * Check if entity provides any services.
     *
     * @return bool
     */
    public function getIsProviderAttribute(): bool
    {
        return $this->isProvider();
    }

    // ===== BUSINESS LOGIC METHODS =====

    /**
     * Check if the entity is operationally active.
     * Entity must be active AND approved to be considered operational.
     *
     * @return bool
     */
    public function isOperational(): bool
    {
        return $this->isActive() && $this->approval_status === 'approved';
    }

    /**
     * Check if entity provides any services (goods or services).
     *
     * @return bool
     */
    public function isProvider(): bool
    {
        return $this->is_goods_provider || $this->is_service_provider;
    }

    /**
     * Check if entity is a top-level entity (no parent).
     *
     * @return bool
     */
    public function isTopLevel(): bool
    {
        return $this->parent_id === null;
    }

    /**
     * Check if entity has child entities.
     *
     * @return bool
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    // ===== USER RELATIONSHIPS =====

    /**
     * Get all users belonging to this entity.
     *
     * @return HasMany
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'entity_id');
    }

    /**
     * Get only operationally active users for this entity.
     *
     * @return HasMany
     */
    public function activeUsers(): HasMany
    {
        return $this->users()->operational();
    }

    // ===== HIERARCHY RELATIONSHIPS =====

    /**
     * Get the parent entity (if this is a child entity).
     *
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'parent_id');
    }

    /**
     * Get all child entities.
     *
     * @return HasMany
     */
    public function children(): HasMany
    {
        return $this->hasMany(Entity::class, 'parent_id');
    }

    /**
     * Get all descendants (children, grandchildren, etc.) recursively.
     *
     * @return \Illuminate\Support\Collection
     */
    public function descendants()
    {
        $descendants = collect();
        
        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->descendants());
        }
        
        return $descendants;
    }

    /**
     * Get all ancestors (parent, grandparent, etc.) recursively.
     *
     * @return \Illuminate\Support\Collection
     */
    public function ancestors()
    {
        $ancestors = collect();
        $current = $this->parent;
        
        while ($current) {
            $ancestors->push($current);
            $current = $current->parent;
        }
        
        return $ancestors;
    }

    // ===== BUSINESS RELATIONSHIPS =====

    /**
     * Get all relationships where this entity is the source.
     *
     * @return HasMany
     */
    public function entityRelationshipsAsSource(): HasMany
    {
        return $this->hasMany(EntityRelationship::class, 'source_entity_id');
    }

    /**
     * Get all relationships where this entity is the target.
     *
     * @return HasMany
     */
    public function entityRelationshipsAsTarget(): HasMany
    {
        return $this->hasMany(EntityRelationship::class, 'target_entity_id');
    }

    /**
     * Get entities that supply to this entity.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function suppliers()
    {
        return $this->entityRelationshipsAsTarget()
            ->where('relationship_type', 'supplies_to')
            ->operational()
            ->with('sourceEntity')
            ->get()
            ->pluck('sourceEntity');
    }

    /**
     * Get entities that this entity supplies to.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function customers()
    {
        return $this->entityRelationshipsAsSource()
            ->whereIn('relationship_type', ['supplies_to', 'sells_to'])
            ->operational()
            ->with('targetEntity')
            ->get()
            ->pluck('targetEntity');
    }

    // ===== POLYMORPHIC RELATIONSHIPS =====

    /**
     * Get all addresses for this entity.
     *
     * @return MorphToMany
     */
    public function addresses(): MorphToMany
    {
        return $this->morphToMany(Address::class, 'addressable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all contacts for this entity.
     *
     * @return MorphToMany
     */
    public function contacts(): MorphToMany
    {
        return $this->morphToMany(Contact::class, 'contactable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all KYC documents for this entity.
     *
     * @return MorphToMany
     */
    public function kycs(): MorphToMany
    {
        return $this->morphToMany(Kyc::class, 'kycable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get all tax information for this entity.
     *
     * @return MorphToMany
     */
    public function taxes(): MorphToMany
    {
        return $this->morphToMany(Tax::class, 'taxable')
            ->withPivot('is_primary')
            ->withTimestamps()
            ->orderByPivot('is_primary', 'desc');
    }

    /**
     * Get notification preferences for this entity.
     *
     * @return MorphToMany
     */
    public function notifications(): MorphToMany
    {
        return $this->morphToMany(Notification::class, 'notifiable')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    // ===== CODE GENERATION AUDIT =====

    /**
     * Get all code generation audits for this entity.
     *
     * @return HasMany
     */
    public function codeGenerationAudits(): HasMany
    {
        return $this->hasMany(CodeGenerationAudit::class, 'entity_id');
    }

    // ===== HELPER METHODS =====

    /**
     * Get the primary address for this entity.
     *
     * @return Address|null
     */
    public function getPrimaryAddress(): ?Address
    {
        return $this->addresses()->wherePivot('is_primary', true)->first();
    }

    /**
     * Get the primary contact for this entity.
     *
     * @return Contact|null
     */
    public function getPrimaryContact(): ?Contact
    {
        return $this->contacts()->wherePivot('is_primary', true)->first();
    }

    /**
     * Get the primary KYC document for this entity.
     *
     * @return Kyc|null
     */
    public function getPrimaryKyc(): ?Kyc
    {
        return $this->kycs()->wherePivot('is_primary', true)->first();
    }

    /**
     * Get the primary tax information for this entity.
     *
     * @return Tax|null
     */
    public function getPrimaryTax(): ?Tax
    {
        return $this->taxes()->wherePivot('is_primary', true)->first();
    }

    /**
     * Get the primary notification preference for this entity.
     *
     * @return Notification|null
     */
    public function getPrimaryNotification(): ?Notification
    {
        return $this->notifications()->wherePivot('is_primary', true)->first();
    }

    /**
     * Get admin users for this entity.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAdminUsers()
    {
        // This would be implemented once roles are set up
        return $this->users()->operational()->get();
    }

    // ===== QUERY SCOPES =====

    /**
     * Scope to only approved entities.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApproved($query)
    {
        return $query->where('approval_status', 'approved');
    }

    /**
     * Scope to entities by type.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('entity_type', $type);
    }

    /**
     * Scope to entities pending approval.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePendingApproval($query)
    {
        return $query->where('is_approval_required', true)
            ->where('approval_status', 'pending');
    }

    /**
     * Scope to operational entities (active and approved).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOperational($query)
    {
        return $query->active()->approved();
    }

    /**
     * Scope to provider entities (goods or services).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeProviders($query)
    {
        return $query->where(function ($q) {
            $q->where('is_goods_provider', true)
                ->orWhere('is_service_provider', true);
        });
    }

    /**
     * Scope to top-level entities (no parent).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to child entities (have parent).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeChildren($query)
    {
        return $query->whereNotNull('parent_id');
    }
}