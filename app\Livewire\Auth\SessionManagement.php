<?php

namespace App\Livewire\Auth;

use App\Models\Auth\LoginHistory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class SessionManagement extends Component
{
    /**
     * Render the component.
     */
    public function render()
    {
        $user = Auth::user();
        
        // Get all active sessions for the current user
        $activeSessions = LoginHistory::where('user_id', $user->id)
            ->whereNull('logout_at')
            ->orderBy('login_at', 'desc')
            ->get();
            
        // Mark the current session
        $currentSessionId = Session::getId();
        
        return view('livewire.auth.session-management', [
            'activeSessions' => $activeSessions,
            'currentSessionId' => $currentSessionId,
        ]);
    }
    
    /**
     * Terminate a specific session
     *
     * @param string $sessionId
     * @return void
     */
    public function terminateSession(string $sessionId)
    {
        $user = Auth::user();
        
        // Find and terminate the specified session
        $session = LoginHistory::where('user_id', $user->id)
            ->where('session_id', $sessionId)
            ->whereNull('logout_at')
            ->first();
        
        if ($session) {
            $session->update([
                'logout_at' => now(),
                'logout_reason' => 'terminated_by_user',
                'duration_seconds' => now()->diffInSeconds($session->login_at),
            ]);
            
            $this->dispatch('session-terminated');
        }
    }
} 