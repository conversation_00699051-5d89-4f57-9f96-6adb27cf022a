<?php

namespace App\Livewire\Admin\Rbac\Users;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\User;
use App\Models\Rbac\UserRoleAssignment;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Layout;

#[Layout('components.admin.layout')]
class UserRoleIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $filterRole = '';
    public $filterStatus = '';
    
    protected $queryString = [
        'search' => ['except' => ''],
        'filterRole' => ['except' => ''],
        'filterStatus' => ['except' => '']
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function getUsersProperty()
    {
        $query = User::where('entity_id', Auth::user()->entity_id);

        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        // Temporarily disable filtering until relationship is confirmed working
        // if ($this->filterRole) {
        //     $query->whereHas('roleAssignments', function($q) {
        //         $q->where('role_id', $this->filterRole)
        //           ->current();
        //     });
        // }

        // if ($this->filterStatus) {
        //     if ($this->filterStatus === 'active') {
        //         $query->whereHas('roleAssignments', function($q) {
        //             $q->current();
        //         });
        //     } else {
        //         $query->whereDoesntHave('roleAssignments', function($q) {
        //             $q->current();
        //         });
        //     }
        // }

        return $query->orderBy('name')
                    ->paginate(15);
    }

    public function getAvailableRolesProperty()
    {
        return \App\Models\Rbac\SystemRole::where('entity_id', Auth::user()->entity_id)
            ->operational()
            ->orderBy('hierarchy_level')
            ->get();
    }

    public function render()
    {
        return view('livewire.admin.rbac.users.user-role-index', [
            'users' => $this->users,
            'availableRoles' => $this->availableRoles
        ])->with([
            'heading' => __('User Role Assignments'),
            'subheading' => __('Manage user role assignments and permissions'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.users.index')],
                ['label' => __('User Assignments'), 'url' => route('admin.rbac.users.index')]
            ]
        ]);
    }
}
