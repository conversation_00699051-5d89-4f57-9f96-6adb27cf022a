<?php

namespace App\Livewire\Registration\Steps;

use Livewire\Component;
use App\Models\User;
use App\Models\Auth\RegistrationAttempt;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Mail\Registration\OtpMail;

class EmailVerification extends Component
{
    public $email = '';
    public $otp = '';
    public $showOtpField = false;
    public $otpSent = false;
    public $attemptId = null;
    
    protected $rules = [
        'email' => 'required|email|max:255',
        'otp' => 'required_if:showOtpField,true|digits:6'
    ];
    
    protected $messages = [
        'email.required' => 'Email address is required',
        'email.email' => 'Please provide a valid email address',
        'otp.required_if' => 'OTP is required',
        'otp.digits' => 'OTP must be 6 digits'
    ];
    
    public function sendOtp()
    {
        $this->validate(['email' => 'required|email|max:255']);
        
        // Check if email already registered
        if (User::where('email', $this->email)->exists()) {
            $this->addError('email', 'This email is already registered.');
            return;
        }
        
        // Generate OTP
        $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
        
        // Create or update registration attempt
        $attempt = RegistrationAttempt::updateOrCreate(
            ['email' => $this->email, 'is_submitted' => false],
            [
                'current_stage' => 'email_verification',
                'stages_completed' => json_encode([]),
                'stages_data' => json_encode(['email' => $this->email]),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'verification_token' => $otp,
                'verification_sent_at' => now(),
                'expires_at' => now()->addHours(24),
                'resume_token' => Str::random(32),
                'source' => 'web'
            ]
        );
        
        $this->attemptId = $attempt->id;
        
        // Send OTP email
        Mail::to($this->email)->send(new OtpMail($otp));
        
        $this->showOtpField = true;
        $this->otpSent = true;
        
        session()->flash('message', 'OTP sent to your email. Please check your inbox.');
    }
    
    public function verifyOtp()
    {
        $this->validate();
        
        $attempt = RegistrationAttempt::find($this->attemptId);
        
        if (!$attempt) {
            $this->addError('otp', 'Registration session has expired. Please start again.');
            return;
        }
        
        // Check if OTP is expired (30 minutes)
        if ($attempt->verification_sent_at->addMinutes(30)->isPast()) {
            $this->addError('otp', 'OTP has expired. Please request a new one.');
            return;
        }
        
        // Verify OTP
        if ($attempt->verification_token !== $this->otp) {
            $this->addError('otp', 'Invalid OTP. Please try again.');
            return;
        }
        
        // Mark email as verified
        $attempt->update([
            'is_email_verified' => true,
            'current_stage' => 'entity_type_selection',
            'stages_completed' => json_encode(['email_verification'])
        ]);
        
        // Store attempt ID in session for next steps
        session(['registration_attempt_id' => $attempt->id]);
        
        // Emit event to parent wizard
        $this->dispatch('stepCompleted', 'email_verification');
    }
    
    public function resendOtp()
    {
        if (!$this->attemptId) return;
        
        $attempt = RegistrationAttempt::find($this->attemptId);
        
        if (!$attempt) {
            $this->addError('otp', 'Registration session has expired. Please start again.');
            return;
        }
        
        // Check if we can resend (minimum 1 minute gap)
        if ($attempt->verification_sent_at->addMinute()->isFuture()) {
            session()->flash('error', 'Please wait before requesting another OTP.');
            return;
        }
        
        // Generate new OTP
        $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
        $attempt->update([
            'verification_token' => $otp,
            'verification_sent_at' => now()
        ]);
        
        // Send OTP email
        Mail::to($this->email)->send(new OtpMail($otp));
        
        session()->flash('message', 'New OTP sent successfully.');
    }
    
    public function render()
    {
        return view('livewire.registration.steps.email-verification');
    }
} 