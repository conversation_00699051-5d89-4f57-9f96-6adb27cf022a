@props([
    'name' => $attributes->whereStartsWith('wire:model')->first(),
    'placeholder' => null,
    'invalid' => null,
    'size' => 'base',
])

@php
$invalid ??= ($name && $errors->has($name));

$sizeClasses = match ($size) {
    'sm' => 'h-8 py-1.5 text-sm rounded-md',
    'lg' => 'h-12 py-3 text-lg rounded-lg',
    default => 'h-10 py-2 text-sm rounded-lg',
};

$baseClasses = 'appearance-none w-full px-3 pr-10 block shadow-sm border bg-white dark:bg-zinc-800';

$stateClasses = $invalid 
    ? 'border-red-500 text-red-900 focus:border-red-500 focus:ring-red-500 dark:border-red-600 dark:text-red-100'
    : 'border-zinc-300 text-zinc-900 focus:border-blue-500 focus:ring-blue-500 dark:border-zinc-600 dark:text-zinc-100';

$classes = "$baseClasses $sizeClasses $stateClasses focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors";
@endphp

<div class="relative">
    <select
        {{ $attributes->merge([
            'class' => $classes,
            'name' => $name
        ]) }}
        @if ($invalid) aria-invalid="true" @endif
    >
        @if ($placeholder)
            <option value="" disabled selected>{{ $placeholder }}</option>
        @endif

        {{ $slot }}
    </select>
    
    <!-- Dropdown arrow -->
    <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <svg class="h-5 w-5 text-zinc-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
    </div>
</div>
