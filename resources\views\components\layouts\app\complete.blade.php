{{-- resources/views/components/layouts/app/complete.blade.php - Enhanced Version --}}
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"

<head>
    @include('partials.head')
</head>

<body class="min-h-screen bg-white dark:bg-zinc-800 antialiased">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar Navigation -->
        <aside class="z-20 border-e border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900" role="navigation" aria-label="{{ __('Main Navigation') }}">
            <flux:sidebar sticky stashable>
                <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

                <!-- Logo -->
                <header class="me-5">
                    <a href="{{ route('dashboard') }}"
                        class="flex items-center space-x-2 rtl:space-x-reverse transition-opacity hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-zinc-50 dark:focus:ring-offset-zinc-900 rounded-md"
                        wire:navigate
                        aria-label="{{ __('Go to Dashboard') }}">
                        <x-app-logo />
                    </a>
                </header>

                <!-- Main Navigation -->
                <nav aria-label="{{ __('Main Navigation') }}">
                    <flux:navlist variant="outline">
                        <flux:navlist.group :heading="__('Platform')" class="grid">
                            <flux:navlist.item
                                icon="home"
                                :href="route('dashboard')"
                                :current="request()->routeIs('dashboard')"
                                wire:navigate>
                                {{ __('Dashboard') }}
                            </flux:navlist.item>
                        </flux:navlist.group>
                    </flux:navlist>
                </nav>

                <flux:spacer />

                <!-- External Links -->
                <nav aria-label="{{ __('External Links') }}">
                    <flux:navlist variant="outline">
                        <flux:navlist.item
                            icon="folder-git-2"
                            href="https://github.com/laravel/livewire-starter-kit"
                            target="_blank"
                            rel="noopener noreferrer">
                            {{ __('Repository') }}
                        </flux:navlist.item>

                        <flux:navlist.item
                            icon="book-open-text"
                            href="https://laravel.com/docs/starter-kits#livewire"
                            target="_blank"
                            rel="noopener noreferrer">
                            {{ __('Documentation') }}
                        </flux:navlist.item>
                    </flux:navlist>
                </nav>

                <!-- Desktop User Menu -->
                <div class="hidden lg:block">
                    @auth
                        <x-user-menu position="bottom" align="start" />
                    @endauth
                </div>
            </flux:sidebar>
        </aside>

        <!-- Main Content Wrapper -->
        <div class="flex flex-1 flex-col overflow-hidden">
            <!-- Top Header -->
            <header class="z-10 border-b border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900">
                <flux:header container>
                    <!-- Mobile Menu Toggle -->
                    <flux:sidebar.toggle
                        class="lg:hidden"
                        icon="bars-2"
                        inset="left"
                        aria-label="{{ __('Toggle Sidebar') }}" />

                    <!-- Mobile Logo -->
                    <div class="ms-2 me-5 lg:hidden">
                        <a href="{{ route('dashboard') }}"
                            class="flex items-center space-x-2 rtl:space-x-reverse transition-opacity hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-zinc-50 dark:focus:ring-offset-zinc-900 rounded-md"
                            wire:navigate
                            aria-label="{{ __('Go to Dashboard') }}">
                            <x-app-logo />
                        </a>
                    </div>

                    <!-- Page Title/Breadcrumbs -->
                    @if(isset($title))
                    <div class="hidden lg:block">
                        <flux:heading size="lg" as="h1">{{ $title }}</flux:heading>
                    </div>
                    @endif

                    @auth
                    <!-- Header Session Timer -->
                    <div class="mx-4 hidden lg:block flex-shrink-0">
                        <x-session-timer />
                    </div>
                    @endauth

                    <flux:spacer />

                    <!-- Header Actions -->
                    <nav aria-label="{{ __('Header Actions') }}" class="me-1.5">
                        <flux:navbar class="space-x-0.5 rtl:space-x-reverse py-0!">
                            @auth
                            <flux:tooltip :content="__('Transfer Session')" position="bottom">
                                <flux:navbar.item
                                    class="!h-10 [&>div>svg]:size-5"
                                    icon="share"
                                    :href="route('sessions.dashboard')"
                                    :label="__('Transfer Session')"
                                    aria-label="{{ __('Transfer Session') }}" />
                            </flux:tooltip>
                            @endauth

                            <flux:tooltip :content="__('Search')" position="bottom">
                                <flux:navbar.item
                                    class="!h-10 [&>div>svg]:size-5"
                                    icon="magnifying-glass"
                                    href="#"
                                    :label="__('Search')"
                                    aria-label="{{ __('Open Search') }}" />
                            </flux:tooltip>

                            <flux:tooltip :content="__('Notifications')" position="bottom">
                                <flux:navbar.item
                                    class="!h-10 [&>div>svg]:size-5"
                                    icon="bell"
                                    href="#"
                                    :label="__('Notifications')"
                                    aria-label="{{ __('View Notifications') }}" />
                            </flux:tooltip>
                        </flux:navbar>
                    </nav>

                    <!-- Mobile User Menu -->
                    <div class="lg:hidden">
                        @auth
                            <x-user-menu position="top" align="end" />
                        @endauth
                    </div>
                </flux:header>
            </header>

            <!-- Main Content Area - FIXED POSITIONING -->
            <main class="flex-1 overflow-y-auto" role="main">
                <!-- Remove flux:main wrapper that might be causing positioning issues -->
                <div class="h-full w-full">
                    {{ $slot }}
                </div>
            </main>

            <!-- Footer -->
            <x-app-footer />
        </div>
    </div>

    @fluxScripts
    @stack('scripts')
</body>

</html>