<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Add fields to support multi-stage registration process.
     */
    public function up(): void
    {
        // Create registration_attempts table if it doesn't exist
        if (!Schema::hasTable('registration_attempts')) {
            Schema::create('registration_attempts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
                $table->string('entity_code')->nullable();
                $table->string('email');
                $table->string('ip_address', 45);
                $table->text('user_agent');
                $table->boolean('success')->default(false);
                $table->timestamp('attempted_at');
                
                // Soft delete and restore functionality
                $table->softDeletes()->comment('Soft delete timestamp');
                $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
                $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
                $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
                
                // Index for common queries
                $table->index('email');
                $table->index('attempted_at');
            });
        }
        
        Schema::table('registration_attempts', function (Blueprint $table) {
            // Only add columns if they don't exist
            if (!Schema::hasColumn('registration_attempts', 'current_stage')) {
                // Current stage tracking
                $table->string('current_stage')->default('email_verification')->comment('Current stage of the registration process');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'stages_data')) {
                // Store data for each stage
                $table->json('stages_data')->nullable()->comment('JSON data for each completed stage');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'stages_completed')) {
                // Track completed stages
                $table->json('stages_completed')->nullable()->comment('Array of completed stage names');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'entity_type')) {
                // Entity type (for dynamic form fields)
                $table->string('entity_type')->nullable()->comment('Type of entity being registered (distributor, dealer, etc.)');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'verification_token')) {
                // Email verification
                $table->string('verification_token')->nullable()->comment('Token for email verification');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'verification_sent_at')) {
                $table->timestamp('verification_sent_at')->nullable()->comment('When the verification OTP was sent');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'is_email_verified')) {
                $table->boolean('is_email_verified')->default(false)->comment('Whether email has been verified');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'resume_token')) {
                // Resume functionality
                $table->string('resume_token')->nullable()->comment('Token for resuming incomplete registration');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'resume_token_created_at')) {
                $table->timestamp('resume_token_created_at')->nullable()->comment('When the resume token was created');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'expires_at')) {
                // Expiration
                $table->timestamp('expires_at')->nullable()->comment('When this registration attempt expires');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'is_submitted')) {
                // Submission status
                $table->boolean('is_submitted')->default(false)->comment('Whether registration has been submitted for approval');
            }
            
            if (!Schema::hasColumn('registration_attempts', 'submitted_at')) {
                $table->timestamp('submitted_at')->nullable()->comment('When registration was submitted');
            }
            
            // Add indexes for common queries
            try {
                $table->index('current_stage');
            } catch (\Exception $e) {
                // Index might already exist
            }
            
            try {
                $table->index('entity_type');
            } catch (\Exception $e) {
                // Index might already exist
            }
            
            try {
                $table->index('resume_token');
            } catch (\Exception $e) {
                // Index might already exist
            }
            
            try {
                $table->index('verification_token');
            } catch (\Exception $e) {
                // Index might already exist
            }
            
            try {
                $table->index('expires_at');
            } catch (\Exception $e) {
                // Index might already exist
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registration_attempts', function (Blueprint $table) {
            // Remove all added columns if they exist
            $columns = [
                'current_stage',
                'stages_data',
                'stages_completed',
                'entity_type',
                'verification_token',
                'verification_sent_at',
                'is_email_verified',
                'resume_token',
                'resume_token_created_at',
                'expires_at',
                'is_submitted',
                'submitted_at',
            ];
            
            $existingColumns = [];
            foreach ($columns as $column) {
                if (Schema::hasColumn('registration_attempts', $column)) {
                    $existingColumns[] = $column;
                }
            }
            
            if (!empty($existingColumns)) {
                $table->dropColumn($existingColumns);
            }
            
            // Drop indexes - we'll use try/catch since we can't easily check if they exist
            try {
                $table->dropIndex(['current_stage']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            try {
                $table->dropIndex(['entity_type']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            try {
                $table->dropIndex(['resume_token']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            try {
                $table->dropIndex(['verification_token']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            
            try {
                $table->dropIndex(['expires_at']);
            } catch (\Exception $e) {
                // Index might not exist
            }
        });
    }
};
