<?php

namespace Tests\Unit;

use App\Http\Middleware\CheckActiveSessions;
use App\Services\LoginTrackingService;
use PHPUnit\Framework\TestCase;

class CheckActiveSessionsTest extends TestCase
{
    protected CheckActiveSessions $middleware;
    protected LoginTrackingService $loginTracker;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->loginTracker = $this->createMock(LoginTrackingService::class);
        $this->middleware = new CheckActiveSessions($this->loginTracker);
    }
    
    public function testGetSessionTimeoutForRole()
    {
        // Use reflection to access protected method
        $reflection = new \ReflectionClass($this->middleware);
        $method = $reflection->getMethod('getSessionTimeoutForRole');
        $method->setAccessible(true);
        
        // Test Super Admin timeout
        $superAdminTimeout = $method->invoke($this->middleware, 1, true);
        $this->assertEquals(30 * 60, $superAdminTimeout);
        
        // Test Admin Manager timeout
        $adminManagerTimeout = $method->invoke($this->middleware, 2, false);
        $this->assertEquals(60 * 60, $adminManagerTimeout);
        
        // Test Admin timeout
        $adminTimeout = $method->invoke($this->middleware, 3, false);
        $this->assertEquals(90 * 60, $adminTimeout);
        
        // Test default timeout
        $defaultTimeout = $method->invoke($this->middleware, null, false);
        $this->assertEquals(120 * 60, $defaultTimeout);
    }
} 