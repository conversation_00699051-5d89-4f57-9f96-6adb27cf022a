<?php
// app/Models/Rbac/UserDepartmentAssignment.php
namespace App\Models\Rbac;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Entity\Entity;
use App\Models\User;
use App\Traits\HasAuditFields;

/**
 * Class UserDepartmentAssignment
 *
 * Assigns users to organizational departments.
 */
class UserDepartmentAssignment extends Model
{
    use SoftDeletes, HasAuditFields;

    protected $fillable = [
        'entity_id',
        'created_by',
        'user_id',
        'department_id',
        'assigned_from',
        'assigned_until',
        'is_active',
        'is_approval_required',
        'approval_status',
        'assignment_notes',
        'updated_by',
        'deleted_by',
        'restored_by',
        'restored_at',
    ];

    protected $casts = [
        'assigned_from' => 'date',
        'assigned_until' => 'date',
        'is_active' => 'boolean',
        'is_approval_required' => 'boolean',
        'restored_at' => 'datetime',
    ];

    // ===== RELATIONSHIPS =====

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id', 'entity_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(OrganizationDepartment::class, 'department_id');
    }
}
