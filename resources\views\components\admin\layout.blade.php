@props(['heading', 'subheading', 'breadcrumbs' => [], 'title' => null])

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    @include('partials.head')
</head>

<body class="min-h-screen bg-white dark:bg-zinc-800 antialiased">
    <div class="flex h-screen overflow-hidden">
        <!-- Admin Sidebar Navigation -->
        <aside class="z-20 border-e border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900 w-64" role="navigation" aria-label="{{ __('Admin Navigation') }}">
            <flux:sidebar sticky>
                <!-- Logo -->
                <header class="me-5">
                    <a href="{{ route('dashboard') }}"
                        class="flex items-center space-x-2 rtl:space-x-reverse transition-opacity hover:opacity-80 focus:opacity-80 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-zinc-50 dark:focus:ring-offset-zinc-900 rounded-md"
                        wire:navigate
                        aria-label="{{ __('Go to Dashboard') }}">
                        <x-app-logo />
                    </a>
                </header>

                <!-- Main Navigation -->
                <nav aria-label="{{ __('Main Navigation') }}">
                    <flux:navlist variant="outline">
                        <flux:navlist.group :heading="__('Platform')" class="grid">
                            <flux:navlist.item
                                icon="home"
                                :href="route('dashboard')"
                                :current="request()->routeIs('dashboard')"
                                wire:navigate>
                                {{ __('Dashboard') }}
                            </flux:navlist.item>
                        </flux:navlist.group>
                    </flux:navlist>
                </nav>

                <!-- Admin Navigation -->
                <div class="mt-6">
                    <x-admin.navigation />
                </div>

                <flux:spacer />

                <!-- External Links -->
                <nav aria-label="{{ __('External Links') }}">
                    <flux:navlist variant="outline">
                        <flux:navlist.item
                            icon="folder-git-2"
                            href="https://github.com/laravel/livewire-starter-kit"
                            target="_blank"
                            rel="noopener noreferrer">
                            {{ __('Repository') }}
                        </flux:navlist.item>

                        <flux:navlist.item
                            icon="book-open-text"
                            href="https://laravel.com/docs/starter-kits#livewire"
                            target="_blank"
                            rel="noopener noreferrer">
                            {{ __('Documentation') }}
                        </flux:navlist.item>
                    </flux:navlist>
                </nav>

                <!-- Desktop User Menu -->
                <div class="hidden lg:block">
                    @auth
                        <x-user-menu position="bottom" align="start" />
                    @endauth
                </div>
            </flux:sidebar>
        </aside>

        <!-- Main Content Wrapper -->
        <div class="flex flex-1 flex-col overflow-hidden">
            <!-- Top Header -->
            <header class="z-10 border-b border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900">
                <flux:header container>
                    <!-- Page Title -->
                    <div class="hidden lg:block">
                        <flux:heading size="lg" as="h1">{{ $title ?? $heading }}</flux:heading>
                    </div>

                    @auth
                    <!-- Header Session Timer -->
                    <div class="mx-4 hidden lg:block flex-shrink-0">
                        <x-session-timer />
                    </div>
                    @endauth

                    <flux:spacer />

                    <!-- Header Actions -->
                    <nav aria-label="{{ __('Header Actions') }}" class="me-1.5">
                        <flux:navbar class="space-x-0.5 rtl:space-x-reverse py-0!">
                            @auth
                            <flux:tooltip :content="__('Transfer Session')" position="bottom">
                                <flux:navbar.item
                                    class="!h-10 [&>div>svg]:size-5"
                                    icon="share"
                                    :href="route('sessions.dashboard')"
                                    :label="__('Transfer Session')"
                                    aria-label="{{ __('Transfer Session') }}" />
                            </flux:tooltip>
                            @endauth

                            <flux:tooltip :content="__('Search')" position="bottom">
                                <flux:navbar.item
                                    class="!h-10 [&>div>svg]:size-5"
                                    icon="magnifying-glass"
                                    href="#"
                                    :label="__('Search')"
                                    aria-label="{{ __('Open Search') }}" />
                            </flux:tooltip>

                            <flux:tooltip :content="__('Notifications')" position="bottom">
                                <flux:navbar.item
                                    class="!h-10 [&>div>svg]:size-5"
                                    icon="bell"
                                    href="#"
                                    :label="__('Notifications')"
                                    aria-label="{{ __('View Notifications') }}" />
                            </flux:tooltip>
                        </flux:navbar>
                    </nav>

                    <!-- Mobile User Menu -->
                    <div class="lg:hidden">
                        @auth
                            <x-user-menu position="top" align="end" />
                        @endauth
                    </div>
                </flux:header>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto p-6" role="main">
                <!-- Breadcrumb Navigation -->
                <x-breadcrumb :items="$breadcrumbs" />

                <!-- Content Card -->
                <div class="mt-6">
                    <flux:card>
                        <flux:card.header>
                            <flux:heading size="lg">{{ $heading }}</flux:heading>
                            <flux:subheading>{{ $subheading }}</flux:subheading>
                        </flux:card.header>

                        <flux:card.body>
                            {{ $slot }}
                        </flux:card.body>
                    </flux:card>
                </div>
            </main>

            <!-- Footer -->
            <x-app-footer />
        </div>
    </div>

    @fluxScripts
    @stack('scripts')
</body>

</html>
