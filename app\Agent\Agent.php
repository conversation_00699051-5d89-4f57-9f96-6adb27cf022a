<?php

namespace App\Agent;

use BadMethodCallException;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class Agent
{
    /**
     * User agent string
     * @var string
     */
    protected string $userAgent;

    /**
     * HTTP request headers
     * @var array
     */
    protected array $headers;

    /**
     * Configuration options
     * @var array
     */
    protected array $config;

    /**
     * Cache for detection results
     * @var array
     */
    protected array $cache = [];

    /**
     * Windows version mapping with Windows 11 detection
     */
    protected const WINDOWS_VERSIONS = [
        '10.0' => ['default' => '10', 'win11_pattern' => '/Windows NT 10\.0; Win64; x64.*Edge\/(9\d|1\d\d)/i'],
        '6.3' => '8.1',
        '6.2' => '8',
        '6.1' => '7',
        '6.0' => 'Vista',
        '5.2' => 'Server 2003 / XP x64',
        '5.1' => 'XP',
        '5.0' => '2000',
    ];

    /**
     * Common device model mappings
     */
    protected const APPLE_MODELS = [
        // iPhone models
        'iPhone13,1' => 'iPhone 12 Mini',
        'iPhone13,2' => 'iPhone 12',
        'iPhone13,3' => 'iPhone 12 Pro',
        'iPhone13,4' => 'iPhone 12 Pro Max',
        'iPhone14,2' => 'iPhone 13 Pro',
        'iPhone14,3' => 'iPhone 13 Pro Max',
        'iPhone14,4' => 'iPhone 13 Mini',
        'iPhone14,5' => 'iPhone 13',
        'iPhone14,6' => 'iPhone SE (3rd gen)',
        'iPhone14,7' => 'iPhone 14',
        'iPhone14,8' => 'iPhone 14 Plus',
        'iPhone15,2' => 'iPhone 14 Pro',
        'iPhone15,3' => 'iPhone 14 Pro Max',
        'iPhone15,4' => 'iPhone 15',
        'iPhone15,5' => 'iPhone 15 Plus',
        'iPhone16,1' => 'iPhone 15 Pro',
        'iPhone16,2' => 'iPhone 15 Pro Max',
    ];

    /**
     * Whitelist for magic __call method
     */
    protected const VALID_PROPERTIES = [
        'windows',
        'mac',
        'ios',
        'android',
        'linux',
        'chromeos',
        'blackberry',
        'unix',
        'apple',
        'samsung',
        'google',
        'huawei',
        'xiaomi',
        'oneplus',
        'sony'
    ];

    /**
     * Constructor
     *
     * @param array|null $headers HTTP headers (defaults to $_SERVER)
     * @param array|null $config Configuration options
     */
    public function __construct(array $headers = null, array $config = null)
    {
        $this->headers = $headers ?: $_SERVER;
        $this->userAgent = $this->headers['HTTP_USER_AGENT'] ?? '';
        $this->config = $config ?: config('agent', []);
    }

    // ========================
    // Core Device Type Detection
    // ========================

    /**
     * Check if the device is a mobile phone
     *
     * @return bool
     */
    public function isMobile(): bool
    {
        return $this->cached('isMobile', function () {
            if (empty($this->userAgent)) {
                return false;
            }
            
            return preg_match(
                '/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|rim)|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i',
                $this->userAgent
            ) || preg_match(
                '/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|ob)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(is|jp)|dbte|dc\-s|dica|dmob|do(c|op)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ts)|ic(40|50|c\-|vc\-|vc )|ig01|ikom|imei|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt(|ib)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(gr|lk|ng)|mc(ad|er|ev)|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|ad|gr|ha|mm|nw|re|ze)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[0-2]|n30(0|2)|n50(0|2)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|r980|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|pn)|sc(01|h\-|oo|mm)|sdk\/|se(c(\-|0|1)|47|mc|un|v w)|sfmp|sh(mo|ow)|si(50|70)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(us|v )|sy(01|mb)|t2(18|50)|t6(00|20)|t7(70)|ta(ad|ak)|tc(ad|eg)|tdg\-|tel(i|m)|tim\-|tk(a|l)|tm(ga|ha|\-mm)|toll|trot|tsm|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|ty)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i',
                substr($this->userAgent, 0, 4)
            );
        });
    }

    /**
     * Check if the device is a tablet
     *
     * @return bool
     */
    public function isTablet(): bool
    {
        return $this->cached('isTablet', function () {
            if (empty($this->userAgent)) {
                return false;
            }
            
            return !$this->isMobile() && preg_match(
                '/(tablet|ipad|playbook|silk)|(android(?!.*mobile))/i',
                $this->userAgent
            );
        });
    }

    /**
     * Check if the device is a desktop computer
     *
     * @return bool
     */
    public function isDesktop(): bool
    {
        return $this->cached('isDesktop', function () {
            return !$this->isMobile() && !$this->isTablet() && !$this->isBot() && !$this->isTV() && !$this->isSmartwatch();
        });
    }

    /**
     * Check if the device is a smartwatch
     *
     * @return bool
     */
    public function isSmartwatch(): bool
    {
        return $this->cached('isSmartwatch', function () {
            if (empty($this->userAgent)) {
                return false;
            }
            
            return preg_match('/(watch|wear|fitbit|garmin|applewatch|tizen)/i', $this->userAgent);
        });
    }

    /**
     * Check if the device is a TV
     *
     * @return bool
     */
    public function isTV(): bool
    {
        return $this->cached('isTV', function () {
            if (empty($this->userAgent)) {
                return false;
            }
            
            return preg_match('/(tv|smarttv|googletv|appletv|hbbtv|pov_tv|dlnadoc|roku|firetv|mibox|androidtv)/i', $this->userAgent);
        });
    }

    // ========================
    // Browser Detection
    // ========================

    /**
     * Get the browser name
     *
     * @return string|null
     */
    public function browser(): ?string
    {
        return $this->cached('browser', function () {
            if (empty($this->userAgent)) {
                return null;
            }

            // Ordered by specificity (most specific patterns first)
            $browsers = [
                // Edge must come before Chrome since Edge contains "Chrome" in its UA string
                'Edge' => '/(Edg|Edge|EdgA|EdgiOS)\/([\d.]+)/i',
                'Brave' => '/Brave\/([\d.]+)/i',
                'Vivaldi' => '/Vivaldi\/([\d.]+)/i',
                'Opera' => '/Opera|OPR\/([\d.]+)/i',
                'Samsung' => '/SamsungBrowser\/([\d.]+)/i',
                'UCBrowser' => '/UCBrowser|UCWEB\/([\d.]+)/i',
                'WeChat' => '/MicroMessenger\/([\d.]+)/i',
                'Firefox' => '/Firefox|FxiOS\/([\d.]+)/i',
                'IE' => '/MSIE|Trident\/([\d.]+)/i',
                // Chrome check must come after Edge and other Chrome-based browsers
                'Chrome' => '/Chrome\/([\d.]+)|CriOS\/([\d.]+)/i',
                // Safari check must come last
                'Safari' => '/Version\/([\d.]+).*Safari\//i',
            ];

            foreach ($browsers as $name => $pattern) {
                if (preg_match($pattern, $this->userAgent)) {
                    // Special case for Edge to handle different UA formats
                    if ($name === 'Edge') {
                        if (preg_match('/Edg\/([\d.]+)/i', $this->userAgent)) return 'Edge';
                        if (preg_match('/Edge\/([\d.]+)/i', $this->userAgent)) return 'Edge (Legacy)';
                        if (preg_match('/EdgA\/([\d.]+)/i', $this->userAgent)) return 'Edge (Android)';
                        if (preg_match('/EdgiOS\/([\d.]+)/i', $this->userAgent)) return 'Edge (iOS)';
                    }
                    return $name;
                }
            }

            // Check config for additional browsers
            foreach ($this->config['browsers'] ?? [] as $browser => $pattern) {
                if (!isset($browsers[$browser]) && preg_match("/{$pattern}/i", $this->userAgent)) {
                    return $browser;
                }
            }

            // Fallback for Safari when no other browser is detected
            if (
                preg_match('/Safari/i', $this->userAgent) &&
                !preg_match('/Chrome|CriOS|Edg|Edge|OPR|Firefox|FxiOS/i', $this->userAgent)
            ) {
                return 'Safari';
            }

            return null;
        });
    }
    
    // ========================
    // Platform Detection
    // ========================

    /**
     * Get the platform/OS name
     *
     * @return string|null
     */
    public function platform(): ?string
    {
        return $this->cached('platform', function () {
            if (empty($this->userAgent)) {
                return null;
            }

            $platforms = [
                // iOS check needs to come before Mac since iOS user agents contain "Mac OS X" string
                'iOS' => '/iPhone|iPad|iPod/i',
                'Windows' => '/Windows NT|WinNT|Win32/i',
                'Mac' => '/Macintosh|Mac OS X|MacPPC|MacIntel/i',
                'Android' => '/Android/i',
                'Linux' => '/Linux|X11|Ubuntu|Debian|Fedora|CentOS|Red Hat|SUSE|Gentoo|Arch/i',
                'ChromeOS' => '/CrOS/i',
                'BlackBerry' => '/BlackBerry|BB10|RIM Tablet OS/i',
                'Unix' => '/Unix/i',
            ];

            foreach ($platforms as $name => $pattern) {
                if (preg_match($pattern, $this->userAgent)) {
                    return $name;
                }
            }

            // Check config for additional platforms
            foreach ($this->config['platforms'] ?? [] as $platform => $pattern) {
                if (!isset($platforms[$platform]) && preg_match("/{$pattern}/i", $this->userAgent)) {
                    return $platform;
                }
            }

            return null;
        });
    }

    // ========================
    // Version Detection
    // ========================

    /**
     * Get the browser version
     *
     * @return string|null
     */
    public function browserVersion(): ?string
    {
        return $this->cached('browserVersion', function () {
            if (empty($this->userAgent)) {
                return null;
            }

            $browser = $this->browser();
            $patterns = [
                'Brave' => '/Brave\/([\d.]+)/i',
                'Chrome' => '/Chrome\/([\d.]+)|CriOS\/([\d.]+)/i',
                'Edge' => '/Edg(?:e|A|IOS)?\/([\d.]+)/i',
                'Firefox' => '/Firefox\/([\d.]+)|FxiOS\/([\d.]+)/i',
                'IE' => '/MSIE ([\d.]+)|rv:([\d.]+)/i',
                'Opera' => '/Opera\/([\d.]+)|OPR\/([\d.]+)/i',
                'Safari' => '/Version\/([\d.]+)/i',
                'Samsung' => '/SamsungBrowser\/([\d.]+)/i',
                'UCBrowser' => '/UCBrowser\/([\d.]+)|UCWEB\/([\d.]+)/i',
                'Vivaldi' => '/Vivaldi\/([\d.]+)/i',
                'WeChat' => '/MicroMessenger\/([\d.]+)/i',
            ];

            if ($browser && isset($patterns[$browser]) && preg_match($patterns[$browser], $this->userAgent, $matches)) {
                return $matches[1] ?? $matches[2] ?? null;
            }
            return null;
        });
    }

    /**
     * Get the platform/OS version
     *
     * @return string|null
     */
    public function platformVersion(): ?string
    {
        return $this->cached('platformVersion', function () {
            if (empty($this->userAgent)) {
                return null;
            }

            $platform = $this->platform();
            $patterns = [
                'Android' => '/Android ([\d.]+)/i',
                'BlackBerry' => '/(?:BlackBerry|BB10).*Version\/([\d.]+)/i',
                'ChromeOS' => '/CrOS x86_64 ([\d.]+)/i',
                'iOS' => '/(?:iPhone|iPad|iPod)(?:.*OS )?([\d_\.]+)/i',
                'Mac' => '/Mac OS X ([\d_\.]+)/i',
                'Windows' => '/Windows NT ([\d.]+)/i',
                'Linux' => '/Linux ([\d.]+)|Ubuntu\/([\d.]+)|Debian\/([\d.]+)/i',
            ];

            if ($platform && isset($patterns[$platform]) && preg_match($patterns[$platform], $this->userAgent, $matches)) {
                if ($platform === 'iOS' || $platform === 'Mac') {
                    return str_replace('_', '.', $matches[1]);
                } elseif ($platform === 'Windows') {
                    return $this->mapWindowsVersion($matches[1]);
                } else {
                    return $matches[1] ?? $matches[2] ?? $matches[3] ?? null;
                }
            }

            return null;
        });
    }

    /**
     * Map Windows NT version to marketing name
     *
     * @param string $version Windows NT version
     * @return string|null Mapped Windows version
     */
    protected function mapWindowsVersion(string $version): ?string
    {
        if (!isset(self::WINDOWS_VERSIONS[$version])) {
            return $version;
        }

        if (is_array(self::WINDOWS_VERSIONS[$version])) {
            $config = self::WINDOWS_VERSIONS[$version];
            if (isset($config['win11_pattern']) && preg_match($config['win11_pattern'], $this->userAgent)) {
                return '11';
            }
            return $config['default'];
        }

        return self::WINDOWS_VERSIONS[$version];
    }

    // ========================
    // Device Brand & Model Detection
    // ========================

    /**
     * Get the device brand
     *
     * @return string|null
     */
    public function deviceBrand(): ?string
    {
        return $this->cached('deviceBrand', function () {
            if (empty($this->userAgent)) {
                return null;
            }

            $brands = [
                'Apple' => '/iPhone|iPad|iPod|Macintosh|Mac OS X|MacPPC|MacIntel/i',
                'Samsung' => '/SM-|GT-|SAMSUNG|Galaxy/i',
                'Google' => '/Pixel|Nexus/i',
                'Huawei' => '/HUAWEI|HONOR/i',
                'Xiaomi' => '/Redmi|Mi|POCO|Xiaomi/i',
                'OnePlus' => '/OnePlus/i',
                'Sony' => '/Xperia|Sony/i',
            ];

            foreach ($brands as $brand => $pattern) {
                if (preg_match($pattern, $this->userAgent)) {
                    return $brand;
                }
            }

            // Check config for additional brands
            foreach ($this->config['device_brands'] ?? [] as $brand => $pattern) {
                if (!isset($brands[$brand]) && preg_match("/{$pattern}/i", $this->userAgent)) {
                    return $brand;
                }
            }

            return null;
        });
    }

    /**
     * Get the device model
     *
     * @return string|null
     */
    public function deviceModel(): ?string
    {
        return $this->cached('deviceModel', function () {
            if (empty($this->userAgent)) {
                return null;
            }

            $brand = $this->deviceBrand();
            $model = null;

            switch ($brand) {
                case 'Apple':
                    if (preg_match('/iPhone(\d+,\d+)/i', $this->userAgent, $matches)) {
                        $modelId = $matches[1];
                        $model = 'iPhone ' . (self::APPLE_MODELS[$modelId] ?? $modelId);
                    } elseif (preg_match('/iPad(\d+,\d+)/i', $this->userAgent, $matches)) {
                        $modelId = $matches[1];
                        $model = 'iPad ' . (self::APPLE_MODELS[$modelId] ?? $modelId);
                    } elseif (preg_match('/iPod(\d+,\d+)/i', $this->userAgent, $matches)) {
                        $modelId = $matches[1];
                        $model = 'iPod ' . (self::APPLE_MODELS[$modelId] ?? $modelId);
                    } elseif (preg_match('/(Macintosh|MacBook|iMac|MacPro|Macmini|Mac Studio)/i', $this->userAgent, $matches)) {
                        $model = $matches[1];
                    }
                    break;

                case 'Samsung':
                    if (preg_match('/SM-([A-Z0-9]+)/i', $this->userAgent, $matches)) {
                        $model = 'SM-' . $matches[1];
                    } elseif (preg_match('/GT-([A-Z0-9]+)/i', $this->userAgent, $matches)) {
                        $model = 'GT-' . $matches[1];
                    } elseif (preg_match('/SAMSUNG-([A-Z0-9]+)/i', $this->userAgent, $matches)) {
                        $model = 'Samsung ' . $matches[1];
                    } elseif (preg_match('/Galaxy\s(Tab|S|Note|Z Fold|Z Flip)[\s\w\d-]+/i', $this->userAgent, $matches)) {
                        $model = 'Galaxy ' . trim($matches[0]);
                    }
                    break;

                case 'Google':
                    if (preg_match('/Pixel\s([\d\w]+)/i', $this->userAgent, $matches)) {
                        $model = 'Pixel ' . $matches[1];
                    } elseif (preg_match('/Nexus\s([\d]+)/i', $this->userAgent, $matches)) {
                        $model = 'Nexus ' . $matches[1];
                    }
                    break;

                case 'Huawei':
                    if (preg_match('/(HUAWEI|HONOR)\s([A-Z0-9-]+)/i', $this->userAgent, $matches)) {
                        $model = trim($matches[0]);
                    }
                    break;

                case 'Xiaomi':
                    if (preg_match('/(Redmi|Mi|POCO)\s([A-Z0-9\s]+)/i', $this->userAgent, $matches)) {
                        $model = trim($matches[0]);
                    }
                    break;

                case 'OnePlus':
                    if (preg_match('/OnePlus(?:A|IN)?([\d\w]+)/i', $this->userAgent, $matches)) {
                        $model = 'OnePlus ' . $matches[1];
                    }
                    break;

                case 'Sony':
                    if (preg_match('/Xperia\s([A-Z0-9]+)/i', $this->userAgent, $matches)) {
                        $model = 'Xperia ' . $matches[1];
                    }
                    break;
            }

            return $model;
        });
    }

    /**
     * Check if the user agent is a bot
     *
     * @return bool
     */
    public function isBot(): bool
    {
        return $this->cached('isBot', function () {
            if (empty($this->userAgent)) {
                return false;
            }

            $defaultBots = [
                'bot',
                'spider',
                'crawl',
                'slurp',
                'archiver',
                'facebook',
                'twitter',
                'google',
                'bing',
                'yandex',
                'baidu',
                'duckduckgo',
                'curl',
                'wget'
            ];
            
            $botPatterns = array_merge($defaultBots, $this->config['bots'] ?? []);
            return preg_match('/(' . implode('|', $botPatterns) . ')/i', $this->userAgent);
        });
    }

    // ========================
    // Language & Headers
    // ========================

    /**
     * Get the preferred languages from Accept-Language header
     *
     * @return array
     */
    public function languages(): array
    {
        return $this->cached('languages', function () {
            $header = $this->headers['HTTP_ACCEPT_LANGUAGE'] ?? '';
            $languages = [];
            
            if (empty($header)) {
                return [];
            }
            
            foreach (explode(',', $header) as $lang) {
                $parts = explode(';', $lang);
                $language = substr(trim($parts[0]), 0, 2);
                if ($language && !in_array(strtolower($language), $languages)) {
                    $languages[] = strtolower($language);
                }
            }
            return $languages;
        });
    }

    // ========================
    // Security Detection
    // ========================

    /**
     * Check if the browser is running in headless mode
     *
     * @return bool
     */
    public function isHeadlessChrome(): bool
    {
        return $this->cached('isHeadlessChrome', function () {
            if (empty($this->userAgent)) {
                return false;
            }
            
            return Str::contains($this->userAgent, 'HeadlessChrome') ||
                preg_match('/HeadlessChrome\/[\d.]+/i', $this->userAgent);
        });
    }

    /**
     * Check if the request is likely coming through a proxy
     *
     * @return bool
     */
    public function isProxy(): bool
    {
        return $this->cached('isProxy', function () {
            $proxyHeaders = [
                'HTTP_VIA',
                'HTTP_X_FORWARDED_FOR',
                'HTTP_FORWARDED_FOR',
                'HTTP_X_FORWARDED',
                'HTTP_FORWARDED',
                'HTTP_CLIENT_IP',
                'HTTP_X_PROXY_ID',
                'HTTP_PROXY_CONNECTION'
            ];
            
            foreach ($proxyHeaders as $header) {
                if (!empty($this->headers[$header])) {
                    return true;
                }
            }
            return false;
        });
    }

    // ========================
    // Utility Methods
    // ========================

    /**
     * Get all detection results as an array
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'browser' => [
                'name' => $this->browser(),
                'version' => $this->browserVersion(),
                'is_headless' => $this->isHeadlessChrome(),
            ],
            'platform' => [
                'name' => $this->platform(),
                'version' => $this->platformVersion(),
            ],
            'device' => [
                'type' => $this->deviceType(),
                'brand' => $this->deviceBrand(),
                'model' => $this->deviceModel(),
                'is_mobile' => $this->isMobile(),
                'is_tablet' => $this->isTablet(),
                'is_desktop' => $this->isDesktop(),
                'is_smartwatch' => $this->isSmartwatch(),
                'is_tv' => $this->isTV(),
            ],
            'is_bot' => $this->isBot(),
            'languages' => $this->languages(),
            'security' => [
                'is_proxy' => $this->isProxy(),
                'is_tor' => $this->isTor(),
                'is_vpn' => $this->isVPN(),
            ],
            'raw_user_agent' => $this->userAgent,
        ];
    }

    /**
     * Get all detection results as JSON
     *
     * @param int $options JSON encoding options
     * @return string
     */
    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * Pre-compute all detections
     *
     * @return self
     */
    public function analyze(): self
    {
        $this->toArray();
        return $this;
    }

    /**
     * Check if the request is coming from a Tor exit node
     *
     * @return bool
     */
    public function isTor(): bool
    {
        return $this->cached('isTor', function () {
            $knownTorExitNodePatterns = [
                '/^Mozilla\/5\.0 \(Windows NT 10\.0; rv:[\d.]+\) Gecko\/[\d]{8} Firefox\/[\d.]+$/i',
                '/^Mozilla\/5\.0 \(Android [\d.]+; Mobile; rv:[\d.]+\) Gecko\/[\d.]+$/i',
                '/^Mozilla\/5\.0 \(X11; Linux x86_64; rv:[\d.]+\) Gecko\/[\d]{8} Firefox\/[\d.]+$/i',
            ];
            
            // Check for Tor Browser fingerprints
            foreach ($knownTorExitNodePatterns as $pattern) {
                if (preg_match($pattern, $this->userAgent)) {
                    return true;
                }
            }
            
            // Check for common Tor exit node headers
            if (!empty($this->headers['HTTP_X_TOR'])) {
                return true;
            }
            
            return false;
        });
    }
    
    /**
     * Basic detection of VPN connections
     * Note: This is not foolproof and should be used with caution
     *
     * @return bool
     */
    public function isVPN(): bool
    {
        return $this->cached('isVPN', function () {
            // Common VPN provider domains in referer or origin
            $vpnDomains = [
                'nordvpn',
                'expressvpn',
                'vyprvpn',
                'protonvpn',
                'surfshark',
                'privateinternetaccess',
                'torguard',
                'cyberghost',
            ];
            
            $referer = $this->headers['HTTP_REFERER'] ?? '';
            $origin = $this->headers['HTTP_ORIGIN'] ?? '';
            
            foreach ($vpnDomains as $domain) {
                if (stripos($referer, $domain) !== false || stripos($origin, $domain) !== false) {
                    return true;
                }
            }
            
            // Check for potential VPN indicators
            if ($this->isProxy() && $this->isTor()) {
                return true;
            }
            
            return false;
        });
    }

    // ========================
    // Magic Methods
    // ========================

    /**
     * Magic method to provide is* methods for platform and brand detection
     *
     * @param string $name Method name
     * @param array $arguments Method arguments
     * @return bool
     * @throws BadMethodCallException
     */
    public function __call($name, $arguments)
    {
        if (str_starts_with($name, 'is')) {
            $property = strtolower(substr($name, 2));
            if (in_array($property, self::VALID_PROPERTIES)) {
                $platform = strtolower($this->platform() ?? '');
                $brand = strtolower($this->deviceBrand() ?? '');
                return $platform === $property || $brand === $property;
            }
        }
        throw new BadMethodCallException("Method {$name} does not exist");
    }

    // ========================
    // Internal Helpers
    // ========================

    /**
     * Get a cached value or compute and cache it
     *
     * @param string $key Cache key
     * @param callable $callback Callback to compute the value
     * @return mixed
     */
    protected function cached(string $key, callable $callback)
    {
        if (!($this->config['cache'] ?? true)) {
            return $callback();
        }
        if (!array_key_exists($key, $this->cache)) {
            $this->cache[$key] = $callback();
        }
        return $this->cache[$key];
    }

    /**
     * Get the device type
     *
     * @return string|null
     */
    public function deviceType(): ?string
    {
        return $this->cached('deviceType', function () {
            if ($this->isTV()) return 'TV';
            if ($this->isMobile()) return 'Mobile';
            if ($this->isTablet()) return 'Tablet';
            if ($this->isDesktop()) return 'Desktop';
            if ($this->isSmartwatch()) return 'SmartWatch';
            if ($this->isBot()) return 'Bot';
            return null;
        });
    }

    /**
     * Set a custom user agent string (useful for testing)
     *
     * @param string $userAgent User agent string
     * @return self
     */
    public function setUserAgent(string $userAgent): self
    {
        $this->userAgent = $userAgent;
        $this->cache = []; // Clear cache when user agent changes
        return $this;
    }
}
