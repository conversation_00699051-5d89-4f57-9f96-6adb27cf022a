<?php
// database/migrations/2025_07_09_090000_create_code_sequences_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration creates the 'code_sequences' table, which is essential
     * for the CodeGeneratorService. It stores the last used sequence number
     * for each unique type of code, ensuring that all generated codes are
     * unique and sequential.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('code_sequences', function (Blueprint $table) {
            // Standard auto-incrementing primary key.
            $table->id();

            // The unique key for the sequence. This is a combination of the
            // code's prefix and its location (e.g., "SUP_MH", "ORD_KA").
            // Using a single key is more flexible than multiple columns.
            $table->string('key')->unique();

            // The last sequence number that was successfully generated and
            // committed for the given key. UnsignedBigInteger allows for
            // a very large number of sequences.
            $table->unsignedBigInteger('sequence')->default(0);

            // Standard Laravel timestamps to track when the sequence record
            // was created or last updated.
            $table->timestamps();
            
            // Soft delete and restore functionality
            $table->softDeletes()->comment('Soft delete timestamp');
            $table->unsignedBigInteger('deleted_by')->nullable()->comment('User who soft deleted the record');
            $table->timestamp('restored_at')->nullable()->comment('Timestamp when the record was restored');
            $table->unsignedBigInteger('restored_by')->nullable()->comment('User who restored the record');
        });
    }

    /**
     * Reverse the migrations.
     *
     * This method is executed when rolling back the migration. It safely
     * drops the 'code_sequences' table from the database.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('code_sequences');
    }
};
