{{-- 1. Breadcrumb Component --}}
{{-- resources/views/components/breadcrumb.blade.php --}}
@props(['items' => []])

<nav aria-label="{{ __('Breadcrumb') }}" class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-2">
        @foreach($items as $index => $item)
        <li class="flex items-center">
            @if($index > 0)
            <flux:icon.chevron-right class="mx-2 h-4 w-4 text-zinc-400" />
            @endif

            @if($loop->last)
            <span class="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                {{ $item['label'] }}
            </span>
            @else
            <a href="{{ $item['url'] }}"
                class="text-sm font-medium text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-100 transition-colors"
                wire:navigate>
                {{ $item['label'] }}
            </a>
            @endif
        </li>
        @endforeach
    </ol>
</nav>