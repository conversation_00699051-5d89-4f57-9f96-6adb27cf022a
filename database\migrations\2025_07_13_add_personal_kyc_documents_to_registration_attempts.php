<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registration_attempts', function (Blueprint $table) {
            if (!Schema::hasColumn('registration_attempts', 'personal_kyc_documents')) {
                $table->json('personal_kyc_documents')->nullable()->comment('JSON data for personal KYC documents');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registration_attempts', function (Blueprint $table) {
            if (Schema::hasColumn('registration_attempts', 'personal_kyc_documents')) {
                $table->dropColumn('personal_kyc_documents');
            }
        });
    }
}; 