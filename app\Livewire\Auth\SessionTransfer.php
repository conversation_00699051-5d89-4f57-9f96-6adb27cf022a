<?php

namespace App\Livewire\Auth;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use App\Models\Auth\LoginHistory;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

#[Layout('components.layouts.auth')]
class SessionTransfer extends Component
{
    #[Validate('required|string')]
    public string $transferCode = '';
    
    public ?string $error = null;
    
    /**
     * Mount the component and check for code in query string
     */
    public function mount()
    {
        \Log::debug('=== SessionTransfer@mount START ===');
        \Log::debug('Current Session ID: ' . session()->getId());
        \Log::debug('Session Data: ' . json_encode(session()->all()));
        
        // Check if there's a code in the query string
        $code = request()->query('code');
        \Log::debug('Code from Query String: ' . ($code ?? 'null'));
        
        // Get code length from config
        $codeLength = config('session_transfer.code_length', 8);
        
        if ($code && strlen($code) === $codeLength) {
            $this->transferCode = $code;
            \Log::debug('Transfer Code Set from Query String: ' . $this->transferCode);
        }
        
        \Log::debug('=== SessionTransfer@mount END ===');
    }
    
    /**
     * Complete the transfer with just the code
     */
    public function validateTransferCode()
    {
        \Log::debug('=== SessionTransfer@validateTransferCode START ===');
        \Log::debug('Transfer Code: ' . $this->transferCode);
        \Log::debug('Current Session ID: ' . session()->getId());
        
        // Get configuration values
        $codeLength = config('session_transfer.code_length', 8);
        $maxAttempts = config('session_transfer.max_attempts', 3);
        $cachePrefix = config('session_transfer.cache_key_prefix', 'session_transfer:');
        $cacheStore = config('session_transfer.cache_store');
        
        $this->validate([
            'transferCode' => 'required|string|size:' . $codeLength,
        ]);
        \Log::debug('Validation Passed');
        
        // Get transfer information from cache
        $cacheKey = $cachePrefix . $this->transferCode;
        $transferData = $cacheStore ? 
            Cache::store($cacheStore)->get($cacheKey) : 
            Cache::get($cacheKey);
        
        \Log::debug('Transfer Data from Cache: ' . ($transferData ? json_encode($transferData) : 'null'));
        
        if (!$transferData) {
            $this->error = 'The transfer code is invalid or has expired.';
            \Log::debug('Error: ' . $this->error);
            \Log::debug('=== SessionTransfer@validateTransferCode END (with error) ===');
            return;
        }
        
        // Increment attempt counter
        $transferData['attempts'] = ($transferData['attempts'] ?? 0) + 1;
        \Log::debug('Attempt count: ' . $transferData['attempts']);
        
        // Check if max attempts exceeded
        if ($transferData['attempts'] > $maxAttempts) {
            // Delete the transfer code from cache
            if ($cacheStore) {
                Cache::store($cacheStore)->forget($cacheKey);
            } else {
                Cache::forget($cacheKey);
            }
            
            $this->error = 'Maximum number of attempts exceeded. Please generate a new transfer code.';
            \Log::debug('Error: ' . $this->error);
            \Log::debug('=== SessionTransfer@validateTransferCode END (with error) ===');
            return;
        }
        
        // Update the attempts count in cache
        if ($cacheStore) {
            Cache::store($cacheStore)->put(
                $cacheKey, 
                $transferData, 
                config('session_transfer.expiration_time', 600)
            );
        } else {
            Cache::put(
                $cacheKey, 
                $transferData, 
                config('session_transfer.expiration_time', 600)
            );
        }
        
        // Get the user
        $user = User::find($transferData['user_id']);
        \Log::debug('User Found: ' . ($user ? 'Yes (ID: ' . $user->id . ')' : 'No'));
        
        if (!$user) {
            $this->error = 'User not found. Please try again.';
            \Log::debug('Error: ' . $this->error);
            \Log::debug('=== SessionTransfer@validateTransferCode END (with error) ===');
            return;
        }
        
        \Log::debug('Original Session ID: ' . $transferData['session_id']);
        
        // Get the original session for device information
        $originalSession = LoginHistory::where('session_id', $transferData['session_id'])
            ->where('user_id', $user->id)
            ->first();
            
        if (!$originalSession) {
            $this->error = 'Original session not found. Please try again.';
            \Log::debug('Error: ' . $this->error);
            \Log::debug('=== SessionTransfer@validateTransferCode END (with error) ===');
            return;
        }
        
        // Check if the original session is already transferred
        if ($originalSession->is_transferred || $originalSession->transferred_to) {
            $this->error = 'This session has already been transferred to another device.';
            \Log::debug('Error: ' . $this->error);
            \Log::debug('=== SessionTransfer@validateTransferCode END (with error) ===');
            return;
        }
        
        // Log in the user without password
        Auth::login($user);
        \Log::debug('User Logged In: ' . Auth::id());
        
        // Set session context
        \Log::debug('Setting Session Context');
        \Log::debug('Role ID: ' . ($transferData['role_id'] ?? 'null'));
        \Log::debug('Entity ID: ' . ($transferData['entity_id'] ?? 'null'));
        
        app(\App\Services\LoginValidationService::class)->setSessionContext(
            $user, 
            $transferData['role_id'], 
            $transferData['entity_id']
        );
        \Log::debug('Session Context Set');
        \Log::debug('Updated Session Data: ' . json_encode(session()->all()));
        
        // Generate a new session ID for this transfer
        $newSessionId = Session::getId();
        \Log::debug('New Session ID: ' . $newSessionId);
        
        // Generate a new fingerprint for this device
        $components = [
            request()->ip(),
            request()->userAgent(),
            \App\Agent\Facades\Agent::browser(),
            \App\Agent\Facades\Agent::browserVersion(),
            \App\Agent\Facades\Agent::platform(),
            \App\Agent\Facades\Agent::platformVersion(),
            \App\Agent\Facades\Agent::deviceType()
        ];
        
        $newFingerprint = md5(implode('|', array_filter($components)));
        \Log::debug('New Device Fingerprint: ' . $newFingerprint);
        \Log::debug('Fingerprint Components: ' . json_encode(array_filter($components)));
        
        // Store the fingerprint in the session for consistency
        Session::put('device_fingerprint', $newFingerprint);
        
        // Copy device information from original session but use new fingerprint
        $deviceInfo = [
            'device_type' => \App\Agent\Facades\Agent::deviceType(),
            'device_brand' => \App\Agent\Facades\Agent::deviceBrand(),
            'device_model' => \App\Agent\Facades\Agent::deviceModel(),
            'device_fingerprint' => $newFingerprint,
            'platform' => \App\Agent\Facades\Agent::platform(),
            'platform_version' => \App\Agent\Facades\Agent::platformVersion(),
            'browser' => \App\Agent\Facades\Agent::browser(),
            'browser_version' => \App\Agent\Facades\Agent::browserVersion(),
            'fingerprint_changed' => false // Important: reset this flag
        ];
        
        // Record this as a new login with transfer information
        $loginData = [
            'user_id' => $user->id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => $newSessionId,
            'session_label' => 'Transferred from another device',
            'login_at' => now(),
            'login_method' => 'transfer',
            'login_successful' => true,
            'transferred_from' => $transferData['session_id'],
            'guard' => config('auth.defaults.guard'),
            'role_id' => $transferData['role_id'],
            'entity_id' => $transferData['entity_id'],
            'department_id' => $transferData['department_id'],
            'is_transferred' => false, // This is a new session created by transfer
            // Preserve cross-entity access information
            'is_cross_entity_access' => $transferData['is_cross_entity_access'] ?? false,
            'original_entity_id' => $transferData['original_entity_id'] ?? null,
            'original_role_id' => $transferData['original_role_id'] ?? null,
        ];
        
        // Merge device information
        $loginData = array_merge($loginData, $deviceInfo);
        
        \Log::debug('Login Data: ' . json_encode($loginData));
        
        // Create the login record for this new session
        $newLoginRecord = LoginHistory::create($loginData);
        \Log::debug('New Login Record Created: ' . json_encode($newLoginRecord->toArray()));
        
        // Terminate the original session
        $originalSessionQuery = LoginHistory::where('session_id', $transferData['session_id'])
            ->where('user_id', $user->id)
            ->whereNull('logout_at');
            
        \Log::debug('Original Session Query: ' . $originalSessionQuery->toSql());
        \Log::debug('Original Session Query Bindings: ' . json_encode($originalSessionQuery->getBindings()));
        
        $originalSession = $originalSessionQuery->first();
        \Log::debug('Original Session Found: ' . ($originalSession ? 'Yes' : 'No'));
        
        if ($originalSession) {
            \Log::debug('Original Session Before Update: ' . json_encode($originalSession->toArray()));
            
            // Calculate duration
            $loginTime = $originalSession->login_at;
            $now = now();
            $duration = max(0, $now->timestamp - $loginTime->timestamp);
            
            \Log::debug('Login Time: ' . $loginTime);
            \Log::debug('Now: ' . $now);
            \Log::debug('Duration: ' . $duration);
            
            $originalSession->update([
                'logout_at' => $now,
                'logout_reason' => 'transferred_to_another_device',
                'transferred_to' => $newSessionId,
                'duration_seconds' => $duration,
                'is_transferred' => true
            ]);
            
            \Log::debug('Original Session After Update: ' . json_encode($originalSession->fresh()->toArray()));
            
            // IMPORTANT FIX: Delete the original session from the Laravel sessions table
            // This ensures the old browser session is invalidated
            DB::table('sessions')
                ->where('id', $transferData['session_id'])
                ->delete();
            
            \Log::debug('Original Laravel session record deleted');
        } else {
            \Log::debug('Warning: Original session not found for termination');
            
            // Try to update by direct query
            $updateResult = LoginHistory::where('session_id', $transferData['session_id'])
                ->where('user_id', $user->id)
                ->whereNull('logout_at')
                ->update([
                    'logout_at' => now(),
                    'logout_reason' => 'transferred_to_another_device',
                    'transferred_to' => $newSessionId,
                    'duration_seconds' => max(0, now()->timestamp - Carbon::parse($transferData['created_at'])->timestamp),
                    'is_transferred' => true
                ]);
                
            \Log::debug('Direct Update Result: ' . $updateResult . ' rows affected');
            
            // IMPORTANT FIX: Delete the original session from the Laravel sessions table
            // This ensures the old browser session is invalidated even if we couldn't find the login history
            DB::table('sessions')
                ->where('id', $transferData['session_id'])
                ->delete();
            
            \Log::debug('Original Laravel session record deleted');
        }
        
        // Store the original session info in the current session
        Session::put('transferred_from_session', $transferData['session_id']);
        Session::put('transfer_completed_at', now()->toDateTimeString());
        
        // Copy device fingerprint to session for consistency
        Session::put('device_fingerprint', $newFingerprint);
        Session::put('is_transferred_session', true);
        
        // Preserve cross-entity access information in session
        if ($transferData['is_cross_entity_access'] ?? false) {
            Session::put('is_super_admin', true);
            Session::put('original_entity_id', $transferData['original_entity_id']);
            \Log::debug('Cross-entity access preserved in transferred session');
            \Log::debug('Set is_super_admin = true in session');
            \Log::debug('Set original_entity_id = ' . $transferData['original_entity_id'] . ' in session');
        }
        
        \Log::debug('Session Updated with Transfer Info');
        
        // Remove the transfer code from cache
        if ($cacheStore) {
            Cache::store($cacheStore)->forget($cachePrefix . $this->transferCode);
        } else {
            Cache::forget($cachePrefix . $this->transferCode);
        }
        \Log::debug('Transfer Code Removed from Cache');
        
        \Log::debug('=== SessionTransfer@validateTransferCode END ===');
        
        // Redirect to dashboard
        return $this->redirect(route('dashboard'), navigate: true);
    }
    
    /**
     * Render the component
     */
    public function render()
    {
        \Log::debug('=== SessionTransfer@render ===');
        \Log::debug('Transfer Code: ' . $this->transferCode);
        \Log::debug('Error: ' . ($this->error ?? 'null'));
        
        return view('livewire.auth.session-transfer');
    }
} 