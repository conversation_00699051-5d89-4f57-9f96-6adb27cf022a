@php
    $heading = $heading ?? __('Manage Roles');
@endphp

<div class="space-y-6">
    <!-- User Information -->
    <div class="bg-zinc-50 dark:bg-zinc-800 rounded-lg p-6">
        <div class="flex items-center">
            <div class="h-12 w-12 flex-shrink-0">
                <div class="h-12 w-12 rounded-full bg-zinc-300 dark:bg-zinc-600 flex items-center justify-center">
                    <flux:icon name="user-circle" class="h-8 w-8 text-zinc-500 dark:text-zinc-400" />
                </div>
            </div>
            <div class="ml-4">
                <flux:heading size="lg">{{ $user->name }}</flux:heading>
                <flux:text class="text-zinc-500">{{ $user->email }}</flux:text>
            </div>
        </div>
    </div>

    <!-- Current Role Assignments -->
    <flux:fieldset>
        <flux:legend>{{ __('Current Role Assignments') }}</flux:legend>
        
        @if($currentAssignments->count() > 0)
            <div class="space-y-3">
                @foreach($currentAssignments as $assignment)
                    <div class="flex items-center justify-between p-4 bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 rounded-lg">
                        <div class="flex items-center gap-4">
                            <x-admin.role-badge :role="$assignment->role" />
                            <div>
                                <flux:text class="text-sm">
                                    {{ __('Assigned: :date', ['date' => $assignment->assigned_from->format('M j, Y')]) }}
                                </flux:text>
                                @if($assignment->assigned_until)
                                    <flux:text class="text-sm text-zinc-500">
                                        {{ __('Expires: :date', ['date' => $assignment->assigned_until->format('M j, Y')]) }}
                                    </flux:text>
                                @endif
                            </div>
                        </div>
                        <flux:button 
                            variant="ghost" 
                            size="sm" 
                            color="danger"
                            wire:click="revokeAssignment({{ $assignment->id }})"
                            wire:confirm="{{ __('Are you sure you want to revoke this role assignment?') }}">
                            {{ __('Revoke') }}
                        </flux:button>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <flux:icon name="shield-exclamation" class="h-12 w-12 text-zinc-400 mx-auto mb-4" />
                <flux:text class="text-zinc-500">{{ __('No active role assignments') }}</flux:text>
            </div>
        @endif
    </flux:fieldset>

    <!-- Assign New Role -->
    <form wire:submit="assignRole">
        <flux:fieldset>
            <flux:legend>{{ __('Assign New Role') }}</flux:legend>

            <div class="grid gap-6 md:grid-cols-3">
                <flux:field>
                    <flux:label>{{ __('Role') }}</flux:label>
                    <flux:select wire:model="role_id" placeholder="{{ __('Select a role') }}">
                        @foreach($availableRoles as $role)
                            <flux:option value="{{ $role->id }}">
                                {{ $role->role_name }} ({{ __('Level :level', ['level' => $role->hierarchy_level]) }})
                            </flux:option>
                        @endforeach
                    </flux:select>
                    <flux:error name="role_id" />
                </flux:field>

                <flux:field>
                    <flux:label>{{ __('Assigned From') }}</flux:label>
                    <flux:input type="date" wire:model="assigned_from" />
                    <flux:error name="assigned_from" />
                </flux:field>

                <flux:field>
                    <flux:label>{{ __('Assigned Until') }}</flux:label>
                    <flux:input type="date" wire:model="assigned_until" />
                    <flux:description>{{ __('Leave empty for no expiration') }}</flux:description>
                    <flux:error name="assigned_until" />
                </flux:field>
            </div>

            <div class="flex justify-end">
                <flux:button type="submit" variant="primary">
                    {{ __('Assign Role') }}
                </flux:button>
            </div>
        </flux:fieldset>
    </form>

    <!-- Back to Users -->
    <div class="flex justify-start pt-6 border-t border-zinc-200 dark:border-zinc-700">
        <flux:button 
            variant="ghost" 
            href="{{ route('admin.rbac.users.index') }}"
            wire:navigate>
            <flux:icon name="arrow-left" class="h-4 w-4" />
            {{ __('Back to Users') }}
        </flux:button>
    </div>
</div>
