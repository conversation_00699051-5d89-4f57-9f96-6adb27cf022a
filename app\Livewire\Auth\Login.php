<?php

namespace App\Livewire\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;
use App\Services\LoginTrackingService;
use App\Services\LoginValidationService;
use App\Models\Auth\LoginHistory;
use App\Models\Entity\Entity;
use App\Models\Rbac\SystemRole;
use App\Models\User;
use Carbon\Carbon;

#[Layout('components.layouts.auth')]
class Login extends Component
{
    #[Validate('required|string|email')]
    public string $email = '';

    #[Validate('required|string')]
    public string $password = '';

    public bool $remember = false;
    public ?string $sessionLabel = null;
    public ?string $entityId = null;
    public ?int $roleId = null;
    
    // For Super Admin entity selection
    public bool $showEntitySelector = false;
    public array $availableEntities = [];
    public array $availableRoles = [];
    public bool $skipEntitySelection = false;
    
    protected LoginTrackingService $loginTracker;
    protected LoginValidationService $loginValidator;
    
    public function boot(LoginTrackingService $loginTracker, LoginValidationService $loginValidator)
    {
        $this->loginTracker = $loginTracker;
        $this->loginValidator = $loginValidator;
    }

    /**
     * Handle an incoming authentication request.
     * 
     * @return mixed
     */
    public function login()
    {
        $this->validate();

        $this->ensureIsNotRateLimited();
        
        try {
            $user = User::where('email', $this->email)->first();
            
            if (!Auth::attempt(['email' => $this->email, 'password' => $this->password], $this->remember)) {
                RateLimiter::hit($this->throttleKey());
                
                // Log failed login attempt
                $this->loginTracker->recordFailedLogin(
                    request(),
                    $this->email,
                    'Invalid credentials',
                    $user?->id
                );
                
                throw ValidationException::withMessages([
                    'email' => __('auth.failed'),
                ]);
            }
            
            // Get authenticated user
            $user = Auth::user();
            
            // Check if this is a Super Administrator
            $isSuperAdmin = $this->loginValidator->isSuperAdmin($user);
            
            // If Super Admin and no entity is selected, show entity selector with skip option
            if ($isSuperAdmin && !$this->entityId && !$this->skipEntitySelection) {
                // Load available entities
                $this->loadEntitiesForSuperAdmin();
                $this->showEntitySelector = true;
                
                // Keep the user logged in but don't redirect yet
                return null;
            }
            
            // Run comprehensive login validation
            $validationResult = $this->loginValidator->validateLogin($user);
            
            if ($validationResult !== true) {
                Auth::logout();
                Session::invalidate();
                Session::regenerateToken();
                
                // Log failed login attempt with correct reason
                $this->loginTracker->recordFailedLogin(
                    request(),
                    $this->email,
                    $validationResult['error'],
                    $user->id
                );
                
                // Check if we need to redirect to a specific route
                if (isset($validationResult['redirect'])) {
                    return $this->redirect(route($validationResult['redirect']), navigate: true);
                }
                
                throw ValidationException::withMessages([
                    'email' => __($validationResult['message']),
                ]);
            }
            
            // Clear rate limiting
            RateLimiter::clear($this->throttleKey());
            
            // Regenerate session for security
            Session::regenerate();
            
            // Determine if this is cross-entity access
            $isCrossEntityAccess = $isSuperAdmin && $this->entityId && $this->entityId != $user->entity_id;
            $originalEntityId = $isCrossEntityAccess ? $user->entity_id : null;
            $originalRoleId = $isCrossEntityAccess ? null : null; // We'll set this if we have a specific role
            
            // Set session context (role, department, guard, entity)
            $this->loginValidator->setSessionContext(
                $user, 
                $this->roleId, 
                $this->entityId, 
                $this->sessionLabel
            );
            
            // Record successful login with cross-entity info if applicable
            $loginRecord = $this->loginTracker->recordSuccessfulLogin(
                request(), 
                $user->id,
                $this->sessionLabel,
                $isCrossEntityAccess,
                $originalEntityId,
                $originalRoleId
            );
            
            // Update login record with role_id if available
            if (Session::has('active_role_id')) {
                $loginRecord->update([
                    'role_id' => Session::get('active_role_id'),
                    'department_id' => Session::get('active_department_id'),
                    'entity_id' => Session::get('active_entity_id')
                ]);
            }
            
            // Check if we need to enforce multi-login limits
            $activeSessions = LoginHistory::activeSessions()
                ->where('user_id', $user->id)
                ->count();
            
            if ($activeSessions > $user->multi_login && $user->multi_login > 0) {
                // Will be caught by the CheckActiveSessions middleware
                return $this->redirect(route('auth.session-management'), navigate: true);
            } else {
                return $this->redirect(route('dashboard'), navigate: true);
            }
        } catch (ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            // Log any unexpected errors
            report($e);
            
            // Log the failed login attempt
            $this->loginTracker->recordFailedLogin(
                request(),
                $this->email,
                'System error: ' . $e->getMessage()
            );
            
            throw ValidationException::withMessages([
                'email' => __('An unexpected error occurred. Please try again later.'),
            ]);
        }
    }
    
    /**
     * Load available entities for Super Admin.
     */
    public function loadEntitiesForSuperAdmin()
    {
        $entities = Entity::where('is_active', true)
            ->where(function($query) {
                $query->where('is_approval_required', false)
                    ->orWhere('approval_status', 'approved');
            })
            ->get(['entity_id', 'entity_name', 'entity_type']);
            
        $this->availableEntities = $entities->toArray();
    }
    
    /**
     * Load available roles for the selected entity.
     */
    public function loadRolesForEntity()
    {
        if (!$this->entityId) {
            $this->availableRoles = [];
            return;
        }
        
        $roles = SystemRole::where('is_active', true)
            ->where(function($query) {
                $query->where('is_approval_required', false)
                    ->orWhere('approval_status', 'approved');
            })
            ->where(function($query) {
                $query->where('entity_id', $this->entityId)
                    ->orWhereNull('entity_id');
            })
            ->get(['id', 'role_name', 'hierarchy_level']);
            
        $this->availableRoles = $roles->toArray();
    }
    
    /**
     * Handle entity selection.
     */
    public function selectEntity()
    {
        $this->validate([
            'entityId' => 'required|string|exists:entities,entity_id',
        ]);
        
        // Load roles for this entity
        $this->loadRolesForEntity();
    }
    
    /**
     * Continue login after entity/role selection.
     */
    public function continueLogin()
    {
        // Validate entity and role
        $this->validate([
            'entityId' => 'required|string|exists:entities,entity_id',
            'roleId' => 'nullable|integer|exists:system_roles,id',
        ]);
        
        // Continue with login process
        $this->login();
    }

    /**
     * Skip entity selection and continue as Super Administrator
     */
    public function skipAndContinue()
    {
        $this->skipEntitySelection = true;
        $this->entityId = null;
        $this->roleId = null;
        $this->showEntitySelector = false;
        
        // Continue with login process
        $this->login();
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->email) . '|' . request()->ip());
    }
    
    /**
     * Render the component.
     */
    public function render()
    {
        return view('livewire.auth.login', [
            'showEntitySelector' => $this->showEntitySelector,
            'availableEntities' => $this->availableEntities,
            'availableRoles' => $this->availableRoles,
        ]);
    }
}
