<?php

namespace App\Livewire\Admin\Rbac\Roles;

use Livewire\Component;
use App\Models\Rbac\SystemRole;
use App\Models\Rbac\OrganizationDepartment;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;
use Livewire\Attributes\Layout;

#[Layout('components.admin.layout')]
class RoleCreate extends Component
{
    #[Validate('required|string|max:255')]
    public $role_name = '';

    #[Validate('nullable|string|max:1000')]
    public $description = '';

    #[Validate('nullable|exists:system_roles,id')]
    public $parent_role_id = null;

    #[Validate('required|integer|min:3|max:10')]
    public $hierarchy_level = 3;

    #[Validate('required|in:web,api,mobile')]
    public $guard_type = 'web';

    #[Validate('nullable|date')]
    public $active_from = null;

    #[Validate('nullable|date|after:active_from')]
    public $active_until = null;

    public function mount()
    {
        // Set default active_from to today
        $this->active_from = now()->toDateString();
    }

    public function getAvailableParentRolesProperty()
    {
        $currentUserLevel = session('active_role_level', 999);
        
        return SystemRole::where('entity_id', Auth::user()->entity_id)
            ->where('hierarchy_level', '<', $this->hierarchy_level)
            ->where('hierarchy_level', '>', $currentUserLevel)
            ->operational()
            ->orderBy('hierarchy_level')
            ->get();
    }

    public function save()
    {
        $this->validate();

        // Additional business logic validation
        $this->validateHierarchyLevel();

        SystemRole::create([
            'entity_id' => Auth::user()->entity_id,
            'role_name' => $this->role_name,
            'description' => $this->description,
            'parent_role_id' => $this->parent_role_id,
            'hierarchy_level' => $this->hierarchy_level,
            'guard_type' => $this->guard_type,
            'active_from' => $this->active_from,
            'active_until' => $this->active_until,
            'is_active' => false, // Requires approval
            'approval_status' => 'pending',
            'created_by' => Auth::id(),
        ]);

        session()->flash('success', __('Role created successfully and is pending approval.'));
        
        return $this->redirect(route('admin.rbac.roles.index'), navigate: true);
    }

    private function validateHierarchyLevel()
    {
        $currentUserLevel = session('active_role_level', 999);
        
        if ($this->hierarchy_level <= $currentUserLevel) {
            $this->addError('hierarchy_level', 
                __('You can only create roles with lower privilege levels than your own.'));
        }
    }

    public function render()
    {
        return view('livewire.admin.rbac.roles.role-create', [
            'availableParentRoles' => $this->availableParentRoles
        ])->with([
            'heading' => __('Create New Role'),
            'subheading' => __('Define a new system role with specific permissions'),
            'breadcrumbs' => [
                ['label' => __('Dashboard'), 'url' => route('dashboard')],
                ['label' => __('Admin'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Roles'), 'url' => route('admin.rbac.roles.index')],
                ['label' => __('Create'), 'url' => route('admin.rbac.roles.create')]
            ]
        ]);
    }
}
