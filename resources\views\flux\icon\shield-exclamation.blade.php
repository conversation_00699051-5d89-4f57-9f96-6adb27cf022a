@props([
    'variant' => 'outline',
])

@php
$sizeClasses = match($variant) {
    'outline' => 'h-6 w-6',
    'solid' => 'h-6 w-6',
    'mini' => 'h-5 w-5',
    'micro' => 'h-4 w-4',
    default => 'h-6 w-6',
};

$classes = "shrink-0 $sizeClasses";
@endphp

<svg {{ $attributes->merge(['class' => $classes]) }} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285ZM12 15.75h.007v.008H12v-.008Z"/>
</svg>
