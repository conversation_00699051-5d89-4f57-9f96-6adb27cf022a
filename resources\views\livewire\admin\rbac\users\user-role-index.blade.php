@php
    $heading = $heading ?? __('User Role Assignments');
@endphp
<div class="space-y-6">
    <!-- Header Actions -->
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
            <!-- Search -->
            <flux:input 
                wire:model.live.debounce.300ms="search"
                placeholder="{{ __('Search users...') }}"
                class="w-64">
                <x-slot name="iconTrailing">
                    <flux:icon name="magnifying-glass" class="h-4 w-4" />
                </x-slot>
            </flux:input>

            <!-- Filters -->
            <flux:select wire:model.live="filterRole" placeholder="{{ __('All Roles') }}">
                @foreach($availableRoles as $role)
                    <flux:option value="{{ $role->id }}">{{ $role->role_name }}</flux:option>
                @endforeach
            </flux:select>

            <flux:select wire:model.live="filterStatus" placeholder="{{ __('All Status') }}">
                <flux:option value="active">{{ __('Has Active Roles') }}</flux:option>
                <flux:option value="inactive">{{ __('No Active Roles') }}</flux:option>
            </flux:select>
        </div>
    </div>

    <!-- Users Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-700">
        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
            <thead class="bg-zinc-50 dark:bg-zinc-800">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('User') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Current Roles') }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Status') }}
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-zinc-500 uppercase tracking-wider">
                        {{ __('Actions') }}
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-zinc-900 divide-y divide-zinc-200 dark:divide-zinc-700">
                @forelse($users as $user)
                <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-800">
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="h-10 w-10 flex-shrink-0">
                                <div class="h-10 w-10 rounded-full bg-zinc-300 dark:bg-zinc-600 flex items-center justify-center">
                                    <flux:icon name="user" class="h-5 w-5 text-zinc-500 dark:text-zinc-400" />
                                </div>
                            </div>
                            <div class="ml-4">
                                <flux:text class="font-medium">{{ $user->name }}</flux:text>
                                <flux:text class="text-sm text-zinc-500">{{ $user->email }}</flux:text>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex flex-wrap gap-1">
                            {{-- Temporarily show placeholder until relationship is fixed --}}
                            <flux:text class="text-sm text-zinc-500">{{ __('Role assignments loading...') }}</flux:text>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <flux:badge variant="solid" color="info">{{ __('Checking...') }}</flux:badge>
                    </td>
                    <td class="px-6 py-4 text-right">
                        <div class="flex items-center justify-end gap-2">
                            <flux:button 
                                variant="ghost" 
                                size="sm"
                                href="{{ route('admin.rbac.users.assign', $user) }}"
                                wire:navigate>
                                {{ __('Manage Roles') }}
                            </flux:button>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="4" class="px-6 py-12 text-center">
                        <flux:text class="text-zinc-500">{{ __('No users found.') }}</flux:text>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $users->links() }}
    </div>
</div>
